<template>
  <div class="timing-log-example">
    <h2>定时任务日志组件示例</h2>
    
    <!-- 使用示例 -->
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>基本使用</span>
        </div>
      </template>
      
      <div class="example-content">
        <el-button type="primary" @click="showLogDialog">
          查看任务执行日志
        </el-button>
        
        <!-- 日志组件 -->
        <TimingLog v-model="logDialogVisible" />
      </div>
    </el-card>
    
    <!-- 功能说明 -->
    <el-card class="feature-card">
      <template #header>
        <div class="card-header">
          <span>功能特性</span>
        </div>
      </template>
      
      <div class="feature-content">
        <h3>主要功能</h3>
        <ul>
          <li>📊 <strong>任务日志查询</strong>：支持按任务名称、时间范围、执行状态筛选</li>
          <li>📋 <strong>详细信息展示</strong>：显示任务名称、Bean名称、执行方法、参数等</li>
          <li>⚠️ <strong>异常详情查看</strong>：支持查看执行失败的详细异常信息</li>
          <li>📤 <strong>数据导出</strong>：支持导出日志数据为Excel文件</li>
          <li>📄 <strong>分页显示</strong>：支持分页浏览大量日志数据</li>
        </ul>
        
        <h3>技术特性</h3>
        <ul>
          <li>✅ <strong>Vue 3 Composition API</strong>：使用最新的Vue3语法</li>
          <li>✅ <strong>Element Plus</strong>：基于Element Plus UI组件库</li>
          <li>✅ <strong>useCrud Hook</strong>：使用现代化的数据管理方式</li>
          <li>✅ <strong>TypeScript支持</strong>：完整的类型定义</li>
          <li>✅ <strong>响应式设计</strong>：自适应不同屏幕尺寸</li>
        </ul>
        
        <h3>使用方法</h3>
        <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;el-button @click="showLog"&gt;查看日志&lt;/el-button&gt;
    &lt;TimingLog v-model="logVisible" /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import TimingLog from './log.vue'

const logVisible = ref(false)

const showLog = () =&gt; {
  logVisible.value = true
}
&lt;/script&gt;</code></pre>
      </div>
    </el-card>
    
    <!-- API文档 -->
    <el-card class="api-card">
      <template #header>
        <div class="card-header">
          <span>API 文档</span>
        </div>
      </template>
      
      <div class="api-content">
        <h3>Props</h3>
        <el-table :data="propsData" border>
          <el-table-column prop="name" label="参数名" width="150" />
          <el-table-column prop="type" label="类型" width="100" />
          <el-table-column prop="default" label="默认值" width="100" />
          <el-table-column prop="description" label="说明" />
        </el-table>
        
        <h3>Events</h3>
        <el-table :data="eventsData" border>
          <el-table-column prop="name" label="事件名" width="150" />
          <el-table-column prop="params" label="参数" width="200" />
          <el-table-column prop="description" label="说明" />
        </el-table>
        
        <h3>Methods</h3>
        <el-table :data="methodsData" border>
          <el-table-column prop="name" label="方法名" width="150" />
          <el-table-column prop="params" label="参数" width="200" />
          <el-table-column prop="description" label="说明" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TimingLog from './log.vue'

// 响应式数据
const logDialogVisible = ref(false)

// 方法
const showLogDialog = () => {
  logDialogVisible.value = true
}

// API文档数据
const propsData = ref([
  {
    name: 'modelValue',
    type: 'Boolean',
    default: 'false',
    description: '控制弹窗显示/隐藏，支持v-model'
  }
])

const eventsData = ref([
  {
    name: 'update:modelValue',
    params: '(visible: Boolean)',
    description: '弹窗显示状态变化时触发'
  }
])

const methodsData = ref([
  {
    name: 'doInit',
    params: '()',
    description: '初始化并加载日志数据'
  }
])
</script>

<style lang="scss" scoped>
.timing-log-example {
  padding: 20px;
  
  .example-card,
  .feature-card,
  .api-card {
    margin-bottom: 20px;
    
    .card-header {
      font-weight: bold;
      color: #303133;
    }
  }
  
  .example-content {
    text-align: center;
    padding: 20px;
  }
  
  .feature-content {
    h3 {
      color: #409EFF;
      margin: 20px 0 10px 0;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 5px;
    }
    
    ul {
      margin: 10px 0;
      padding-left: 20px;
      
      li {
        margin: 8px 0;
        line-height: 1.6;
      }
    }
    
    pre {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      border-left: 4px solid #409EFF;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.4;
      }
    }
  }
  
  .api-content {
    h3 {
      color: #409EFF;
      margin: 20px 0 15px 0;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 5px;
    }
    
    .el-table {
      margin-bottom: 30px;
    }
  }
}
</style>
