<template>
  <div class="app-container">

    <div class="flow">
      <div class="content">
        <!--左侧工具栏-->
        <div id="stencil" />
        <div class="panel">
          
          <!--流程图画板-->
          <main-board />
        </div>
        <!--右侧工具栏-->
        <div class="config"  v-if="selectedNode" >
          <!-- <config-panel v-if="isReady" /> -->
          <div>
          <h3>节点属性</h3>
          <label>
            标签:
            <input v-model="nodeLabel" @input="updateNodeLabel" />
          </label>
          <!-- 添加更多属性编辑项 -->
        </div>
        </div>
      </div>
    </div>

  </div>

</template>

<script>
import { MainBoard } from './MainBoard.vue'

export default {
  name: 'Dashboard',
  dicts: ['area_status'],
  data() {
    return {
      modules: [],
      permission: {
        add: ['admin', 'areaInfo:add'],
        edit: ['admin', 'areaInfo:edit'],
        del: ['admin', 'areaInfo:del']
      },
      rules: {
        tenantId: [
          { required: true, message: '租户ID不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'areaId', display_name: '区域ID' },
        { key: 'areaName', display_name: '区域名称' },
        { key: 'status', display_name: '区域状态: 1:有效, 0:失效' }
      ],
      methodGroups: [
        {
          name: '方法列表',
          collapsable: true
        },
        {
          name: '步骤列表',
          collapsable: true
        },
        {
          name: '动作列表',
          collapsable: true
        },
        {
          name: '指令列表',
          collapsable: true
        }

      ],
      graph: null,
      stencil: null,
      selectedNode: null,
      nodeLabel: '',
      updateNodeLabel: ''
    }
  },
  mounted() {
    
  },
  methods: {

  }

}

</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .flow {
   /* width: 100vw; */
    width: 100%;
    height: 100vh;
  }

  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  #stencil {
    width: 290px;
    height: 100%;
    position: relative;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
  }

  .panel {
    width: calc(100% - 580px);
    height: 100%;
  }

  .panel .toolbar {
    width: 100%;
    height: 38px;
    display: flex;
    align-items: center;
    background-color: #f7f9fb;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  .panel #container {
    width: 100%;
    height: calc(100% - 38px);
  }

  .config {
    width: 290px;
    height: 100%;
    padding: 0 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
  }

  .node_icon {
    color: darkcyan;
  }
</style>
