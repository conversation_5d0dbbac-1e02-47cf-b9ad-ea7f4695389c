import request from '@/utils/request'

export function queryDeviceInstanceById(params) {
  return request({
    url: 'api/deviceInstance/queryDeviceInstanceById',
    method: 'get',
    params
  })
}

export function updatePositionDetailConfig(data) {
  return request({
    url: 'api/deviceInstance/updatePositionDetailConfig',
    method: 'put',
    data
  })
}

export default { queryDeviceInstanceById, updatePositionDetailConfig }
