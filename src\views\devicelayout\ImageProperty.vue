<template>
  <div class="property-container">
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-divider content-position="left">基本信息</el-divider>
      <el-form-item label="背景色" prop="backgroundColor">
        <el-color-picker
          v-model="form.backgroundColor"
          :predefine="colorPresets"
          show-alpha
          color-format	="hex"
          popper-class="theme-picker-dropdown"
        />
      </el-form-item>
      <el-form-item label="线条颜色" prop="strokeColor">
        <el-color-picker
          v-model="form.strokeColor"
          :predefine="colorPresets"
          show-alpha
          color-format	="hex"
          popper-class="theme-picker-dropdown"
        />
      </el-form-item>
      <el-form-item label="线条宽度" prop="strokeWidth">
        <el-input-number
          v-model="form.strokeWidth"
          :min="1"
          :max="10"
          size="small"
          controls-position="right"
        />
      </el-form-item>
      <el-form-item label="图像" prop="image">
        <upload-image-selector v-model="form.image" />
      </el-form-item>
      <el-form-item label="zIndex" prop="zIndex">
        <el-input v-model="form.zIndex" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { Graph, Node } from '@antv/x6'
import { debounce } from '@/utils'
import { useStore } from 'vuex'

import UploadImageSelector from '@/views/components/UploadImageSelector.vue'

// Props
const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  },
  graph: {
    default: () => ({}),
    type: Graph
  },
  dataStore: {
    default: () => ({}),
    type: Object
  }
})

// Emits
const emit = defineEmits(["update:modelValue"]);

const store = useStore()

// 计算属性
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)
// 颜色预设
const colorPresets = [
  "#4080FF",
  "#ff4500",
  "#ff8c00",
  "#90ee90",
  "#00ced1",
  "#1e90ff",
  "#c71585",
  "rgba(255, 69, 0, 0.68)",
  "rgb(255, 120, 0)",
  "hsva(120, 40, 94)",
];

// 响应式数据
const defaultForm = { id: null, backgroundColor: null,strokeColor: null,strokeWidth: 1, image:null, description: null, positions: [], type: null, configTemplate: null, javaClassName: null, configJavaClassName: null, layoutImage: null, layoutWidth: null, layoutHeight: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }
const form = reactive({ ...defaultForm })
const rules = reactive({
  name: [
    { required: true, message: '设备实例名称', trigger: 'blur' }
  ]
})
const currentSelectedPosIndex = ref(-1)
const initing = ref(false)
const formRef = ref(null)
const tableRef = ref(null)

const changeThemeColor = (val) => {
  //form.backgroundColor = val
  emit('update:modelValue', val)
}

// 方法
const getCurDeviceInstance = () => {
  const curNode = props.curNode
  const { graph } = curNode.model || {}

  if (!graph) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === curNode.id) {
      return ins
    }
  }
  return null
}

const transformNodeToFormData = () => {

  const curNode = props.curNode
  if (!curNode) {
    return
  }
  
  if (curNode.getData()?.imageName) {
    form.image = curNode.getData().imageName
  }
  
  form.backgroundColor = curNode.attr('body/fill');
  form.strokeColor =  curNode.attr('body/stroke' );
  form.strokeWidth =  curNode.attr('body/strokeWidth');
  form.zIndex = curNode.prop('zIndex');
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  if (!curNode) {
    return
  }

  curNode.attr('body/fill', form.backgroundColor);
    curNode.attr('body/stroke', form.strokeColor);
    curNode.attr('body/strokeWidth', form.strokeWidth);
    
}

// 创建防抖的更新函数
const debouncedTransformFormDataToNode = debounce(() => {
  transformFormDataToNode()
}, 300)

// 监听器
watch(
  () => form,
  (newVal) => {
    const curNode = props.curNode
    if (!curNode) {
      return
    }

    curNode.attr('image/xlink:href',  imageAccessUrl.value + form.image);
    curNode.setData({
      ...curNode.getData(), imageName: form.image
    })
    
    curNode.attr('body/fill', form.backgroundColor);
    curNode.attr('body/stroke', form.strokeColor);
    curNode.attr('body/strokeWidth', form.strokeWidth);
    curNode.prop('zIndex',form.zIndex);
  },
  {deep:true}
)

// 生命周期
onMounted(() => {
  console.log('LayoutProperty created..')
  transformNodeToFormData()
})
</script>

<style lang="scss">
.property-container {
  width: 100%;
}
.circle-button {
  width: 40px;
  height: 40px;
}

.el-table .position-table-cell .cell {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell input {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell .input-pos input {
  width:56px;
}
.el-table .position-table-cell .cell .input-name {
  padding-right: 4px;
}

</style>
