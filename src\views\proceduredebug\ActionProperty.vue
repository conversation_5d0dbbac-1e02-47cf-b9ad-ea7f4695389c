<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="动作名称" prop="name">
        <el-input v-model="form.name" readonly />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
          readonly 
        />
      </el-form-item>
      <el-form-item label="异步模式" prop="asyncMode">
        <el-input v-model="form.asyncMode"  readonly />
      </el-form-item>
      <el-divider content-position="left">可选变量</el-divider>
      <el-form-item v-for="item in form.variablesArr" :key="item.key" :label="item.name" prop="variablesArr">
        <el-input v-model="item.value" :placeholder="item.defaultValue"  readonly />
      </el-form-item>
    </el-form>
    <el-divider content-position="left">预置条件</el-divider>
    <el-table ref="tableRef" :data="form.predicatesArr" size="small" :cell-class-name="'predicate-table-cell'" max-height="450" style="width:100%">
      <el-table-column prop="operator" label="连接符" width="80">
        <template #default="scope">
          <el-select v-model="scope.row.joinOperator" placeholder="请选择" size="small">
            <el-option label="与" value="and" />
            <el-option label="或" value="or" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" min-width="100">
        <template #default="scope">
          <el-input v-model="scope.row.name" placeholder="请选择" class="input-with-select">
          
          </el-input>
        </template>
      </el-table-column>
      <el-table-column prop="compareOperator" label="比较符" width="80">
        <template #default="scope">
          <el-select v-model="scope.row.compareOperator" placeholder="请选择" size="small">
            <el-option label="等于" value="eq" />
            <el-option label="不等" value="ne" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="targetValue" label="值" width="60">
        <template #default="scope">
          <el-input v-model="scope.row.targetValue" class="input-pos" readonly />
        </template>
      </el-table-column>
    </el-table>
    <el-form-item label="条件不满足时" prop="unmatchedThen">
      <el-input v-model="form.unmatchedThen" class="input-pos" readonly />
    </el-form-item>
    <el-divider content-position="left">节点运行日志</el-divider>
    <TaskLogViewer :data-filter="dataFilter"/>
    <div class="log-container">
      <div v-for="(log, index) in logs" :key="index" class="log-entry">
        <span class="log-task-tag" :style="getTaskTagStyle(log.taskId)"></span>
        <span class="log-timestamp" :style="getTextStyleByStatus(log.status)">{{ getTruncatedTime(log.updateTime) }}</span>
        <span class="log-message" :style="getTextStyleByStatus(log.status)">{{ log.name + (log.message ? (': ' + log.message) : '') }}</span>
        <span class="log-status" :style="getTextStyleByStatus(log.status)">{{ log.status }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { Node } from '@antv/x6'
import { Search } from '@element-plus/icons-vue'
import { queryActionVarDefinesByActionId } from '@/api/procedure'
import TaskLogViewer from '@/views/components/TaskLogViewer.vue'

const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  }
})

const defaultForm = { 
  id: null, 
  name: null, 
  description: null, 
  asyncMode: null, 
  variablesArr: [], 
  predicatesArr: [], 
  deleteFlag: null, 
  createBy: null, 
  createTime: null, 
  updateBy: null, 
  updateTime: null 
}

const formRef = ref(null)
const tableRef = ref(null)
const form = reactive({ ...defaultForm })
const rules = reactive({
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
})

const initing = ref(false)
const positionSelectDialogVisible = ref(false)
const curPredicateItem = reactive({})
const curPredicateItemIndex = ref(0)
const logs = ref([])
const dataStore = ref(null)
const dataFilter = reactive({
                    nodeType: 'ACTION',
                    id: 0
                  })
// 监听curNode变化
watch(() => props.curNode, () => {
  transformNodeToFormData()
}, { deep: false })

// 初始化
onMounted(() => {
  transformNodeToFormData()
})

// 方法定义
function init() {
  initing.value = true
}

function openStationDialog() {
  console.log('openStationDialog ...')
}

function transformNodeToFormData() {
  const curData = props.curNode.getData()
  debugger
  if (!curData) return
  
  form.name = curData.name
  form.description = curData.description
  form.asyncMode = curData.asyncMode
  form.unmatchedThen = curData.unmatchedThen
  
  if (curData.variables) {
    form.variablesArr = JSON.parse(curData.variables)
  }
  
  if (curData.predicates) {
    form.predicatesArr = JSON.parse(curData.predicates)
  }

  
  dataFilter.id = curData.id
}

function transferVariablesArrToVariables() {
  const storeData = getStoreObjectByNodeId()
  if (storeData) {
    storeData.variables = null
    if (form.variablesArr && form.variablesArr.length > 0) {
      storeData.variables = {}
      for (const idx in form.variablesArr) {
        const arrItem = form.variablesArr[idx]
        if (arrItem.value) {
          storeData.variables[arrItem.key] = arrItem.value
        }
      }
      storeData.variables = JSON.stringify(storeData.variables)
    }
  }
}

function transferPredicatesArrToPredicates() {
  const curData = getStoreObjectByNodeId()
  if (curData) {
    curData.predicates = JSON.stringify(form.predicatesArr)
  }
}

function addCondition() {
  const newData = { 
    joinOperator: 'and', 
    name: null, 
    sourceType: null, 
    sourceParameter: null, 
    compareOperator: 'eq', 
    targetValue: null 
  }
  form.predicatesArr.push(newData)
}

function deleteCondition(rowIndex) {
  form.predicatesArr.splice(rowIndex, 1)
}

function handlePredicateNameCommand(cmd, rowIdx) {
  const curData = form.predicatesArr[rowIdx]
  
  if (cmd === 'position-select') {
    curData.sourceType = cmd
    Object.assign(curPredicateItem, curData)
    curPredicateItemIndex.value = rowIdx
    positionSelectDialogVisible.value = true
  }
}

function submitPosSelection(evt) {
  form.predicatesArr.splice(curPredicateItemIndex.value, 1, curPredicateItem)
  positionSelectDialogVisible.value = false
}

function getStoreObjectByNodeId() {
  const curNode = props.curNode
  const { graph } = curNode.model || {}

  if (!graph) {
    return null
  }

  dataStore.value = graph.dataStore
  const nodeId = curNode.id
  
  if (dataStore.value.dagNodeId === nodeId) {
    return dataStore.value
  }
  
  if (!Array.isArray(dataStore.value.methods)) {
    return null
  }
  
  for (const methodObj of dataStore.value.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    
    if (!Array.isArray(methodObj.method.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      
      if (!Array.isArray(stepObj.step.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }

  return null
}

function getTaskTagStyle(taskId) {
  // 实现任务标签样式逻辑
  return {}
}

function getTextStyleByStatus(status) {
  // 实现根据状态获取文本样式的逻辑
  return {}
}

function getTruncatedTime(time) {
  // 实现时间截断逻辑
  return time
}
</script>

<style lang="scss" scoped>
.operation {
  width: 18px;
  padding: 2px 2px;
  margin-left: 4px;
}

.operation-container {
  display: flex;
}

:deep(.predicate-table-cell) {
  .cell {
    padding-left: 2px;
    padding-right: 2px;
    
    input {
      padding-left: 2px;
      padding-right: 2px;
    }
    
    .input-pos input {
      width: 56px;
    }
    
    .input-name {
      padding-right: 4px;
    }
  }
}

.log-container {
  .log-entry {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }
  
  .log-task-tag {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
  }
  
  .log-timestamp {
    margin-right: 10px;
    font-size: 12px;
  }
  
  .log-message {
    flex: 1;
  }
  
  .log-status {
    margin-left: 10px;
    font-weight: bold;
  }
}
</style>
