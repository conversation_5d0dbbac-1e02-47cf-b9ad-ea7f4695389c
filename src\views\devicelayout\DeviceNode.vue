<template>

  <div :class="'device-node-bg ' +(  'device-node-bg-'+ deviceInstanceStatus )" 
  :style="imageVisible?'background-image:url('+imageAccessUrl+bgImage+');':''" 
  :title="nodeName+deviceInstanceStatusDisplay">
    <el-badge v-if="getCurDeviceInstance() && getCurDeviceInstance().enableMock" is-dot class="badge-item" title="仿真模式" >
    </el-badge>
      
      <div
      v-for="(marker, index) in markers"
      :key="index"
      :class="['marker', 'marker-'+marker.shape, 'marker-'+marker.state,'marker-sel-'+marker.selectState ]"
      :title="marker.tooltip"
      :style="{ top: marker.y + '%', left: marker.x + '%', width: marker.width + '%', height: marker.height + '%'}"
      @contextmenu.prevent="handleMarkerContextMenu(marker, $event)"
      @mousedown="handleMarkerMouseDown(marker, $event)"
      :id="'marker-'+marker.nodeId"
      >
        <div v-if="marker.state==='RUNNING'" :class="['runner', 'runner-'+marker.shape+'-'+marker.state ]" />

        <el-popover
        v-if="marker.type==='INDICATOR'" 
        :placement="getIndicatorPopoverPlacement(marker)"
        :visible="getIndicatorPopoverVisible(marker)"
        :width="240"
        :teleported="true"
        :persistent="false"
        trigger="manual">
        <template #default>
          <div class="popover-indicator-container">
            <div class="left">
              <span class="title">{{ marker.name }}</span>
              <span v-for="(item, idx) in getIndicatorItems(marker)" 
              :key="idx" class="item">
                {{ `${item.status}: ${item.count} ${item.unit} (${item.percentage})` }}
              </span>
            </div>
            <div class="right"><el-button type="primary" size="small" class="btn" @click="onResetIndicator(marker)">复位</el-button>
              <el-button plain size="small" class="btn" @click="onCancelIndicator(marker)">取消</el-button></div>
          </div>
        </template>
        <template #reference>
            
          <div ref="indicatorRef" v-if="marker.type==='INDICATOR'" :class="['indicator', {'indicator-vertical': marker.height >= marker.width-1}]" 
            :title="getIndicatorTitle(marker)" 
            @click="onClickIndicator(marker)">
            <div 
              v-for="(indicatorItem, idx) in getIndicatorItems(marker)" 
              :key="idx" 
              :class="['indicator-item', 'indicator-item-'+indicatorItem.status]" 
              :style="indicatorItem.isVertical ? 
                {height: indicatorItem.percentage, width: '100%'} : 
                {width: indicatorItem.percentage, height: '100%'}"
            />
          </div>
          
        </template>
      </el-popover>
        <!--
        <PositionDetailConfigPopover
          v-if="marker.type==='INDICATOR'"
          :virtual-triggering = "true"
          virtual-ref="indicatorRef"
          :deviceInstanceId="getCurDeviceInstance().id"
          :name="marker.name" />
-->

<el-popover
        v-if="marker.type==='CONTROLLER'" 
        :placement="getIndicatorPopoverPlacement(marker)"
        :visible="getIndicatorPopoverVisible(marker)"
        :width="240"
        :teleported="true"
        trigger="manual"
        :ref="(el)=>{popoverMap.set(marker, el)}"
        >
        <template #default>
          <div class="popover-controller-container">
              <div class="header"><span class="title">{{ marker.name }}</span>
                <el-button plain size="small" circle :icon="Close" class="close" @click="onCancelIndicator(marker)"></el-button></div>
              <div class="body">
                <el-button  v-for="(item, idx) in marker.cmdControls" 
                 type="primary" size="small" class="btn" @click="onDeviceCtrlProcess(marker)">{{item.name}}</el-button>
              </div>
          </div>
        </template>
        <template #reference>
            
          <div v-if="marker.type==='CONTROLLER'" :class="['indicator', {'indicator-vertical': marker.height >= marker.width-1}]" 
            :title="getIndicatorTitle(marker)" 
            @click="onClickIndicator(marker)">
            
          </div>
          
        </template>
      </el-popover>



      </div>

      <div v-if="deviceInstanceStatusDisplay !== ''" :class="'device-status-text '+ 'device-status-text-'+deviceInstanceStatus">
      {{ nodeName+deviceInstanceStatusDisplay }}
    </div>
      
  <el-dropdown placement="bottom" ref="dropdownRef" 
  trigger="manual" popper-class="device-node-dropdown"
  @command="handleMarkerCommand">
    <span class="el-dropdown-link"></span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="RESET">重置点位状态</el-dropdown-item>
        
        <!-- 添加带子菜单的菜单项 -->
        <div class="submenu-item" @mouseenter="showSubMenu = true;updateSubmenuPosition($event)" @mouseleave="showSubMenu = false">
          <el-dropdown-item>设置点位状态</el-dropdown-item>
          <div class="submenu" v-show="showSubMenu"  :style="{ left: submenuPosition.left, top: submenuPosition.top }">
            <el-dropdown-menu>
              <el-dropdown-item command="IDLE">空闲</el-dropdown-item>
              <el-dropdown-item command="READY">容器可用</el-dropdown-item>
              <el-dropdown-item command="NONE">禁用</el-dropdown-item>
            </el-dropdown-menu>
          </div>
        </div>
        
        <!-- 点位操作菜单项 -->
        <el-dropdown-item command="PICK" divided  @mouseup="startMoveObject">抓取选择</el-dropdown-item>
        <!--
        <el-dropdown-item command="PLACE"  @mouseup="endMoveObject">放置到此</el-dropdown-item>
        -->
      </el-dropdown-menu>
    </template>
  </el-dropdown>

  </div>

</template>

<script setup>
import { ref, inject, onMounted, onBeforeUnmount, computed, nextTick } from 'vue'
import { Graph, FunctionExt, Shape, Platform, Path, Edge, Node, StringExt } from '@antv/x6'

import moment from 'moment'
import { ElNotification, ElBadge, ElDropdown,ElDropdownMenu, ElDropdownItem, ElPopover, ElButton } from 'element-plus'
import { Close } from '@element-plus/icons-vue'

import { changePositionStatus, movePos } from '@/api/position'
import PositionDetailConfigPopover from '@/views/material/components/PositionDetailConfigPopover.vue'

// 依赖注入
const getNode = inject('getNode')
// 从节点数据中获取store
const getStore = () => {
  const node = getNode()
  if (node && node.model && node.model.graph) {
    return node.model.graph.store
  }
  return null
}
const bTaskStarted = computed(() => {
  const store = getStore()
  if (!store) {
    return false
  }
  const ret = store.state.task.task.started
  
  return ret
})
const imageVisible = computed(() => {
  const node = getNode()
  console.log(nodeName.value+' imageVisible:' + node.getData().imageVisible)
  if (node.getData()?.imageVisible === false) {
    return false
  }else{
    return true
  }
})
const deviceInitialized = computed(() => {
  return (getCurDeviceInstance() && getCurDeviceInstance().initialized && getCurDeviceInstance().enableMock)
})
const deviceInstanceStatus = ref(null)
const deviceInstanceStatusDisplay = computed(() => {
  if (deviceInstanceStatus.value === 'NONE') {
    return ': 禁用'
  }else if (deviceInstanceStatus.value === 'FAILED') {
    return ': '+getCurDeviceInstance().message
  }
  return ''
})
// 响应式状态
const nodeName = ref('')
const nodeType = ref('')

const baseApi = ref(null)
const bgImage = ref(null)

const markers = ref([])
const imageAccessUrl = ref(null)

const dropdownRef = ref(null)
const currentMarker = ref(null)
const showSubMenu = ref(false) // 控制子菜单显示
const submenuPosition = ref({ left: 0, top: 0 }) // 添加子菜单位置状态

// 用 Map 存储多个 popover ref
const popoverMap = ref(new Map())


const getIndicatorPopoverVisible = (marker) => {
  if ((marker.type === 'INDICATOR' || marker.type === 'CONTROLLER') && marker.popoverVisible) {
    return true
  }
  return false
}
const getIndicatorPopoverPlacement = (marker) => {
  return marker.popoverPlace || 'bottom'
}
const onClickIndicator = (marker) => {
  if (bTaskStarted.value) {
    return
  }
  if (marker.popoverVisible){
    marker.popoverVisible = false
  }else{
    marker.popoverVisible = true
  }
}

const onResetIndicator = async (marker) => {
  try{
    debugger
      await changePositionStatus({name:marker.name, id:marker.id, changeCmd: 'reset', resetAssociations: true})
      marker.popoverVisible = false
      ElNotification.success({title: '复位成功',
          type:'success',
          duration: 2500})
    }catch(e){
      ElNotification.error({
        title: '复位失败',
      })
    }
  
}

const onCancelIndicator = async (marker) => {
  try{
    debugger
      await changePositionStatus({name:marker.name, id:marker.id, changeCmd: 'reset', resetAssociations: false})
      marker.popoverVisible = false
    }catch(e){
      ElNotification.error({
        title: '撤销失败',
      })
    }
  
}

// 计算属性
const getCurDeviceInstance = () => {
  const node = getNode()
  const { graph } = node.model || {}
  if (!graph || !graph.dataStore) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === node.id) {
      return ins
    }
  }
  return null
}

// 方法
const createMarkers = (current) => {
  
  markers.value = []
  const devInst = getCurDeviceInstance()
  if (!devInst) {
    return
  }

  if (devInst.positionConfig) {
    const posConfigs = JSON.parse(devInst.positionConfig)
    posConfigs.forEach(cfg => {
      cfg.state = 'NONE'
    })
    
    const positions = devInst.positions
    if (positions) {
      for (var i in posConfigs) {
        if (i >= posConfigs.length) {
          break
        }
        const posCfg = posConfigs[i]
        const pos = positions[i]
        if (!pos || !posCfg) {
          console.log('deviceInst:' + devInst.id + ', devInst.name=' + devInst.name + ', pos=' + pos + ', posCfg=' + posCfg)
          continue
        }
        posCfg.id = pos.id
        posCfg.name = pos.name
        posCfg.type = pos.type
        posCfg.visible = pos.visible
        //if (current && current.posIndex == i) {
        //  posCfg.state = current.state
        //}else{
        posCfg.state = pos.status
        //}
        if (current && current.posIndex == i && current.selectState) {
          posCfg.selectState = current.selectState
        }
        
        posCfg.tooltip = pos.name + ':' + posCfg.state
        if (pos.holdByTaskNumber) {
          posCfg.tooltip += '\r\n任务名: ' + pos.holdByTaskName +
            '\r\n任务编号: ' + pos.holdByTaskNumber
        }
        if (pos.holdByTaskActionName) {
          posCfg.tooltip += '\r\n执行动作: ' + pos.holdByTaskActionName
          if (pos.runningStartTime) {
            posCfg.tooltip += '\r\n执行开始: ' + moment(pos.runningStartTime).format('YYYY-MM-DD HH:mm:ss') +
                      '\r\n执行结束: ' + moment(pos.runningEndTime).format('YYYY-MM-DD HH:mm:ss')
          }
        }
      }
    }
    markers.value = posConfigs.filter(cfg => cfg.visible!=='N')
    
    for (const marker of markers.value) {
      if ((marker.type === 'INDICATOR' || marker.type === 'CONTROLLER') && marker.state === 'TO_CONFIRM') {
        marker.popoverVisible = true
      }
    }
  }
}

// 生命周期
const initNode = () => {
  const baseUrl = import.meta.env.VITE_BASE_API === '/' ? '' : import.meta.env.VITE_BASE_API
  baseApi.value = baseUrl
  imageAccessUrl.value = baseUrl + '/file/%E5%9B%BE%E7%89%87/'
  const node = getNode()
  bgImage.value = node.getData().layoutImage
  nodeName.value = node.getData().name
  nodeType.value = node.getData().type
  createMarkers()
}

// 添加右键菜单处理函数
const handleMarkerContextMenu = (marker, event) => {
  currentMarker.value = marker
  if (bTaskStarted.value) {
    return
  }
  if (marker.type === 'INDICATOR') {
    return
  }

  // 确保下拉菜单组件已经挂载
  if (dropdownRef.value) {
    // 手动设置下拉菜单位置并显示
    // 获取事件坐标
    const x = event.clientX;
    const y = event.clientY;

    
    // 移除可能存在的旧克隆元素
    let curCloneElement = document.getElementById('cur-clone-element');
    if (curCloneElement) {
      curCloneElement.remove();
    }
    
    // 创建点位元素的克隆
    const currentTarget = event.currentTarget;
    curCloneElement = currentTarget.cloneNode(true);
    curCloneElement.id = 'cur-clone-element';
    // 设置position-id属性为当前点位的id
    curCloneElement.setAttribute('position-id', currentMarker.value.id);

    
    // 设置克隆元素的样式
    Object.assign(curCloneElement.style, {
      position: 'fixed',
      left: x + 'px',
      top: y + 'px',
      width: currentTarget.clientWidth * getCurrentScale() + 'px',
      height: currentTarget.clientHeight * getCurrentScale() + 'px',
      display: 'none',
      zIndex: '9999',
      pointerEvents: 'none', // 防止克隆元素接收鼠标事件
      transform: 'translate(-50%, -50%)', // 使元素中心对准鼠标位置
      opacity: '0.8',
      boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)' // 添加阴影效果
    });
    
    // 将克隆元素添加到文档中
    document.body.appendChild(curCloneElement);
    
    
    // 设置下拉菜单的位置
    const dropdownEl = dropdownRef.value.$el;
    if (dropdownEl) {
      // 先设置一个固定位置，确保菜单能够正确显示
      const style = document.createElement('style');
      style.id = 'dropdown-position-style';
      style.textContent = `
        .device-node-dropdown {
          position: fixed !important;
          left: ${x}px !important;
          top: ${y}px !important;
          margin: 0 !important;
        }
        .device-node-dropdown .el-popper__arrow {
          display: none!important;
        }
      `;
      
      // 移除可能存在的旧样式
      const oldStyle = document.getElementById('dropdown-position-style');
      if (oldStyle) {
        document.head.removeChild(oldStyle);
      }
      
      // 添加新样式
      document.head.appendChild(style);
    }

    const dropdown = dropdownRef.value
    dropdown.handleOpen(event)
  }
}

const handleMarkerMouseDown = (marker, event) => {
  currentMarker.value = marker
  const node = getNode()
  const { graph } = node.model || {}
  if (graph ) {
    graph.currentPositionId = marker.id
    return
  }
  console.log('handleMarkerMouseDown, id:' + currentMarker.value.id)
}

const handleMarkerCommand = async (command) => {
  const devInst = getCurDeviceInstance()
  if (!devInst || !currentMarker.value) {
    return
  }
  
  // 关闭下拉菜单
  if (dropdownRef.value) {
    dropdownRef.value.handleClose()
  }

  switch (command) {
    case 'RESET':
      try{
        debugger
        await changePositionStatus({name:currentMarker.value.name, id: currentMarker.value.id, changeCmd: 'reset', resetAssociations: false})
        ElNotification({
          title: '重置成功',
          type:'success',
          duration: 2500
        })
      }catch(e){
        ElNotification({
          title: '重置失败',
        })
      }
      break
    case 'IDLE':
    case 'READY':
    case 'NONE':
      try{
        await changePositionStatus({status:command, id:currentMarker.value.id})
        ElNotification({
          title: '变更成功',
          type: 'success',
          duration: 2500
        })
      }catch(e){
        ElNotification({
          title: '变更失败',
          type:'error',
          duration: 2500
        })
      }
        break  
    case 'PICK':
      // 执行拾取操作
      
      break
    case 'PLACE':
      // 执行放置操作
      break
    default:
      // 处理其他命令
      break
  }
  
  
}

// 添加子菜单位置计算方法
const updateSubmenuPosition = (event) => {
  // 获取触发元素
  const target = event.currentTarget;
  if (target) {
    const rect = target.getBoundingClientRect();
    // 计算子菜单位置 - 放在父菜单项的右侧
    submenuPosition.value = {
      left: rect.right + 'px',
      top: rect.top + 'px'
    };
  }
}


// 添加拖动相关的方法
const startMoveObject = (e) => {
  //isResizing.value = true
  
  const handleMouseMove = (moveEvent) => {
    let curCloneElement = document.getElementById('cur-clone-element');
    if (curCloneElement) {
      curCloneElement.style.display = 'block';
      curCloneElement.style.left = moveEvent.clientX + 'px';
      curCloneElement.style.top = moveEvent.clientY + 'px';
    }

  }
  
  const handleMouseUp = (upEvent) => {
  //  isResizing.value = false

    let curCloneElement = document.getElementById('cur-clone-element');
    if (curCloneElement && curCloneElement.style.display === 'block') {
      document.removeEventListener('click', handleMouseMove)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)

      let curPosId = currentMarker.value.id;
      const node = getNode()
      const { graph } = node.model || {}
      if (graph ) {
        curPosId = graph.currentPositionId 
      }
      handlePositionMove( curCloneElement.getAttribute('position-id'), curPosId)
      curCloneElement.style.display = 'none';
    }

  }
  document.addEventListener('click', handleMouseMove)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  
  // 阻止默认行为和冒泡
  e.preventDefault()
  e.stopPropagation()
}

const endMoveObject = (e) => {
  console.log('endMoveObject.clientX=' + e.clientX)
}

const handlePositionMove = async (sourcePositionId, targetPositionId) => {
  
  if (sourcePositionId === (''+targetPositionId)) {
    return
  }
  const srcPos = getPositionById(sourcePositionId)
  const targetPos = getPositionById(targetPositionId)
  if (!srcPos ||!targetPos) {
    return
  }

  // 检查目标点位状态是否为IDLE
  if (targetPos.status !== 'IDLE') {
    ElNotification({
      title: '操作失败',
      message: '目标点位状态必须为空闲(IDLE)状态',
      type: 'error',
      duration: 2500
    })
    return
  }
  
  // 检查源点位是否有机器人
  if (!srcPos.robots || srcPos.robots.length === 0) {
    ElNotification({
      title: '操作失败',
      message: '源点位没有可移动的机器人',
      type: 'error',
      duration: 2500
    })
    return
  }
  
  // 检查源点位和目标点位是否放置相同的物体
  
  if (!srcPos.objectName || srcPos.objectName !== targetPos.objectName) {
    ElNotification({
      title: '操作失败',
      message: '源点位和目标点位应该放置相同的物体',
      type: 'error',
      duration: 2500
    })
    return
  }
  
  
  // 查找源点位和目标点位中具有相同robotDevInstanceId的机器人
  let matchedRobot = null
  let matchedRobotIndex = -1
  
  for (let i = 0; i < srcPos.robots.length; i++) {
    const srcRobot = srcPos.robots[i]
    // 检查目标点位是否有相同ID的机器人
    const targetRobotIndex = targetPos.robots ? 
      targetPos.robots.findIndex(robot => robot.robotDevInstanceId === srcRobot.robotDevInstanceId) : -1
    
    if (targetRobotIndex !== -1) { // 目标点位没有相同ID的机器人，可以移动
      matchedRobot = srcRobot
      matchedRobotIndex = i
      break
    }
  }
  
  if (!matchedRobot) {
    ElNotification({
      title: '操作失败',
      message: '没有找到可以移动的机器人',
      type: 'error',
      duration: 2500
    })
    return
  }
  
  // 执行移动操作
  try {
     const res = await movePos({
      sourcePositionId: sourcePositionId,
      targetPositionId: targetPositionId,
      robotDevInstanceId: matchedRobot.robotDevInstanceId
    })
    ElNotification({
      title: '位置移动已提交,请稍候',
      message: '',
      type: 'success',
      duration: 2500
    })
  } catch (error) {
    console.error('移动机器人时发生错误:', error)
    ElNotification({
      title: '操作失败',
      message: '移动机器人时发生错误',
      type: 'error',
      duration: 2500
    })
  }
}

// 通过ID获取点位对象
const getPositionById = (id) => {
  const node = getNode()
  const { graph } = node.model || {}
  if (!graph ||!graph.dataStore) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]

      const positions = ins.positions
      if (positions) {
        for (var i in positions) {
          const pos = positions[i]
          if (pos.id == id) {
            return pos
          }
        }
      }

  }
  return null
}


// 添加获取当前缩放比例的方法
const getCurrentScale = () => {
  const node = getNode()
  const { graph } = node.model || {}
  if (!graph) {
    return 1
  }
  // 获取当前的变换矩阵
  return graph.zoom()
}

// 监听数据变化
const handleDataChange = ({ cell, current }) => {

  if (cell.getData().changeState === 'RESCALED') {
    for (const marker of markers.value) {
     if (marker.popoverVisible) {
        marker.popoverVisible = false
        nextTick(()=>{
          marker.popoverVisible = true
        })
     }
    }
  }

  if (cell.getData().changeState === 'NODE_UNMOUNT') {
    console.log('NODE_UNMOUNT:'+nodeName.value)
    for (const marker of markers.value) {
      marker.popoverVisible = false
    }
  }

  if (cell.getData().changeState === 'POSITION_CHANGE') {
    createMarkers(current)
  }
  if (cell.getData().changeState === 'DEV_INST_STATUS_CHANGE') {
    const inst = getCurDeviceInstance()
    if (inst){
      deviceInstanceStatus.value = inst.status
    }
   
  }
  if (cell.getData().changeState === 'CHANGED_FROM_PROPERTY') {
    bgImage.value = cell.getData().layoutImage
    nodeName.value = cell.getData().name
    nodeType.value = cell.getData().type
  }
  delete cell.getData().changeState
}

const getIndicatorItems = (marker) => {
  const devInst = getCurDeviceInstance()
  if (!devInst || !devInst.positions) {
    return []
  }
  
  // 创建状态计数对象
  const statusCount = {}
  let materialUnit = '个'
  let totalCount = 0
  // 遍历positions统计各状态数量
  devInst.positions.forEach(pos => {
    if (pos && pos.status && pos.name===marker.name && pos.type !=='INDICATOR') {
      if (!statusCount[pos.status]) {
        statusCount[pos.status] = 0
      }
      statusCount[pos.status]++
      totalCount++
    }
  })
  
  // 转换为数组格式，便于在模板中使用
  const result = []
  const isVertical = marker.height >= marker.width-1
  
  for (const status in statusCount) {
    result.push({
      status: status,
      count: statusCount[status],
      percentage: (statusCount[status] / totalCount * 100).toFixed(0) + '%',
      unit: materialUnit,
      isVertical: isVertical
    })
  }
  if (result.length === 0) {
    return getIndicatorItemsOnMaterialValue(marker)
  }
  return result
}

const getIndicatorItemsOnMaterialValue = (marker) => {
  const devInst = getCurDeviceInstance()
  if (!devInst ||!devInst.positions) {
    return []
  }
    // 创建状态计数对象
    const statusCount = {}
  let materialUnit = '个'
  let totalCount = 0
  // 遍历positions统计各状态数量
  devInst.positions.forEach(pos => {
    if ( pos.name===marker.name && pos.type ==='INDICATOR') {
      const materialTotalValue = pos.materialTotalValue
      const materialRemainValue = pos.materialRemainValue
      statusCount['READY'] = materialRemainValue
      statusCount['IDLE'] = materialTotalValue - materialRemainValue
      materialUnit = pos.materialUnit
      totalCount = materialTotalValue
    }
  })
  
  // 转换为数组格式，便于在模板中使用
  const result = []
  const isVertical = marker.height > marker.width
  
  for (const status in statusCount) {
    result.push({
      status: status,
      count: statusCount[status],
      percentage: (statusCount[status] / totalCount * 100).toFixed(0) + '%',
      unit: materialUnit,
      isVertical: isVertical
    })
  }

  return result
}

const getIndicatorTitle = (marker) => {
  const items = getIndicatorItems(marker)
  if (items.length === 0) {
    return '无'
  }

  let title = marker.name + '\n'
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    title += `${item.status}: ${item.count} ${item.unit} (${item.percentage})`
    if (i < items.length - 1) {
      title += '\n '
    }
  }
  return title

}

onMounted(() => {
  initNode()
  const node = getNode()
  
  // 初始化设备状态
  //deviceInitialized.value = (getCurDeviceInstance() && getCurDeviceInstance().initialized)
  
  
  node.on('change:data', handleDataChange)

  node.on('change:position', (cell, position) => {
    console.log('change:position')
    
  })
  
  node.on('change:size', (cell, size) => {
    console.log('change:size')
    
  })
  
// 组件卸载时移除监听
onBeforeUnmount(() => {
  const node = getNode()
  
  console.log('DeviceNode onBeforeUnmount')
  node.off('change:data', handleDataChange)
debugger
  for (const marker of markers.value) {
    if (marker.popoverVisible){
      marker.popoverVisible = false
    }
  }
})
})

</script>

<style  rel="stylesheet/scss" lang="scss" scoped>

.device-node-bg {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.device-node-bg-connected {
  border: 1px solid rgb(0, 255, 102);
  box-shadow: 0px 0px 5px #d3d3d3;
}
.device-node-bg-FAILED {
  border: 1px solid rgb(255, 0, 0);
  box-shadow: 0px 0px 5px #d3d3d3;
}
.device-node-bg-UNLOCKED {
  border: 1px solid rgba(233, 126, 3, 1);
  box-shadow: 0px 0px 5px #d3d3d3;
  animation: selected-opacity-blink 1s infinite alternate;
}

.device-status-text {
  position: absolute;
  font-size: 12px;
  //top:2px;
  //left:2px;
  background-color: white;
  padding:2px;
}
.device-status-text-FAILED {
  color: rgb(255, 0, 0);
}

.marker {
  position: absolute;
  width: 20px;
  
  cursor: pointer;
  border: 1px solid rgb(153, 153, 153); /* Optional for better visibility */
}
.marker:hover {
  border: 1px solid rgb(247, 115, 0); /* Optional for better visibility */
}
.marker-circle {
  aspect-ratio: 1 / 1;
  overflow: hidden;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}
.marker-rect {
  border-radius: 0;
}
.marker-NONE {
  background-color: rgba(248, 248, 248, 0.038);
  display: block;
}
.marker-IDLE {
  background-color: white;
  display: block;
}
.marker-IDLE_CONTAINER {
  background-color: rgb(6, 185, 235);
  display: none;
}
.marker-READY {
  background-color: rgba(43, 209, 255, 0.64);
}
.marker-HOLD {
  background-color: rgb(0, 255, 0);
  display: block;
}
.marker-RUNNING {
  background-color: rgb(0, 255, 0);
  display: block;
}
.marker-TO_ENTER {
  background-color: rgb(0, 255, 0);
  animation: opacity-blink 1s infinite alternate;
  display: block;
}
.marker-TO_HOLD {
  background-color: rgb(0, 255, 0);
  animation: opacity-blink 1s infinite alternate;
  display: block;
}
.marker-DONE {
  background-color: rgb(0, 140, 255);
  display: block;
}
.marker-WAITING {
  background-color: rgb(228, 49, 49);
  display: block;
}
@keyframes opacity-blink {
    0% {
        background-color: rgba(0, 255, 0, 1);
    }
    100% {
        background-color: rgba(255, 0, 0, 0);
    }
}

.marker-LEAVE {
  background-color: rgb(221, 232, 18);
  display: block;
}
.marker-TO_CONFIRM {
  border: 2px solid rgb(233, 126, 3);
  display: block;
}
.marker-sel-SELECTED {
  border: 2px solid rgb(233, 126, 3);
  animation: selected-opacity-blink 1s infinite alternate;
  display: block;
}

@keyframes selected-opacity-blink {
    0% {
        border-color: rgba(233, 126, 3, 1);
    }
    100% {
      border-color: rgba(255, 0, 0, 0);
    }
}

.runner {
  display: none;
}
.runner-circle-RUNNING {
  display:block;

  width: 100%;
  aspect-ratio: 1;
  border-radius: 50%;
  background:
    radial-gradient(farthest-side,#ffa516 100%,#0000) top/4px 4px no-repeat,
    conic-gradient(#0000 30%,#ffa516);
  -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 4px),#000 0);
  animation: l13 1s infinite linear;
}
@keyframes l13{
  100%{transform: rotate(1turn)}
}

.runner-rect-RUNNING {
  display: block;
  width: 100%;
  height: 100%;
  aspect-ratio: 1;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid transparent;
    border-radius: 4px;
    background-image: 
      linear-gradient(90deg, #ffa516 50%, transparent 50%),
      linear-gradient(90deg, #ffa516 50%, transparent 50%),
      linear-gradient(0deg, #ffa516 50%, transparent 50%),
      linear-gradient(0deg, #ffa516 50%, transparent 50%);
    background-position: 
      0 0,
      0 100%,
      0 0,
      100% 0;
    background-size: 
      200% 2px,
      200% 2px,
      2px 200%,
      2px 200%;
    background-repeat: no-repeat;
    animation: snake-border 1.5s infinite linear;
  }
}

@keyframes snake-border {
  0% {
    background-position: 
      0 0,
      0 100%,
      0 0,
      100% 0;
  }
  25% {
    background-position: 
      200% 0,
      0 100%,
      0 200%,
      100% 0;
  }
  50% {
    background-position: 
      200% 0,
      200% 100%,
      0 200%,
      100% 200%;
  }
  75% {
    background-position: 
      0 0,
      200% 100%,
      0 0,
      100% 200%;
  }
  100% {
    background-position: 
      0 0,
      0 100%,
      0 0,
      100% 0;
  }
}


.badge-item {
  margin-top: 10px;
  margin-right: 40px;
  position:absolute;
  z-index: 3998;
}

.markers-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.indicator {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 2px;
  flex-direction: row;
}

.indicator-vertical {
  flex-direction: column;
}

.indicator-item {
  display: inline-block;
}

.indicator-item-IDLE {
  background-color: rgba(255, 255, 255, 0.34);
}

.indicator-item-READY {
  background-color: rgba(43, 209, 255, 0.34);
}

.indicator-item-RUNNING {
  background-color: rgb(0, 255, 0);
}

.indicator-item-HOLD {
  background-color: rgb(0, 255, 0);
}

.indicator-item-WAITING {
  background-color: rgb(228, 49, 49);
}

.indicator-item-NONE {
  background-color: rgba(248, 248, 248, 0.038);
}

.indicator-item-DONE {
  background-color: rgb(0, 140, 255, 0.34);
}

.indicator-item-LEAVE {
  background-color: rgb(221, 232, 18);
}

.popover-indicator-container {
  display: flex;
  flex-direction: row;
  .left {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    .title {
      font-weight: bold;
    }
    .item {

    }
  }
  .right {
    width: 60px;
    padding-left: 4px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap:2px;
    .btn {
      width: 100%;
      margin-left: 0px;
    }
  }
}

.popover-controller-container {
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .title {
      font-weight: bold;
    }
    .close {
      cursor: pointer;
      position: relative;
      top: -8px;
      font-size: 12px;
    }
  }
  .body {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 4px;
   .btn {
      width: 100%;
      margin-left: 0px;
    }
  }
}

.device-node-dropdown .el-scrollbar {
  overflow: visible !important;
  max-height: none !important;
}
.device-node-dropdown .el-dropdown-menu {
  overflow: visible !important;
  max-height: none !important;
}

.device-node-dropdown .el-scrollbar__wrap {
  overflow: visible !important;
  max-height: none !important;
}

// 子菜单相关样式
.submenu-item {
  position: relative;
  width: 100%;
}

.submenu {
  position: fixed;
  left: 100%;
  top: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 2001;
  min-width: 120px;
  overflow: visible;
  max-height: none; // 移除最大高度限制
}

// 确保下拉菜单链接不可见
.el-dropdown-link {
  display: none;
}

// 添加全局样式
:global(.device-node-dropdown .el-dropdown-menu__item:focus, .device-node-dropdown .el-dropdown-menu__item:not(.is-disabled):hover) {
  background-color: #ecf5ff;
  color: #409EFF;
}

:global(.device-node-dropdown .submenu .el-dropdown-menu) {
  border: none;
  box-shadow: none;
}
</style>
