<template>
  <div :class="tagsView ? 'app-container-tags' : 'app-container'">
    <div :class="tagsView ? 'flow-tags' : 'flow'">
      <div class="content">
        <!--左侧工具栏-->
        <div id="stencil" class="stencil" />
        <div class="panel" :style="{ width: `calc(100% - ${stencilWidth}px - ${configWidth}px)` }">
          <!--流程图工具栏-->
          <div class="toolbar" :style="{ width: `calc(100% - ${stencilWidth}px - ${configWidth}px)` }">
            <el-button-group class="toolbar-group">
              <el-button
                :loading="configLoading"
                type="primary"
                size="small"
                plain
                :icon="Finished"
                @click="doSubmit('DRAFT')"
              >保存</el-button>
              <el-tooltip class="item" effect="dark" content="放大" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomIn"
                  @click="graph?.zoom(0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="缩小" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomOut"
                  @click="graph?.zoom(-0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="调整到合适大小" placement="top">
                <el-button
                  size="small"
                  :icon="Aim"
                  @click="graph?.zoomToFit({ padding: 48, minScale: 0.8, maxScale: 4 });
                          graph?.centerContent()"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除选中的节点" placement="top">
                <el-button
                  size="small"
                  :icon="Delete"
                  @click="removeNodes()"
                />
              </el-tooltip>
            </el-button-group>
          </div>
          <!--流程图画板-->
          <div id="container" />
        </div>
        <!-- 可拖动分隔条 -->
        <div class="resizer" @mousedown="startResize"></div>

        <!--右侧工具栏-->
        <el-card class="config" :style="{ width: `${configWidth}px`}">
          <template #header>
            <div class="clearfix">
              <span>节点属性</span>
            </div>
          </template>
          <ProcedureProperty v-if="selectedNode && selectedNode.getData().type=='PROCEDURE'" :cur-node="selectedNode" />
          <MethodProperty v-if="selectedNode && selectedNode.getData().type=='METHOD'" :cur-node="selectedNode" />
          <StepProperty v-if="selectedNode && selectedNode.getData().type=='STEP'" :cur-node="selectedNode" />
          <ActionProperty v-if="selectedNode && selectedNode.getData().type=='ACTION'" :cur-node="selectedNode" />
          <CommandProperty v-if="selectedNode && selectedNode.getData().type=='COMMAND'" :cur-node="selectedNode" />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, provide } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { ElNotification } from 'element-plus'
import { 
  ZoomIn,
  ZoomOut,
  Aim,
  Delete,
  Finished
} from '@element-plus/icons-vue'

import { Graph, FunctionExt, Shape, Platform, Path, Edge, Node, StringExt } from '@antv/x6'
import { register } from '@antv/x6-vue-shape'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Selection } from '@antv/x6-plugin-selection'
import { Scroller } from '@antv/x6-plugin-scroller'

import { registerDataProcessingDag, isOverlapping } from '@/views/components/x6/dag/index'

import ProcedureProperty from './ProcedureProperty.vue'
import MethodProperty from './MethodProperty.vue'
import StepProperty from './StepProperty.vue'
import ActionProperty from './ActionProperty.vue'
import CommandProperty from './CommandProperty.vue'

import ProcedureNode from './ProcedureNode.vue'
import { createNode as createProcedureNode, createNode, appendNode } from '@/hooks/useDagNode'

import { add, edit, queryProcedureById, queryAllMethods, queryAllSteps, queryAllActions, queryAllUnRefProcNodes, cleanUnRefProcNodes } from '@/api/procedure'

// 状态定义
const router = useRouter()
const route = useRoute()
const store = useStore()

const graph = ref(null)
const stencil = ref(null)
const selectedNode = ref(null)
const configLoading = ref(false)
const addOrEdit = ref('add')
const procedureId = ref(0)
const rootNode = ref(null)

const dataStore = reactive({})

const stencilWidth = ref(260) // 左侧工具栏宽度
const configWidth = ref(290) // 右侧属性面板宽度
const isResizing = ref(false)

const graphLoading = ref(false)

const tagsView = computed(() => store.state.settings.tagsView)

// 组件列表
const allMethods = ref([])
const allSteps = ref([])
const allActions = ref([])

// stencil 分组配置
const methodGroups = [
  {
    name: '方法列表',
    collapsable: true
  },
  {
    name: '步骤列表',
    collapsable: true
  },
  {
    name: '动作列表',
    collapsable: true
  }
]

// 注册组件
register({
  shape: 'procedure-node',
  width: 100,
  height: 120,
  component: ProcedureNode,
  ports: {
    groups: {
      in: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: 'transparent',
            strokeWidth: 1,
            fill: 'transparent'
          }
        }
      },
      out: {
        position: {
          name: 'right',
          args: {
            dx: 0,
            dy: 0
          }
        },
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: 'transparent',
            strokeWidth: 1,
            fill: 'transparent'
          }
        }
      }
    }
  }
})

// 注册X6 DAG组件
registerDataProcessingDag()

// 依赖注入
provide('getDataStore', () => dataStore)

// 方法定义
const initGraph = () => {
  const container = document.getElementById('container')
  graph.value = new Graph({
    container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    autoResize: true,
    background: false,
    snapline: true,
    panning: {
      enabled: false,
      eventTypes: ['rightMouseDown']
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    },
    grid: {
      type: 'dot',
      size: 10,
      visible: true,
      args: {
        color: '#c0c0c0',
        thickness: 1
      }
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF',
            strokeWidth: 4
          }
        }
      }
    },
    connecting: {
      snap: true,
      allowBlank: false,
      allowMulti: true,
      allowLoop: false,
      highlight: true,
      sourceAnchor: {
        name: 'left',
        args: {
          dx: Platform.IS_SAFARI ? 4 : 8
        }
      },
      targetAnchor: {
        name: 'right',
        args: {
          dx: Platform.IS_SAFARI ? 4 : -8
        }
      },
      createEdge() {
        return graph.value.createEdge({
          shape: 'data-processing-curve',
          attrs: {
            line: {
              strokeDasharray: '5 5'
            }
          },
          zIndex: -1
        })
      },
      validateConnection({ sourceMagnet, targetMagnet }) {
        if (!sourceMagnet || sourceMagnet.getAttribute('port-group') === 'in') {
          return false
        }
        if (!targetMagnet || targetMagnet.getAttribute('port-group') !== 'in') {
          return false
        }
        return true
      }
    }
  })

  graph.value.use(
    new Selection({
      rubberband: true,
      showNodeSelectionBox: false
    })
  )
  .use(
    new Scroller({
      enabled: true
    })
  )
}

const initStencil = () => {
  // 创建Stencil
  stencil.value = new Stencil({
    title: '流程节点面板',
    search(cell, keyword) {
      return cell.shape.indexOf(keyword) !== -1
    },
    placeholder: '输入名称查找',
    notFoundText: 'Not Found',
    collapsable: false,
    stencilGraphHeight: 0,
    stencilGraphWidth: 200,
    target: graph.value,
    groups: methodGroups,
    graphWidth: '100%',
    graphOptions: { 
      autoResize: true, 
      scroller: {
        enabled: true,
        pannable: true
      }
    },
    layoutOptions: {
      columns: 1,
      rowHeight: 40,
      marginY: 0,
      dy: 0,
      dx: 80
    }
  })

  const stencilContainer = document.getElementById('stencil')
  stencilContainer?.appendChild(stencil.value.container)

  const commonAttrs = {
    body: {
      fill: '#f0fe00',
      stroke: '#8f8f23',
      strokeWidth: 1,
      class: 'node_icon'
    },
    label: {
      refX: '100%',
      refY: '50%',
      refX2: 4,
      textAnchor: 'start',
      textVerticalAnchor: 'middle'
    },
    image: {
      fill: '#ff0000',
      stroke: '#ffff00'
    }
  }

  // 加载待选方法列表
  loadStencilMethods(commonAttrs)
  // 加载待选步骤列表
  loadStencilSteps(commonAttrs)
  // 加载待选动作列表
  loadStencilActions(commonAttrs)
}

const loadStencilMethods = async (commonAttrs) => {
  try {
    const res = await queryAllMethods(null)
    if (res) {
      allMethods.value = res
      const methodNodes = allMethods.value.map(mth => 
        graph.value.createNode({
          shape: 'data-processing-dag-node',
          x: 10,
          attrs: commonAttrs,
          data: { name: mth.name, description: mth.description, method: mth, type: 'METHOD' }
        })
      )
      
      if (methodNodes.length > 0) {
        stencil.value.load(methodNodes, '方法列表')
      }
    }
  } catch (err) {
    console.error('加载方法列表失败:', err)
  }
}

const loadStencilSteps = async (commonAttrs) => {
  try {
    const res = await queryAllSteps(null)
    if (res) {
      allSteps.value = res
      const stepNodes = allSteps.value.map(step => 
        graph.value.createNode({
          shape: 'data-processing-dag-node',
          x: 10,
          attrs: commonAttrs,
          data: { name: step.name, description: step.description, step: step, type: 'STEP' }
        })
      )
      
      if (stepNodes.length > 0) {
        stencil.value.load(stepNodes, '步骤列表')
      }
    }
  } catch (err) {
    console.error('加载步骤列表失败:', err)
  }
}

const loadStencilActions = async (commonAttrs) => {
  try {
    
    const res = await queryAllActions(null)
    if (res) {
      allActions.value = res
      
      const actionNodes = allActions.value.map(action => 
        graph.value.createNode({
          shape: 'data-processing-dag-node',
          x: 10,
          attrs: commonAttrs,
          data: { name: action.name, description: action.description, dagNodeImage:action.dagNodeImage, action: action, type: 'ACTION' }
        })
      )
      
      if (actionNodes.length > 0) {
        stencil.value.load(actionNodes, '动作列表')
      }
    }
  } catch (err) {
    console.error('加载动作列表失败:', err)
  }
}

const initEvents = () => {
  if (!graph.value) return

  // 监听空白点击事件
  graph.value.on('blank:click', () => {
    // Cancelling operations
  })

  // 监听节点选中事件
  graph.value.on('cell:selected', ({ cell }) => {
    if (cell.isNode()) {
      selectedNode.value = cell
      console.log('selectedNode is changed to: ' + cell.id)
    }
  })

  // 节点撤销选中
  graph.value.on('cell:unselected', () => {
    selectedNode.value = null
  })

  // 自定义放置节点的动作
  graph.value.on('cell:added', ({ cell }) => {
    if (cell.isNode()) {
      const appendRet = appendGraphNode(cell)
      if (!appendRet && cell.getData().parentNodeId) {
        handleNodeAddition(cell)
      }
    }
  })

  // 监听节点位置变化事件
  graph.value.on('node:moved', ({ node }) => {
    appendGraphNode(node)
  })
}

const handleNodeAddition = (cell) => {
  const parentObj = getStoreObjectByNodeId(cell.getData().parentNodeId)
  if (!parentObj) return

  const nodeType = cell.getData().type
  const newData = { ...cell.getData(), dagNodeId: cell.id }

  if (nodeType === 'METHOD') {
    if (!parentObj.methods) {
      parentObj.methods = []
    }
    parentObj.methods.push(newData)
  } else if (nodeType === 'STEP') {
    if (!parentObj.method?.steps) {
      parentObj.method.steps = []
    }
    parentObj.method.steps.push(newData)
  } else if (nodeType === 'ACTION') {
    if (!parentObj.step?.actions) {
      parentObj.step.actions = []
    }
    parentObj.step.actions.push(newData)
  }
}

const appendGraphNode = (node) => {
  if (!graph.value) return false

  const nodes = graph.value.getNodes()
  const newNode = node
  let isCorrect = false

  for (const existingNode of nodes) {
    if (existingNode !== newNode && isOverlapping(newNode, existingNode)) {
      // 方法放置
      if (existingNode.getData().type === 'PROCEDURE' && newNode.getData().type === 'METHOD') {
        if (newNode.ports.items.length === 0) {
          newNode.addPort({ id: newNode.id + '-in', group: 'in' })
          newNode.addPort({ id: newNode.id + '-out', group: 'out' })
        }
        appendNode(existingNode, newNode)

        const parentObj = getStoreObjectByNodeId(existingNode.id)
        if (!parentObj.methods) {
          parentObj.methods = []
        }
        const newData = { ...newNode.getData(), dagNodeId: newNode.id }
      //  parentObj.methods.push(newData)
        isCorrect = true
        break
      }
      // 步骤放置
      if (existingNode.getData().type === 'METHOD' && newNode.getData().type === 'STEP') {
        if (newNode.ports.items.length === 0) {
          newNode.addPort({ id: newNode.id + '-in', group: 'in' })
          newNode.addPort({ id: newNode.id + '-out', group: 'out' })
        }
        appendNode(existingNode, newNode)

        const parentObj = getStoreObjectByNodeId(existingNode.id)
        if (!parentObj.method.steps) {
          parentObj.method.steps = []
        }
        const newData = { ...newNode.getData(), dagNodeId: newNode.id }
      //  parentObj.method.steps.push(newData)
        isCorrect = true
        break
      }
      // 动作放置
      if (existingNode.getData().type === 'STEP' && newNode.getData().type === 'ACTION') {
        if (newNode.ports.items.length === 0) {
          newNode.addPort({ id: newNode.id + '-in', group: 'in' })
          newNode.addPort({ id: newNode.id + '-out', group: 'out' })
        }
        appendNode(existingNode, newNode)

        const parentObj = getStoreObjectByNodeId(existingNode.id)
        if (!parentObj.step.actions) {
          parentObj.step.actions = []
        }
        const newData = { ...newNode.getData(), dagNodeId: newNode.id }
      //  parentObj.step.actions.push(newData)
        isCorrect = true
        break
      }
    }
  }
  return isCorrect
}

const removeNodes = () => {
  const nodes = graph.value?.getSelectedCells()
  if (!nodes) return

  for (const node of nodes) {
    graph.value.removeCell(node)
    graph.value.removeConnectedEdges(node)
    
    const nodeType = node.getData().type
    const data = getStoreObjectByNodeId(node.id)
    const parentData = getStoreParentObjectByNodeId(node.id)
    
    if (nodeType === 'COMMAND' && parentData) {
      // 设备指令不允许在这里删除存储数据
    } else if (nodeType === 'ACTION' && parentData) {
      const idxCmd = parentData.step.actions.findIndex(item => item.dagNodeId === data.dagNodeId)
      if (idxCmd >= 0) {
        parentData.step.actions.splice(idxCmd, 1)
      }
    } else if (nodeType === 'STEP' && parentData) {
      const idxCmd = parentData.method.steps.findIndex(item => item.dagNodeId === data.dagNodeId)
      if (idxCmd >= 0) {
        parentData.method.steps.splice(idxCmd, 1)
      }
    } else if (nodeType === 'METHOD' && parentData) {
      const idxCmd = parentData.methods.findIndex(item => item.dagNodeId === data.dagNodeId)
      if (idxCmd >= 0) {
        parentData.methods.splice(idxCmd, 1)
      }
    }
  }
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  if (!Array.isArray(dataStore.methods)) {
    return null
  }
  
  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    if (!Array.isArray(methodObj.method?.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      if (!Array.isArray(stepObj.step?.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }
  return null
}

const getStoreParentObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return null
  }
  if (!Array.isArray(dataStore.methods)) {
    return null
  }
  
  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return dataStore
    }
    if (!Array.isArray(methodObj.method?.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return methodObj
      }
      if (!Array.isArray(stepObj.step?.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return stepObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return actionObj
          }
        }
      }
    }
  }
  return null
}

const getRootNode = () => {
  let rootNode = null
  graph.value?.getNodes().forEach((graphNode) => {
    if (graphNode.getData().parentNodeId == null) {
      rootNode = graphNode
    }
  })
  return rootNode
}

const initData = async () => {

  const res = await queryAllUnRefProcNodes()
  if (!res) return
  Object.assign(dataStore, res)
 // graph.value.fromJSON(JSON.parse(dataStore.dagJson))
  graph.value.dataStore = dataStore
  rootNode.value = getRootNode()

  refreshGraph()
}

const refreshGraph = () => {
  if (!graph.value) return
debugger
  const rootData = graph.value.dataStore
  const nodes = graph.value.getCells()
  
  for (const node of nodes) {
    graph.value.removeCell(node)
    graph.value.removeConnectedEdges(node)
  }
  
  let y4 = 40
  rootData.type = 'PROCEDURE'
  rootNode.value = createProcedureNode(
    graph.value,
    { x: 20, y: 100 },
    rootData
  )

  let firstMthPos
  let lastMthPos

  for (const idxMth in rootData.methods) {
    debugger
    const mth = rootData.methods[idxMth]
    mth.type = 'METHOD'
    mth.name = mth.method.name
    const mthNode = createNode(graph.value, { x: 20 + 50 * 1, y: 100 }, mth)
    appendNode(rootNode.value, mthNode)

    let firstStepPos
    let lastStepPos
    for (const idxStep in mth.method.steps) {
      const step = mth.method.steps[idxStep]
      step.type = 'STEP'
      step.name = step.step.name
      const stepNode = createNode(graph.value, { x: 20 + 100 * 2, y: 100 }, step)
      appendNode(mthNode, stepNode)

      let firstActPos
      let lastActPos
      for (const idxAct in step.step.actions) {
        const act = step.step.actions[idxAct]
        act.type = 'ACTION'
        act.name = act.action.name
        const actNode = createNode(graph.value, { x: 20 + 100 * 3, y: 100 }, act)
        appendNode(stepNode, actNode)
        
        const firstY4 = y4
        /*
        for (const idxCmd in act.commands) {
          const cmd = act.commands[idxCmd]
          cmd.type = 'COMMAND'
          const cmdNode = createNode(graph.value, { x: 20 + 100 * 4, y: 100 }, cmd)
          appendNode(actNode, cmdNode)
          y4 = y4 + 42
          cmdNode.position(cmdNode.position().x, y4)
          console.log('cmd.position.x=' + cmdNode.position().x + ',.position.y=' + y4 + ', name=' + cmd.name)
        }*/
        
        actNode.position(actNode.position().x, firstY4 + (y4 - firstY4 + 50) / 2)
        y4 = y4 + 42 + 4

        if (Number(idxAct) === 0) {
          firstActPos = actNode.position()
        }
        if (Number(idxAct) === step.step.actions.length - 1) {
          lastActPos = actNode.position()
        }
      }

      if (firstActPos){
        stepNode.position(stepNode.position().x, firstActPos.y + (lastActPos.y - firstActPos.y) / 2)
      }
      if (Number(idxStep) === 0) {
        firstStepPos = stepNode.position()
      }
      if (Number(idxStep) === mth.method.steps.length - 1) {
        lastStepPos = stepNode.position()
      }
    }

    mthNode.position(mthNode.position().x, firstStepPos.y + (lastStepPos.y - firstStepPos.y) / 2)
    debugger
    if (Number(idxMth) === 0) {
      firstMthPos = mthNode.position()
    }
    if (Number(idxMth) === rootData.methods.length - 1) {
      lastMthPos = mthNode.position()
    }
  }

  rootNode.value.position(rootNode.value.position().x, firstMthPos.y + (lastMthPos.y - firstMthPos.y) / 2)
  graphLoading.value = false

}

const sortDataStore = () => {
  if (!Array.isArray(dataStore.methods)) return
  // 过滤掉 dagNodeId 不存在于图表中的方法
  
  dataStore.methods = dataStore.methods.filter(mth => {
    return mth.dagNodeId && graph.value.getCellById(mth.dagNodeId);
  });
  dataStore.methods.sort((mth1, mth2) => {
    const node1 = graph.value.getCellById(mth1.dagNodeId)
    const node2 = graph.value.getCellById(mth2.dagNodeId)
    return node1?.position().y - node2?.position().y
  })

  dataStore.methods.forEach((mth) => {
    if (!Array.isArray(mth.method?.steps)) return
    // 过滤掉 dagNodeId 不存在于图表中的步骤
    mth.method.steps = mth.method.steps.filter(stp => {
      return stp.dagNodeId && graph.value.getCellById(stp.dagNodeId);
    });
    mth.method.steps.sort((step1, step2) => {
      const node1 = graph.value.getCellById(step1.dagNodeId)
      const node2 = graph.value.getCellById(step2.dagNodeId)
      if (!node1 || !node2) return 0
      return node1.position().y - node2.position().y
    })

    mth.method.steps.forEach(stp => {
      if (!Array.isArray(stp.step?.actions)) return
      // 过滤掉 dagNodeId 不存在于图表中的动作
      stp.step.actions = stp.step.actions.filter(act => {
        return act.dagNodeId && graph.value.getCellById(act.dagNodeId);
      });
      stp.step.actions.sort((act1, act2) => {
        const node1 = graph.value.getCellById(act1.dagNodeId)
        const node2 = graph.value.getCellById(act2.dagNodeId)
        if (!node1 || !node2) return 0
        return node1.position().y - node2.position().y
      })
    })
  })
}

const doSubmit = async (saveAs) => {
  const rootNodeData = getRootNode()?.getData()
  if (!rootNodeData) {
    console.log('Could not found rootNode!')
    return
  }

  try {
    configLoading.value = true
    const result = graph.value.toJSON()
    dataStore.dagJson = JSON.stringify(result)
    
    sortDataStore()
    dataStore.status = saveAs

    const res = await cleanUnRefProcNodes(dataStore)
    Object.assign(dataStore, res)
    graph.value.dataStore = dataStore
    
    ElNotification({
      title: '保存成功',
      type: 'success',
      duration: 2000
    })
    refreshGraph()
  } catch (err) {
    console.error('保存失败:', err)
  } finally {
    configLoading.value = false
  }
}


// 添加拖动相关的方法
const startResize = (e) => {
  isResizing.value = true
  
  const handleMouseMove = (moveEvent) => {
    if (isResizing.value) {
      // 计算新的右侧面板宽度
      const containerWidth = document.querySelector('.content').offsetWidth
      const newWidth = containerWidth - moveEvent.clientX
      
      // 设置最小和最大宽度限制
      if (newWidth >= 200 && newWidth <= 500) {
        configWidth.value = newWidth
      }
    }
  }
  
  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    
    // 调整图表大小以适应新的布局
   // if (graph.value) {
   //   const container = document.getElementById('container')
   //   graph.value.resize(container.offsetWidth, container.offsetHeight)
   // }
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  
  // 阻止默认行为和冒泡
  e.preventDefault()
  e.stopPropagation()
}

// 生命周期钩子
onMounted(() => {
  addOrEdit.value = route.query.addOrEdit || 'add'
  procedureId.value = route.query.id || 0
  
  initGraph()
  initStencil()
  initEvents()
  initData()
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped >

.app-container {
  width: 100%;
  height: calc(100vh - 50px);
  padding:0px;
}
.app-container-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

.flow {
  /* width: 100vw; */
  width: 100%;
  height: calc(100vh - 50px);
}
.flow-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  #stencil {
    width: 260px;
    height: 100%;
    position: relative;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
  }

  .panel {
    width: calc(100% - 550px);
    height: 100%;
    background-color: #dff0ff;
  }

  .panel .toolbar {
    width: calc(100% - 567px);
    height: 38px;
    padding-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
   // background-color: #f7f9fb80;
   // border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position:absolute;
    z-index: 1900;
  }
  .toolbar-group {
    border-radius: 20px;
    background-color: #FFFFFF;
    padding: 0px 20px;
  }
  .panel #container {
    width: 100%;
    height: calc(100% - 38px);
  }

// 添加分隔条样式
.resizer {
  width: 4px;
  height: 100%;
  background-color: #e0e0e0;
  cursor: col-resize;
  transition: background-color 0.2s;
  z-index: 1000;
  
  &:hover, &:active {
    background-color: #1890ff;
  }
}

  .config {
    width: 290px;
    height: 100%;
    padding: 0 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    overflow-y: auto;

    :deep(.el-card__body) {
      padding: 20px 0px;
    }
  }

  .node_icon {
    color: darkcyan;
  }
  
</style>