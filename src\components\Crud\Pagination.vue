<!--分页-->
<template>
  <el-pagination
    v-model:page-size="page.size"
    v-model:current-page="page.page"
    :total="page.total"
    style="margin-top: 8px;"
    layout="total, prev, pager, next, sizes"
    @size-change="onSizeChange"
    @current-change="onPageChange"
  />
</template>

<script setup>
import { inject } from 'vue'

// 定义 props
const props = defineProps({
  crud: {
    type: Object,
    default: null
  }
})

// 获取 crud 对象：优先使用 props 传入的，否则从 inject 获取
const crudObj = props.crud || inject('crud')
if (!crudObj) {
  throw new Error('`crud` 未提供，请确保在父组件中通过 provide 注入')
}

// 分页数据
const page = crudObj.page

// 分页大小变化事件
const onSizeChange = (newSize) => {
  page.size = newSize
  crudObj.sizeChangeHandler(newSize)
}

// 页码变化事件
const onPageChange = (newPage) => {
  page.page = newPage
  crudObj.pageChangeHandler(newPage)
}
</script>
