<template>
  <div class="app-container">
    <!-- 工具栏 -->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
      <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
        <el-form-item label="工作站名称" prop="name">
          <el-input
            v-model="query.name"
            clearable
            placeholder="输入工作站名称搜索"
            style="width: 200px;"
            @keyup.enter="crud.toQuery"
          />
        </el-form-item>
        <el-form-item label="更新时间">
          <date-range-picker v-model="query.updateTime" />
        </el-form-item>
        <el-form-item>
          <rrOperation :crud="crud" class="filter-item" />
        </el-form-item>
      </el-form>
    </div>
    </div>

    <crudOperation :crud="crud" :permission="permission" />

    <!-- 表单对话框 -->
    <el-dialog
      align-center
      append-to-body
      :close-on-click-modal="false"
      :before-close="crud.cancelCU"
      :model-value="crud.status.cu > 0"
      :title="crud.status.title"
      width="500px"
    >
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="100px">
        <el-form-item label="工作站名称" prop="name">
          <el-input v-model="form.name" clearable style="width: 300px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="item in dict.job_status"
              :key="item.id"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述信息">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            style="width: 300px;"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 动作队列抽屉 -->
    <el-drawer
      v-model="actionQueueViewerVisible"
      title="工作站执行动作队列缓存"
      direction="rtl"
      :modal="false"
      @opened="onOpenedActionQueueViewer"
    >
      <el-table
        ref="actionQueueTable"
        :data="actionQueue"
        size="small"
        max-height="450"
        style="width: 100%"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div v-for="(item, index) in row.commands" :key="index" class="cmd-log-item">
              <span>{{ item.updateTime }}</span>
              <span>{{ `${item.name} [${item.status}]${item.message ? ': ' + item.message : ''}` }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="enterQueueTime" label="入列时间" width="150" />
        <el-table-column prop="name" label="名称" show-overflow-tooltip />
        <el-table-column prop="taskNumber" label="任务编号" width="120" />
        <el-table-column label="操作" width="80">
          <template #default>
            <el-button size="small" :icon="Delete" type="danger" />
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>

    <!-- 工作站卡片列表 -->
    
    <div class="mock-container">
      <el-card v-for="(item, index) in crud.data" :key="index" class="item-card">
        <template #header>
          <div class="item-header">
            <span class="mock-header-title">{{ item.name }}</span>
            <el-tag :type="item.status === '正常' ? 'success' : 'danger'" effect="plain">
              {{ item.status }}
            </el-tag>
          </div>
        </template>

        <div class="item-body">
          <div class="device-container">
            <div v-for="(dev, devIndex) in item.devices" :key="devIndex" class="device-area">
              <el-image
                :src="getIconUrl(dev.layoutImage || dev.device.layoutImage)"
                fit="contain"
                class="device-image"
              />
            </div>
          </div>
          
          <el-descriptions class="description-container" :column="1" size="small">
            <el-descriptions-item label="描述">{{ item.description || '-' }}</el-descriptions-item>
            <el-descriptions-item label="最后更新">{{ item.updateTime }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <template #footer>
          <div class="item-footer">
            <el-badge v-if="item.actionQueueSize > 0" :value="item.actionQueueSize">
              <el-button size="small" @click="openActionQueueViewer(item.id)">查看队列</el-button>
            </el-badge>
            <udOperation  
              :crud="crud"
              :data="item"
              :permission="permission"
              class="operation-buttons"
            />
          </div>
        </template>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { Delete } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'

import crudStation from '@/api/station'
import { useCrud } from '@/hooks/useCrud'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import udOperation from '@/components/crud/UD.operation.vue'
//import udOperation from '@/views/components/UdBlockOperation.vue'
import DateRangePicker from '@/components/DateRangePicker'

const router = useRouter()
const store = useStore()


// 表单初始值
const defaultForm = { 
  id: null, 
  name: null, 
  description: null, 
  status: null, 
  deleteFlag: null, 
  createBy: null, 
  createTime: null, 
  updateBy: null, 
  updateTime: null 
}
const rules = {
  status: [
    { required: true, message: '工作站状态不能为空', trigger: 'blur' }
  ]
}

// CRUD 配置
const { crud, form, query, CRUD } = useCrud({
  title: '工作站管理',
  url: 'api/station',
  idField: 'id',
  sort: 'id,desc',
  defaultForm,
  rules,
  query: {
    name: null,
    status: null,
    updateTime: null
  },
  crudMethod: { ...crudStation }
})
// 响应式数据

const actionQueueViewerVisible = ref(false)
const actionQueue = ref([])
const actionQueueTable = ref(null)

// 权限配置
const permission = {
  add: ['admin', 'station:add'],
  edit: ['admin', 'station:edit'],
  del: ['admin', 'station:del']
}

// Store 计算属性
const imagesUploadApi = computed(() => store.getters.imagesUploadApi)
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)
const baseApi = computed(() => store.getters.baseApi)

// 方法
const getIconUrl = (imagUrl) => {
  return imageAccessUrl.value + imagUrl
}

const openActionQueueViewer = (stationId) => {
  crudStation.queryActionQueueByStationId({ id: stationId }).then(res => {
    actionQueue.value = res
    actionQueueViewerVisible.value = true
  })
}

const onOpenedActionQueueViewer = () => {
  if (actionQueue.value?.length > 0) {
    actionQueueTable.value.toggleRowExpansion(actionQueue.value[0], true)
  }
}

// CRUD 钩子
crud.hooks[CRUD.HOOK.beforeRefresh] = () => {
  crud.page.size = 100
  crud.sort = 'id,asc'
  return true
}

crud.hooks[CRUD.HOOK.beforeToAdd] = () => {
  router.push('/config-list/station-editor')
  return false
}

crud.hooks[CRUD.HOOK.beforeToEdit] = (crud) => {
  router.push({
    path: '/config-list/station-editor',
    query: { id: crud.form.id, form: crud.form, addOrEdit: 'edit' }
  })
  return false
}

crud.hooks[CRUD.HOOK.afterDeleteError] = (crud, row, resp) => {

  // 获取工作站名称
  const stationName = row.name || '未知工作站'
  
  // 构建引用列表HTML
  let referencesHtml = ''
  if (resp && resp.data && Array.isArray(resp.data)) {
    referencesHtml = '<ul style="padding-left: 20px; margin: 5px 0;">'
    resp.data.forEach(item => {
      referencesHtml += `<li>${item.procedureName} > ${item.methodName} > ${item.stepName} > ${item.actionName}</li>`
    })
    referencesHtml += '</ul>'
  }
  
  // 显示错误通知
  ElNotification({
    title: '删除失败',
    message: `工作站 <strong>${stationName}</strong> 已被如下流程引用，请删除对应引用后再删除工作站${referencesHtml}`,
    type: 'error',
    dangerouslyUseHTMLString: true,
    duration: 5000
  })
  return false
}

onMounted(() => {
  crud.refresh()
})
</script>

<style lang="scss" scoped>
.mock-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  font-size: small;
}
.item-card {
  width: 200px;
  margin: 4px;
  
}
.item-card :deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.item-header {
  width: 100%;
  display: flex;
  flex-direction: row;
}
.mock-header-title {
  font-weight: 600;
  line-height: 150%;
}
.item-body {
  flex: 1;
}
.item-footer {
  display: flex;
  flex-direction: row;
  align-items: end;
  margin-top: 10px;
}
.item-footer:deep(.el-button-group) {
  margin-left: auto;
}
.mock-container:deep(.el-divider--horizontal) {
  margin-bottom: 10px;
}
.mock-container:deep(.el-card__body) {
  padding-bottom: 4px;
}
.device-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  background-color: antiquewhite;
  padding:4px 4px 0px 4px;
  margin-bottom: 4px;
}
.device-area {
  width: 50px;
  height: 50px;
}
.device-image {
    display: block;
    width: 40px;
    height: 40px;
    background-repeat: no-repeat;
    background-position: center;
    background-size:contain;
  }
.description-container {
  font-size: smaller;
  line-height: 150%;
}
.description-container .description-label {
  font-weight: 600;
  padding-right: 10px;
}
  .badge-item {
    margin-top: 10px;
    margin-right: 40px;
  }
.cmd-log-item {
  padding-left: 30px;
}
</style>

