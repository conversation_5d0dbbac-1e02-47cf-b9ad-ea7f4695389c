import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/sampleCategory',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/sampleCategory/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sampleCategory',
    method: 'put',
    data
  })
}

export function selectSampleCategories(params) {
  return request({
    url: 'api/sampleCategory/selectSampleCategories',
    method: 'get',
    params
  })
}

export default { add, edit, del, selectSampleCategories }
