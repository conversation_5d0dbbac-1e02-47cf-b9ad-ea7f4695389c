<template>
  <div class="button-wrapper">
    <div 
      :class="[isDisabled ? 'background-container-disabled' : 'background-container']"
      @click="handleTaskControl"
    >
      <div v-if="!taskStartedStatus" class="button-icon button-icon-pause" :title="tips" />
      <div v-if="taskStartedStatus" class="button-icon button-icon-start" :title="tips" />
    </div>

    <div v-if="!taskStartedStatus" class="status-text"><span>任务暂停</span></div>
    <div v-if="taskStartedStatus" class="status-text"><span>任务开启</span></div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElNotification } from 'element-plus'

const store = useStore()

// 计算属性
const motion = computed(() => store.getters.motion)
const task = computed(() => store.getters.task)

const isDisabled = computed(() => {
  if (store.state.globalSetting.globalSetting.debugMode){
    return false
  }
  return !(
    store.state.task.ctrlStatus.doorStatus ==='LOCKED'
  ) || 
 !( store.state.task.ctrlStatus.needToConfirmIndicator===false)
})

const tips = computed(() => {
  let msg = ''
  if (isDisabled.value && store.state.task.ctrlStatus.needToConfirmIndicator===true) {
    msg += '请确认所有物料已正确复位; '
  }
  if (isDisabled.value && store.state.task.ctrlStatus.doorStatus !=='LOCKED') {
    msg += '请确认门已关闭锁定; '
  }
  if (msg === '') {
    msg = taskStartedStatus.value ? '点击暂停任务':'点击开启任务'
  }
  return msg
})

const doorStatus = computed(() => store.state.task.ctrlStatus.doorStatus)
const taskStartedStatus = computed(() => store.state.task.ctrlStatus.isTaskStarted)
const statusText = computed(() => {
  const status = store.state.task.ctrlStatus.isTaskStarted
  if (status === true) {
    return '任务开启'
  }else {
    return '任务暂停'
  }
})
const backgroundStyle = computed(() => ({
  backgroundImage: `url(${import('@/assets/icons/svg/status-btn-bg.svg')})`
}))

const buttonStartIconStyle = computed(() => ({
  backgroundImage: `url(${import('@/assets/images/start.svg')})`
}))

const buttonPauseIconStyle = computed(() => ({
  backgroundImage: `url(${import('@/assets/images/pause.svg')})`
}))

// 方法
const handleTaskControl = () => {
  if (isDisabled.value) return
  
  if (task.value.started) {
    pauseTask()
  } else {
    startTask()
  }
}

const startTask = async () => {
  try {
    await store.dispatch('setStarted', true)
    ElNotification.success({title:'任务已启动'})
  } catch (error) {
    ElNotification.error({title:'任务启动失败'})
  }
}

const pauseTask = async () => {
  try {
    await store.dispatch('setStarted', false) 
    ElNotification.success({title:'任务已暂停'})
  } catch (error) {
    ElNotification.error({title:'任务暂停失败'})
  }
}

// 生命周期
onMounted(() => {
  store.dispatch('getCurrentTaskGlobalInfo')
})
</script>

<style lang="scss" scoped>

  .button-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center; /* 水平居中 */
    gap: 10px; /* 按钮和文本之间的间距 */
  }

  .background-container {
    width: 60px; /* 宽度 */
    height: 60px; /* 高度 */
    border-radius: 50%; /* 圆形 */
    background-size: cover; /* 背景图片适应按钮 */
    background-position: center; /* 背景图片居中 */
    background-image:none;
    border: 2px solid #59AED2; /* 边框颜色和样式 */
    display: flex;
    align-items: center; /* 内部内容垂直居中 */
    justify-content: center; /* 内部内容水平居中 */
    cursor: pointer; /* 鼠标移到按钮上变为手指 */
  }
  .background-container:hover {
    background-image: radial-gradient(circle at bottom, #FFFFFF,#399AC3 75%, #0580B4 125%,);
  }

  .button-icon {
    width: 40px;
    height: 40px;
    background-size: contain;
  }

  .button-icon-pause {
    background-image: url('@/assets/images/pause.svg');
  }
  .button-icon-start {
    background-image: url('@/assets/images/start.svg');
  }

  .background-container-disabled {
    width: 60px; /* 宽度 */
    height: 60px; /* 高度 */
    border-radius: 50%; /* 圆形 */
    background-size: cover; /* 背景图片适应按钮 */
    background-position: center; /* 背景图片居中 */
    background-image:none;
    border: 2px solid #858585; /* 边框颜色和样式 */
    display: flex;
    align-items: center; /* 内部内容垂直居中 */
    justify-content: center; /* 内部内容水平居中 */
    cursor: pointer; /* 鼠标移到按钮上变为手指 */
  }
  .status-text {
    font-size: 16px;
    font-weight: bold;
    color:#4f96c5;
  }
  .status-text-disabled {
    font-size: 16px;
    font-weight: bold;
    color:#888888;
  }
  .img{
    width:40px;
    height:40px;
    color:red;
    fill: #878787;
  }

</style>
