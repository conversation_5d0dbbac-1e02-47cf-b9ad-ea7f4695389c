import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/procedureVariable',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/procedureVariable/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/procedureVariable/updateProcedureVariablesValue',
    method: 'put',
    data
  })
}
export function updateProcedureVariablesValue(data) {
  return request({
    url: 'api/procedureVariable/updateProcedureVariablesValue',
    method: 'put',
    data
  })
}

export function queryAllRawProceduresWithVariables(params) {
  return request({
    url: 'api/procedureVariable/queryAllRawProceduresWithVariables',
    method: 'get',
    params
  })
}

export default { add, edit, del, queryAllRawProceduresWithVariables, updateProcedureVariablesValue }
