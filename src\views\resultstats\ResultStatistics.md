# ResultStatistics 检测结果统计组件

一个功能完整的检测结果统计分析组件，提供多种图表类型和交互功能。

## 🎯 功能特性

### 📊 多种图表类型
- **检测值趋势图**：折线图显示检测值随时间的变化趋势
- **检测方法统计**：柱状图统计不同检测方法的使用情况
- **检测结论分布**：饼图展示检测结论的分布比例
- **样品检测热力图**：热力图显示不同时间段的检测活动密度

### 🔍 数据筛选功能
- **全局筛选**：时间范围、检测方法、样品类型
- **图表级筛选**：每个图表独立的筛选控件
- **实时更新**：筛选条件变化时图表自动更新

### 🖥️ 交互功能
- **图表放大**：支持单个图表全屏显示
- **响应式布局**：自适应不同屏幕尺寸
- **数据刷新**：手动或自动刷新数据

## 📁 文件结构

```
src/views/testresult/
├── ResultStatistics.vue          # 主组件
├── ResultStatistics.example.vue  # 使用示例
└── ResultStatistics.md          # 文档说明
```

## 🚀 快速开始

### 基本使用

```vue
<template>
  <div>
    <ResultStatistics />
  </div>
</template>

<script setup>
import ResultStatistics from '@/views/testresult/ResultStatistics.vue'
</script>
```

### 带配置使用

```vue
<template>
  <div>
    <ResultStatistics
      :api-url="statisticsApi"
      :auto-refresh="true"
      :refresh-interval="60000"
      :default-filters="defaultFilters"
      @filter-change="handleFilterChange"
      @chart-expand="handleChartExpand"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ResultStatistics from '@/views/testresult/ResultStatistics.vue'

const statisticsApi = ref('/api/statistics/test-results')
const defaultFilters = ref({
  dateRange: ['2024-01-01', '2024-12-31'],
  taskMethodName: 'standard'
})

const handleFilterChange = (filters) => {
  console.log('筛选条件变化:', filters)
}

const handleChartExpand = (chartType) => {
  console.log('图表展开:', chartType)
}
</script>
```

## 📊 图表详细说明

### 1. 检测值趋势图 (Line Chart)
- **功能**：显示特定检测物的数值变化趋势
- **筛选**：支持选择不同检测物（pH值、溶解氧、浊度、温度等）
- **特点**：
  - 平滑曲线显示
  - 面积填充效果
  - 数据点标记
  - 缩放和平移支持

### 2. 检测方法统计 (Bar Chart)
- **功能**：统计不同检测方法的使用情况
- **筛选**：支持切换统计类型（检测次数/异常次数）
- **特点**：
  - 圆角柱状图
  - 数值标签显示
  - 颜色区分统计类型
  - 坐标轴标签旋转

### 3. 检测结论分布 (Pie Chart)
- **功能**：展示检测结论的分布比例
- **类型**：正常、轻微异常、严重异常、需复检
- **特点**：
  - 环形饼图设计
  - 颜色编码结论类型
  - 图例显示
  - 悬停高亮效果

### 4. 样品检测热力图 (Heatmap)
- **功能**：显示不同时间段的检测活动密度
- **筛选**：支持三种时间粒度（按小时、按天、按周）
- **特点**：
  - 渐变色彩映射
  - 可视化映射组件
  - 数据标签显示
  - 悬停提示信息

## 🎨 样式定制

### 网格布局
```scss
.charts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  
  // 响应式布局
  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}
```

### 图表卡片
```scss
.chart-card {
  height: 400px;
  
  &.expanded {
    height: 80vh;
  }
}
```

### 自定义主题
组件支持通过CSS变量自定义主题：

```scss
:root {
  --chart-primary-color: #409EFF;
  --chart-success-color: #67C23A;
  --chart-warning-color: #E6A23C;
  --chart-danger-color: #F56C6C;
}
```

## 🔧 配置选项

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| apiUrl | String | '' | 数据接口地址 |
| autoRefresh | Boolean | false | 是否自动刷新 |
| refreshInterval | Number | 30000 | 刷新间隔(ms) |
| defaultFilters | Object | {} | 默认筛选条件 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| filter-change | (filters: Object) | 筛选条件变化 |
| chart-expand | (chartType: String) | 图表展开 |
| data-refresh | () | 数据刷新 |

### Methods

| 方法名 | 参数 | 说明 |
|--------|------|------|
| refreshData | () | 刷新数据 |
| expandChart | (chartType: String) | 展开图表 |
| resetFilters | () | 重置筛选 |

## 📱 响应式设计

组件采用响应式网格布局，在不同屏幕尺寸下自动调整：

- **桌面端** (>1200px)：2x2网格布局
- **平板端** (≤1200px)：单列布局
- **移动端**：优化触摸交互

## 🔌 数据接口

### 接口格式
```typescript
interface StatisticsData {
  lineChart: {
    dates: string[]
    values: number[]
  }
  barChart: {
    methods: string[]
    counts: number[]
    abnormals: number[]
  }
  pieChart: {
    normal: number
    abnormal: number
    severe: number
    recheck: number
  }
  heatmap: {
    data: [number, number, number][] // [x, y, value]
  }
}
```

## 🛠️ 技术栈

- **Vue 3** - 组合式API
- **Element Plus** - UI组件库
- **ECharts** - 图表库
- **SCSS** - 样式预处理器

## 📝 使用示例

查看 `ResultStatistics.example.vue` 文件获取完整的使用示例和API文档。

## 🔄 更新日志

### v1.0.0
- ✅ 基础图表功能实现
- ✅ 数据筛选功能
- ✅ 图表放大功能
- ✅ 响应式布局
- ✅ 完整文档和示例

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
