
import { createI18n } from 'vue-i18n'
import Cookies from 'js-cookie'
import request from '@/utils/request'

import elEn from 'element-plus/es/locale/lang/en';
import elZh from 'element-plus/es/locale/lang/zh-cn';

const messages = {
  'en-US': {
    ...elEn
  },
  'zh-CN': {
    ...elZh
  }
}

request({
  url: 'file/lang.js',
  method: 'get'
}).then(res => {
  for (const key in messages) {
    if (res[key]) {
      Object.assign(messages[key], res[key])
    }
  }

})

export function getLanguage() {
  const storedLang = Cookies.get('language')
  if (storedLang) {
    return storedLang
  }

  const lang = (navigator.language || navigator.browerLanguage)
  const locales = Object.keys(messages)
  for (const locale of locales) {
    if (lang.indexOf(locale) > -1) {
      return locale
    }
  }

  return 'zh-CN'
}

const i18n = createI18n({
  legacy: false, // 组合API模式
  globalInjection: true, // 全局模式，可以直接使用 $t
  locale: getLanguage(),
 // locale: 'zh-CN',
  messages
})


export default i18n
