<template>
  <div>
    <el-dialog
      align-center
      v-model="dialogVisible"
      :close-on-click-modal="false"
      title="锁选择"
      width="440px"
    
    >
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="锁模式">
          <el-select v-model="form.lockMode" clearable size="small" placeholder="请选择锁模式">
            <el-option label="阻塞" value="BLOCK" />
            <el-option label="非阻塞" value="UNBLOCK" />
          </el-select>
        </el-form-item>
        <el-form-item label="锁对象类型">
          <el-select v-model="form.lockObjectType" clearable size="small" placeholder="请选择锁对象类型">
            <el-option label="设备实例" value="DEV_INSTANCE" />
            <el-option label="设备点位" value="DEV_INSTANCE_POS" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备实例" prop="deviceInstanceName">
          <el-input v-model="form.deviceInstanceName" placeholder="请输入内容" class="input-with-select">
            <template #append>
              <el-popover
                placement="right"
                :width="840"
                :height="500"
                @show="openDialog"
                @hide="onDevInstanceBlur(form)"
                trigger="click">
                <template #default>
                  <div class="dev-graph-container">
                    <div id="main_board_container" ref="mainBoardContainerRef" />
                  </div>
                </template>
                <template #reference>
                  <el-button :icon="Search" ></el-button>
                </template>
              </el-popover>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="点位名称">
          <el-select v-model="form.name" clearable size="small" placeholder="类型">
            <el-option v-for="item in form.posNames" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="text" @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSelection">确认</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, inject, defineModel } from 'vue'
import { Graph } from '@antv/x6'
import { Selection } from '@antv/x6-plugin-selection'
import { Search } from '@element-plus/icons-vue'
import { register } from '@antv/x6-vue-shape'
import { getLatest } from '@/api/deviceLayout'
import DeviceNode from '@/views/devicelayout/DeviceNode.vue'

// 注册自定义节点
register({
  shape: 'device-node',
  width: 212,
  height: 48,
  component: DeviceNode
})


// 使用 defineModel 替代 props
const curData = defineModel('curData', {
  default: () => ({})
})

const dialogVisible = defineModel('dialogVisible', {
  default: () => (false)
})


// 注入
const getDataStore = inject('getDataStore', () => ({}))

// 响应式数据
const defaultForm = { 
  id: null, 
  name: null, 
  description: null, 
  deviceInstanceName: null, 
  deviceInstanceId: null, 
  lockMode: 'BLOCK',
  lockObjectType: 'DEV_INSTANCE',
  status: null, 
  deleteFlag: null, 
  createBy: null, 
  createTime: null, 
  updateBy: null, 
  updateTime: null 
}

const form = reactive({ ...defaultForm })
const rules = reactive({
  deviceInstanceName: [
    { required: true, message: '设备实例名称不能为空', trigger: 'blur' }
  ]
})

const formRef = ref(null)
const popoverVisible = ref(false)
const graph = ref(null)
const selectedNode = ref(null)
const layoutDataStore = ref({})
const dataStore = ref({})
const initing = ref(false)
const devPosition = ref([{ xpos: 0.0, ypos: 0.0, zpos: 0.0 }])

// 监听器
watch(() => curData.value, () => {
  transformNodeToFormData()
}, { deep: false })
watch(() => dialogVisible.value, () => {
  if (dialogVisible.value) {
    init()
  }
})

//watch(() => form, () => {
//  transformFormDataToNode()
//}, { deep: true })

// 生命周期钩子
onMounted(() => {
  transformNodeToFormData()
  
  getLatest(null).then(res => {
    if (res == null) {
      layoutDataStore.value = {}
    } else {
      layoutDataStore.value = res
    }
  })
})

// 方法
function init() {
 // initing.value = true
}

function initGraph() {
  if (initing.value) {
    return
  }
  initing.value = true

  const container = document.getElementById('main_board_container')
  console.log('container.offsetWidth=' + container.offsetWidth + ', container.offsetHeight=' + container.offsetHeight + ', container.clientHeight=' + container.clientHeight)
  
  graph.value = new Graph({
    container: container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    autoResize: true,
    background: false,
    snapline: false,
    interacting: false,
    connecting: {
      snap: true,
      allowBlank: false,
      allowMulti: true,
      allowLoop: true,
      highlight: true,
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#5F95FF',
              stroke: '#5F95FF'
            }
          }
        }
      },
      router: {
        name: 'orth'
      },
      connector: {
        name: 'rounded',
        args: {
          radius: 8
        }
      }
    },
    panning: {
      enabled: true
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    }
  })

  graph.value.use(new Selection({
    rubberband: true,
    showNodeSelectionBox: true,
    multiple: false
  }))

  if (layoutDataStore.value.layoutSvg) {
    graph.value.fromJSON(JSON.parse(layoutDataStore.value.layoutSvg))
  }

  // 无点位设备实例，不显示
  graph.value.getNodes().forEach(node => {
    const storeData = getLayoutStoreObjectByNodeId(node.id)
    if (storeData ) {
      node.show()
    } else {
      node.hide()
    }
  })

  graph.value.zoomToFit({ 
    padding: 10, 
    minScale: 0.8,
    maxScale: 4 
  })

  graph.value.centerContent()

  graph.value.on('cell:selected', ({ cell }) => {
    if (cell.isNode()) {
      selectedNode.value = cell
    }
  })

  graph.value.on('cell:unselected', () => {
    selectedNode.value = null
  })
}

function transformNodeToFormData() {
  const dataParameter = curData.value.sourceParameter
  if (dataParameter) {
    const paramObj = JSON.parse(dataParameter)
    Object.assign(form, paramObj)
  }
}

function transformFormDataToNode() {

  curData.value.sourceParameter = JSON.stringify(form)
  curData.value.name = form.lockObjectType +':'+ form.deviceInstanceName
}

function getStoreObjectByNodeId(nodeId) {
  if (dataStore.value.dagNodeId === nodeId) {
    return dataStore.value
  }
  
  if (!Array.isArray(dataStore.value.actions)) {
    return null
  }
  
  for (const actionObj of dataStore.value.actions) {
    if (actionObj.dagNodeId === nodeId) {
      return actionObj
    }
    
    if (!Array.isArray(actionObj.commands)) {
      continue
    }
    
    for (const cmdObj of actionObj.commands) {
      if (cmdObj.dagNodeId === nodeId) {
        return cmdObj
      }
    }
  }

  return null
}

function getLayoutStoreObjectByNodeId(nodeId) {
  if (!layoutDataStore.value.deviceInstanceList) {
    return null
  }
  
  for (const devInsObj of layoutDataStore.value.deviceInstanceList) {
    if (devInsObj.layoutNodeId === nodeId) {
      return devInsObj
    }
  }

  return null
}

function openDialog() {
  initGraph()
}

const onDevInstanceBlur = (form) => {
 // dialogVisible.value = false
  submitPosSelection()
}
function submitPosSelection() {
  if (!selectedNode.value) {
    return
  }
  
  const devInst = getLayoutStoreObjectByNodeId(selectedNode.value.id)
  if (!devInst || !devInst.positions) {
    return
  }
  
  const selectedPositions = devInst.positions
  
  const nameArr = []
  for (const pos of selectedPositions) {
    if (nameArr.indexOf(pos.name) < 0) {
      nameArr.push(pos.name)
    }
  }

  form.posNames = nameArr
  if (nameArr.length > 0) {
    form.name = form.posNames[0]
  }
  form.deviceInstanceName = devInst.name
  form.deviceInstanceId = devInst.id

}

const submitSelection = () => {
  if (formRef.value) {
    formRef.value.validate((valid) => {
      if (valid) {
        transformFormDataToNode()
        dialogVisible.value = false
      }
    })
  }
}

</script>

<style lang="scss">
.dev-graph-container {
  width: 800px;
  height: 500px;
}

.dev-graph-container #main_board_container {
  height: 100%;
  background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
  background-size: cover;
}
</style>
