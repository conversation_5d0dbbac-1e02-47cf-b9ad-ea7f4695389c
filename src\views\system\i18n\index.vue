<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item :label="$t('消息键')" prop="key">
            <el-input
              v-model="query.key"
              clearable
              :placeholder="$t('消息键')"
              style="width: 185px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('sys.language')" prop="locale">
            <el-select v-model="query.locale" filterable clearable placeholder="请选择" style="width: 185px;">
              <el-option
                v-for="item in dict.data.languages"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('消息体')" prop="message">
            <el-input
              v-model="query.message"
              clearable
              :placeholder="$t('消息体')"
              style="width: 185px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('更新时间')" prop="updateTime">
            <date-range-picker v-model="query.updateTime" />
          </el-form-item>
          <el-form-item>
            <rr-operation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
    <crud-operation :crud="crud" :permission="permission">
      <template #right>
        <el-button
          v-permission="permission.add"
          size="small"
          type="primary"
          :icon="Finished"
          :loading="savingFlag"
          @click="saveI18nData"
        >
          {{ $t('保存') }}
        </el-button>
      </template>
    </crud-operation>
    
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="crud.data"
      size="small"
      border
      style="width: 100%;"
      :span-method="mergeRows"
      @selection-change="crud.selectionChangeHandler"
      @cell-dblclick="handleCellDbClick"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="key" :label="$t('消息键')">
        <template #default="scope">
          <el-input
            v-if="scope.row.keyShowEditor"
            v-model="scope.row.key"
            size="small"
            @blur="handleInputBlur(scope.row, 'key')"
          />
          <span v-else>{{ scope.row.key }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="locale" :label="$t('sys.language')">
        <template #default="scope">
          <el-select
            v-if="scope.row.localeShowEditor"
            v-model="scope.row.locale"
            placeholder="请选择"
            @change="handleInputBlur(scope.row, 'locale')"
          >
            <el-option
              v-for="item in dict.data.languages"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span v-else>{{ dict.label.languages[scope.row.locale] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="message" :label="$t('消息体')">
        <template #default="scope">
          <el-input
            v-if="scope.row.messageShowEditor"
            v-model="scope.row.message"
            size="small"
            @blur="handleInputBlur(scope.row, 'message')"
          />
          <span v-else>{{ scope.row.message }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="description" :label="$t('描述')">
        <template #default="scope">
          <el-input
            v-if="scope.row.descriptionShowEditor"
            v-model="scope.row.description"
            size="small"
            @blur="handleInputBlur(scope.row, 'description')"
          />
          <span v-else>{{ scope.row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="$t('创建时间')" />
      <el-table-column prop="updateTime" :label="$t('更新时间')" />

    </el-table>
    <!--分页组件-->
    <pagination :crud="crud" />
  
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useDict } from '@/hooks/useDict'
import { useCrud } from '@/hooks/useCrud'
import crudSysI18n from '@/api/system/sysI18n'
import DateRangePicker from '@/components/DateRangePicker'

import rrOperation from '@/components/crud/RR.operation'
import crudOperation from '@/components/crud/CRUD.operation'
import udOperation from '@/components/crud/UD.operation'
import pagination from  '@/components/crud/Pagination'
import { iconPropType } from 'element-plus/es/utils/index.mjs'
import {
  Finished
} from '@element-plus/icons-vue';

const { dict } = useDict(['languages'])
const generatedId = ref(0)
const savingFlag = ref(false)

// CRUD配置
const defaultForm = reactive({
  id: null,
  key: null,
  locale: 'zh-CN',
  message: null,
  description: null,
  deleteFlag: null,
  createBy: null,
  updateBy: null,
  createTime: null,
  updateTime: null
})

const { crud, CRUD, query } = useCrud({
  title: '国际化管理',
  url: 'api/sysI18n',
  idField: 'id',
  sort: ['key,asc', 'locale,asc'],
  crudMethod: { ...crudSysI18n },
  optShow: {
    add: true,
    edit: false,
    del: true,
    download: true,
    reset: true
  }
}, defaultForm)

const permission = reactive({
  add: ['admin', 'sysI18n:add'],
  edit: ['admin', 'sysI18n:edit'],
  del: ['admin', 'sysI18n:del']
})

// 方法定义
const handleInputBlur = (row, columnName) => {
  row[`${columnName}ShowEditor`] = false
}

const handleCellDbClick = (row, column) => {
  row[`${column.property}ShowEditor`] = true
}

const mergeRows = ({ row, column, rowIndex }) => {
  if (column.property === 'key') {
    if (rowIndex === 0 || crud.data[rowIndex].key !== crud.data[rowIndex - 1].key ) {
      // 查找当前行的name值与下几行的name值是否相同
      let rowspan = 1
      for (let i = rowIndex + 1; i < crud.data.length; i++) {
        if (crud.data[i].key === row.key) {
          rowspan++
        } else {
          break
        }
      }
      return [rowspan, 1]
    } else {
      // 对重复的行返回[0, 0]，表示隐藏单元格
      if (crud.data[rowIndex].key === crud.data[rowIndex - 1].key) {
        return [0, 0]
      }
    }
  }
  return [1, 1]
}

const saveI18nData = async () => {
  savingFlag.value = true
  try {
    await crudSysI18n.batchSave(crud.data)
    crud.notify('保存成功', 'success')
    crud.refresh()
  } finally {
    savingFlag.value = false
  }
}

// CRUD钩子
crud.hooks[CRUD.HOOK.beforeRefresh] = () => true

crud.hooks[CRUD.HOOK.beforeToAdd] = () => {
  const newData = {
    ...defaultForm,
    keyShowEditor: true,
    localeShowEditor: true,
    messageShowEditor: true,
    descriptionShowEditor: true,
    id: --generatedId.value
  }
  crud.data.push(newData)
  return false
}

onMounted(() => {
  crud.refresh()
})
</script>

<style  rel="stylesheet/scss" lang="scss" scoped>
.el-table__header :deep(th.el-table__cell) {
  border-top: 2px solid #464646;
}
</style>
