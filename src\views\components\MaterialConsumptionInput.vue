<template>
  <div class="material-consumption-container">
    <el-select 
      v-model="selectedMaterial" 
      filterable 
      clearable
      value-key="id"
      placeholder="选择物料" 
      @change="handleMaterialChange"
      class="material-select">
      <el-option
        v-for="item in materialList"
        :key="item.id"
        :label="item.name"
        :value="item"
      />
    </el-select>
    <el-input 
      v-model="quantity" 
      placeholder="数量" 
      @input="updateModelValue"
      class="quantity-input" />
    <span> {{ unit }}</span>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, defineModel } from 'vue'
import crudMaterial from '@/api/material'

// 定义v-model
const modelValue = defineModel({
  type: String,
  default: ''
})

// 响应式状态
const materialList = ref([])
const selectedMaterial = ref(null)
const quantity = ref('')
const unit = ref('')

// 方法
const handleMaterialChange = (material) => {
  if (material) {
    unit.value = material.unit || ''
  } else {
    unit.value = ''
  }
  updateModelValue()
}

const updateModelValue = () => {

  if (selectedMaterial.value ) {
    // 格式化为 "物料ID:物料名称:数量:单位"
    modelValue.value = `${selectedMaterial.value.id}:${selectedMaterial.value.name}:${quantity.value}:${unit.value}`
  } else {
    modelValue.value = ''
  }
}

const parseModelValue = () => {
  selectedMaterial.value = null
  quantity.value = ''
  unit.value = ''
  if (!modelValue.value) return
  
  const parts = modelValue.value.split(':')
  if (parts.length >= 4) {
    const materialId = parts[0]
    const materialName = parts[1]
    quantity.value = parts[2]
    unit.value = parts[3]
    
    // 查找对应的物料对象
    const material = materialList.value.find(m => m.id == materialId)
    if (material) {
      selectedMaterial.value = material
    } else {
      // 如果物料列表中没有找到，创建一个临时对象
      selectedMaterial.value = {
        id: materialId,
        name: materialName,
        unit: unit.value
      }
    }
  }
}

// 加载物料列表
const loadMaterialList = async () => {
  try {

    const res = await crudMaterial.queryTopByStatus({topCount:100 })
    if (res && Array.isArray(res)) {
      materialList.value = res
    }
  } catch (error) {
    console.error('加载物料列表失败:', error)
  }
}

// 监听器
watch(modelValue,() =>{

 parseModelValue()
})

// 生命周期钩子
onMounted(async () => {
  await loadMaterialList()

  parseModelValue()
})
</script>

<style lang="scss" scoped>
.material-consumption-container {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .material-select {
    min-width: 100px;
  }
  
  
  .unit-select {
    width: 80px;
  }
}
</style>