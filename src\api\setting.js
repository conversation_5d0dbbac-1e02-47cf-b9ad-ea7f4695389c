import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/setting',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/setting/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/setting',
    method: 'put',
    data
  })
}

export function queryLatestOne(params) {
  return request({
    url: 'api/setting/queryLatestOne',
    method: 'get',
    params
  })
}

export default { add, edit, del, queryLatestOne }
