<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryForm" :model="query" size="default" :inline="true">
          <el-form-item label="变量名称">
            <el-input
              v-model="query.name"
              clearable
              placeholder="变量名称"
              style="width: 185px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="query.type" clearable placeholder="类型" style="width: 120px">
              <el-option value="String" />
              <el-option value="Number" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <rr-operation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
    <crud-operation :permission="permission" :crud="crud" >
      <template #right>
          <el-button
            v-permission="permission.add"
            size="small"
            plain
            :icon="RefreshLeft"
            :loading="loadFromProcedureConfigLoadingFlag"
            @click="loadFromProcedureConfig"
          >
            {{ $t('从流程配置中加载') }}
          </el-button>
        </template>
    </crud-operation>
    <!--表单组件-->
    <el-dialog
      align-center
      :model-value="crud.status.cu > 0"
      :title="crud.status.title"
      width="600px"
      :close-on-click-modal="false"
      @close="crud.cancelCU"
      class="formula-editor-dialog"
    >
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px" class="formula-form">
        <el-form-item label="变量名称">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="类型" >
            <el-option value="String" />
            <el-option value="Number" />
          </el-select>
        </el-form-item>
        <el-form-item label="显示名">
          <el-input v-model="form.viewName" />
        </el-form-item>
        <el-form-item label="变量描述">
          <el-input v-model="form.description" />
        </el-form-item>
        <el-form-item label="范围">
          <el-select v-model="form.scope" placeholder="范围" >
            <el-option value="USER" />
            <el-option value="SYS" />
          </el-select>
        </el-form-item>
        <el-form-item label="可被用户设置">
          <el-switch v-model="form.valueSetByUser"  active-value="Y" inactive-value="N" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="footer-inner-container">
          <el-button text @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" :icon="Finished" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </template>
    </el-dialog>
    <!--表格渲染-->
    <el-table
      ref="tableRef"
      v-loading="crud.loading"
      :data="crud.data"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="变量名称" sortable />
      <el-table-column prop="type" label="变量类型" />
      <el-table-column prop="viewName" label="显示名" />
      <el-table-column prop="description" label="变量描述" />
      <el-table-column prop="scope" label="范围" />
      <el-table-column prop="source" label="来源" />
      <el-table-column prop="valueSetByUser" label="可被用户设置" />
      <el-table-column prop="createTime" label="创建时间"  width="120"/>
      <el-table-column prop="updateTime" label="更新时间"  width="120"/>
      <el-table-column v-if="checkPer(['admin','formula:edit','formula:del'])" label="操作" width="150px" align="center">
        <template #default="{ row }">
          <ud-operation :crud="crud" :data="row" :permission="permission" />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination :crud="crud" />
    
  </div>
</template>

<script setup>
import { ref, inject, onMounted, watch } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { useDict } from '@/hooks/useDict'
import crudVariable from '@/api/variable'
import CrudOperation from '@/components/crud/CRUD.Operation.vue'
import RrOperation from '@/components/crud/RR.Operation.vue'
import UdOperation from '@/components/crud/UD.Operation.vue'
import Pagination from '@/components/crud/Pagination.vue'
import { Plus, Delete, Finished, RefreshLeft } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'

const checkPer = inject('checkPer')
// 响应式数据
const { dict, getDictLabel } = useDict(['script_types'])

const formRef = ref(null)
const tableRef = ref(null)

const loadFromProcedureConfigLoadingFlag = ref(false)

// CRUD配置
const defaultForm = {
  id: null,
  type: 'String',
  name: null,
  description: null,
  scope: 'USER',
  source: 'MANUAL',
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const permission = {
  add: ['admin', 'formula:add'],
  edit: ['admin', 'formula:edit'],
  del: ['admin', 'formula:del']
}

const { crud, CRUD, query, form, rules } = useCrud({
  title: '变量管理',
  url: 'api/variable',
  idField: 'id',
  sort: 'id,desc',
  crudMethod: { ...crudVariable },
  defaultForm,
  permission,
  formRef,
  tableRef
})

// 初始化CRUD钩子
crud.hooks[CRUD.HOOK.beforeRefresh] = () => true

crud.hooks[CRUD.HOOK.beforeCrudSubmitCU] = () => {
  const theForm = crud.form
  // debugger逻辑保持
}

const loadFromProcedureConfig = async () => {
  loadFromProcedureConfigLoadingFlag.value = true
  const res = await crudVariable.loadFromProcedureConfig()
  crud.refresh()
  ElNotification.success({title:"加载成功"})
  loadFromProcedureConfigLoadingFlag.value = false
}

watch(() => form.content, (val) => {
  console.log('Watch list form.content:' + val)
})

onMounted(() => {
  crud.toQuery()
})
</script>

<style lang="scss" scoped>
.formula-editor-dialog {
  background: linear-gradient(rgb(33 85 129) 7%, rgb(200 223 246) 7%, rgb(255 255 255) 100%);
  :deep(.el-dialog__body) {
    padding-right: 20px !important;
  } 
}
.formula-form {
  margin-bottom: 0!important;
  padding-right: 20px !important;
}
.footer-inner-container {
  padding-right: 20px !important;
}
</style>
