import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/material',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/material/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/material',
    method: 'put',
    data
  })
}
export function queryTopByStatus(query) {
  return request({
    url: 'api/material/queryTopByStatus',
    method: 'get',
    params: query
  })
}

export function reset(data) {
  return request({
    url: 'api/material/reset',
    method: 'put',
    data
  })
}

export default { add, edit, del, queryTopByStatus, reset }
