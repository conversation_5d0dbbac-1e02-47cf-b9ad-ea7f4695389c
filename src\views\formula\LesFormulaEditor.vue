<template>
  <div class="vue-formula">
    <div class="editor-container">
      <codemirror v-model:value="curForm.content" :options="options" @ready="onCmReady"  height="200" width="100%"/>
    </div>
    <div class="formula-info-container" :data-error="`${validInfo != ''}`">
      <span v-if="validInfo">公式错误：{{ validInfo }}</span>
    </div>
    <div class="operator-container">
      <FieldVariable
        class="field-variable"
        :field-list="fieldList"
        @fieldSelect="onFieldSelect"
      />
      <FormulaList
        :nodes="nodes"
        class="formula-list"
        @formulaClick="onFormulaClick"
        @enterInfo="onEnterInfo"
      />
      <div v-if="currentFormula" class="formula-info">
        <div class="info-text">{{ currentFormula.tip }}</div>
        <div class="info-text">用法：{{ currentFormula.usage }}</div>

        <div class="info-text">示例：{{ currentFormula.example }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import codemirror from 'codemirror-editor-vue3'
import FormulaEditorCore from './core/index'
import FieldVariable from './components/FieldVariable.vue'
import FormulaList from './components/FormulaList.vue'
import crudFormula from '@/api/formula'

// 组件样式
import 'codemirror/lib/codemirror.css'
// 主题
import 'codemirror/theme/3024-day.css'
import 'codemirror/theme/ayu-mirage.css'
import 'codemirror/theme/monokai.css'
import 'codemirror/theme/dracula.css'
// 语言模式
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/addon/hint/show-hint.css'
import 'codemirror/addon/hint/show-hint.js'
// 添加粘贴支持
import 'codemirror/addon/edit/closebrackets.js'
import 'codemirror/addon/edit/matchbrackets.js'
import 'codemirror/addon/edit/continuelist.js'

// 默认字段列表
const defaultFieldList = [
  { fullName: '名称', value: 'string', enCode: 'name' },
  { fullName: '描述', value: 'string', enCode: 'desc' }
]

// 默认公式列表
const defaultFormulaList = [{
  name: '常用函数',
  enCode: 'frequentlyUse',
  formula: [
    { name: 'CONCATENATE', enCode: 'CONCATENATE', tip: '合并多个文本', example: 'CONCATENATE("Hello","World") = "HelloWorld"', usage: 'CONCATENATE(文本1,文本2,...)。' },
    { name: 'SUM', enCode: 'SUM', tip: '求和', example: 'SUM(数学成绩,语文成绩,英语成绩,...) = 各科总成绩', usage: 'SUM(数值1,数值2,...)。' },
    { name: 'DATE', enCode: 'DATE', tip: '返回特定日期', example: 'DATE(2021,1,1)', usage: 'DATE(year,month,day)。' },
    { name: 'IF', enCode: 'IF', tip: '条件判断', example: "IF(成绩>60,'及格','不及格')", usage: 'IF(逻辑语句,真值,假值)。' }
  ]
}]

// Props定义
const props = defineProps({
  curForm: {
    type: Object,
    default: () => ({})
  },
  formulaConf: {
    type: Object,
    default: () => ({})
  }
})

const testValue = ref('')

// 响应式数据
const editorCore = ref(null)
const currentFormula = ref(null)
const validInfo = ref('')
const fieldList = ref([...defaultFieldList])
const formulaList = ref([...defaultFormulaList])
const options = reactive({
  height: 100,
  theme: 'dracula',
  mode: 'text/javascript',
  readOnly: false,
  lineNumbers: true,
  lineWiseCopyCut: true,
  gutters: ["CodeMirror-lint-markers"],
  lint: true,
  autofocus: true,
  autoRefresh: true,
  styleActiveLine: true,
  indentWithTabs: false,
  tabSize: 2,
  indentUnit: 2,
  matchBrackets: true,
  autoCloseBrackets: true,
  viewportMargin: Infinity,
  smartIndent: true, // 上下文缩进
  styleActiveLine: true, // 高亮选中行
  showCursorWhenSelecting: true // 当选择处于活动状态时是否应绘制游标
})
/*
const options = reactive({
  autofocus: true,
  line: true,
  height: 200,
  theme: 'dracula',
  tabSize: 4,
  readOnly: false,
  autorefresh: false,
  smartIndent: true,
  lineNumbers: false,
  styleActiveLine: true,
  showCursorWhenSelecting: true,
  mode: 'text/javascript'

})
  */

// 计算属性
const nodes = computed(() => formulaList.value || [])
const fields = computed(() => fieldList.value || [])

// 监听器
watch(() => props.curForm.content, (val) => {
  console.log('Watch curForm.content:' + val)

  if (!val) {
    validInfo.value = ''
    return
  }
  const { error, message } = editorCore.value.validateFormula(fieldList.value)
  validInfo.value = error ? message : ''
})

// 方法
const reset = () => {
  currentFormula.value = null
  editorCore.value?.reset()
}

const getData = () => editorCore.value?.getData()

const onCmReady = (codemirror) => {
  editorCore.value = new FormulaEditorCore(codemirror, '', formulaList.value)
  editorCore.value.registerListen()
  editorCore.value.renderData(props.formulaConf)
  codemirror.refresh()
}

const onFormulaClick = (formula) => {
  currentFormula.value = formula
  editorCore.value.insertText(`${formula.name}()`, 'formula')
}

const onFieldSelect = (field) => {
  editorCore.value.insertText({ ...field }, 'field')
}

const onEnterInfo = (formulaInfo) => {
  currentFormula.value = formulaInfo
}

// 初始化数据
const getVarAndFuncData = async () => {
  try {
    const res = await crudFormula.selectVariablesAndFunctions(null)
    res.varList.forEach(v => {
      v.enCode = v.name
      v.value = v.type.toLowerCase()
    })
    res.funcList.forEach(func => {
      func.enCode = func.name
    })
    fieldList.value = res.varList
    formulaList.value = res.funcList
  } catch (error) {
    console.error('获取变量和函数数据失败:', error)
  }
}

// 生命周期
onMounted(() => {
  getVarAndFuncData()
})
</script>

<style lang="scss" scoped>
  .vue-formula {
    width: 100%;
    .editor-container {
      position: relative;
      width: 100%;
      height: 200px;
      
      :deep(.CodeMirror) {
        height: 100%;
        font-family: monospace;
        font-size: 14px;
        line-height: 1.5;
        
        .CodeMirror-cursor {
          border-left: 1px solid #fff !important;
          z-index: 10;
        }
      }
    }

    .formula-info-container {
      padding: 0 6px;
      background-color: #faeeee;
      color: #8d3030;
      display: flex;
      height: 40px;
      align-items: center;
      &[data-error='false'] {
        background-color: #f7f7f7;
      }
    }
    .operator-container {
      display: flex;
      flex: 1;
      overflow: hidden;
      height: 150px;
      border: 1px solid #d0d0d0;
      .field-variable {
        height: 100%;
        width: 250px;
      }
      .formula-list {
        height: 100%;
        width: 220px;
      }

      .formula-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 6px;
        .info-text {
          font-size: 12px;
          color: #6b7280;
          margin: 6px 0;
        }
      }
    }
  }

  // 确保全局样式不会影响编辑器
  :deep(.CodeMirror-scroll) {
    overflow: auto !important;
  }

  .CodeMirror-hints {
    z-index: 30000 !important;
    background-color: #f0f0f0;
    color: #333;
    width: 130px;
    font-size: 14px;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
  }
  .cm-string {
    color: #f56c6c !important;
  }
  .cm-field {
    background: #eaf2fd;
    color: #2f7deb !important;
    border-radius: 2px;
    display: inline-block;
    font-size: 14px;
    margin: 0 2px;
    padding: 3px 5px;
  }
</style>
