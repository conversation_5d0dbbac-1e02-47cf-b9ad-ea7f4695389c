import { Graph, Edge, Path } from '@antv/x6'

/**
 * 通过Node节点查询对应的设备实例对象
 */
export function getDeviceInstanceByNode (node)  {
  const curNode = node
  const { graph } = curNode.model || {}

  if (!graph) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === curNode.id) {
      return ins
    }
  }
  return null
}