import { createApp } from 'vue'
import App from './App.vue'

const app = createApp(App)

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css'

import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'


import i18n from '@/lang'
import { ElConfigProvider } from 'element-plus'

//
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'

// 数据字典
import dict from './components/Dict'

// 权限指令
import checkPer from '@/utils/permission'
import permission from './components/Permission'
import './assets/styles/element-variables.scss'
// global css
import './assets/styles/index.scss'

// 代码高亮
import VueHighlightJS from 'vue-highlightjs'
import 'highlight.js/styles/atom-one-dark.css'

import store from './store'
import router from './router/routers'

//import './assets/icons' // icon
import './router/index' // permission control
import 'echarts-gl'
import SvgIcon from '@/components/SvgIcon';
import {registerSvgIcons} from '@/assets/icons'

registerSvgIcons(app)

import 'virtual:svg-icons-register'

import print from 'vue3-print-nb';

app.use(i18n); // 使用 vue-i18n
app.use(ElConfigProvider)
app.use(ElementPlus, {
  i18n: (key, value) => i18n.global.t(key, value),
  size: Cookies.get('size') || 'small' // set element-ui default size
}); // 集成 Element Plus 的国际化

app.use(checkPer)
app.use(VueHighlightJS)
app.use(mavonEditor)
app.use(permission)
app.use(dict)
/*app.use(ElementPlus, {
  size: Cookies.get('size') || 'small' // set element-ui default size
})
*/

app.config.productionTip = false

app.use(router)
app.use(store)

app.mount('#app')
// 使用 provide 注入全局属性
app.provide('globalApp', app)

app.use(print)

export default app