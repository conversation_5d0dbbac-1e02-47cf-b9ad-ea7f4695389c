<template>
<div :class="{ 'none-border':true }">
  <el-popover
        placement="top"
        trigger="hover"
        :width="240"
      >
        <div :id="printId" ref="printRef" class="print-container">
          <div class="qrcode-container">
            <qrcode-vue3 :value="modelTag" :key="modelTag || 'empty'" :size="100"
            :dotsOptions="{
                type: 'square', // 设置点的形状为方块
                color: '#000000',
                gradient: {
                  type: 'linear',
                  rotation: 0,
                  colorStops: [
                    { offset: 0, color: '#000000' },
                    { offset: 1, color: '#000000' },
                  ],
                },
              }"
            style="width:100%;height:100%;" level="H" />
            <div class="qrcode-label">{{ modelTag }}</div>
          </div>
          <div class="print-label-container">
            <div class="label-body">
              <div class="label-sample-name" style="margin-bottom:2px;">{{ modelTag }}</div>
            </div>
            <div class="label-time">{{ moment(new Date()).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
        </div>
        <template #reference>

          <el-input
              v-model="modelTag"
              style="max-width: 600px"
              placeholder="请扫码或打印"
              class="input-with-select"
              :prefix-icon="CollectionTag"
            >
            <template #append>
                <el-button :icon="Printer" v-print="printObj" :key="printId" @click="handlePrintQrCode" />
            </template>
          </el-input>
        </template>
  </el-popover>
</div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed, inject, defineModel, nextTick } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { debounce, createUniqueString } from '@/utils'
import { ElMessage } from 'element-plus'
import { Printer, CollectionTag } from '@element-plus/icons-vue'
import QrcodeVue3 from 'qrcode-vue3'
import print from 'vue3-print-nb'
import moment from 'moment'


const modelTag = defineModel()

const qrcodeValue = computed(() => {
  const tag = modelTag.value
debugger
  return tag
})
const printId = computed(() => {
  return 'printQrCode' + (modelTag.value || 'empty')
})

const printRef = ref(null)
const printQrCodeStyle = reactive({
  display: 'none',
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  zIndex: 9999,
  backgroundColor: '#fff',
  padding: '20px',
  borderRadius: '8px',
})

// 打印配置对象
const printObj = computed(() => ({
  // id: 'printQrCode'+modelTag.value,
  id: printId.value,
  popTitle: '二维码打印',
  extraCss: '',
  extraHead: '<meta http-equiv="Content-Language" content="zh-cn"/>',
  preview: false,
  previewTitle: 'Test Title', // The title of the preview window. The default is 打印预览
  previewBeforeOpenCallback () {
    console.log('正在加载预览窗口')
  },
  previewOpenCallback () {
    console.log('已经加载完预览窗口')
  },
  beforeOpenCallback (vue) {
    console.log('打开之前')
    return false
  },
  openCallback (vue) {
  //  props.row.status = 'READY'
    console.log('执行了打印')
  },
  closeCallback (vue) {
    
    console.log('关闭了打印工具')
  }
}))

// 处理打印二维码
const handlePrintQrCode = () => {
  // 如果标签为空，则生成随机标签
  if (!modelTag.value) {
    modelTag.value = createUniqueString()
  }
  const tt = printObj.value
debugger
  printQrCode()

}

// 执行打印
const printQrCode = () => {
  // 使用 vue-print-nb 打印指定元素
  
  printQrCodeStyle.display = 'block'
 // printRef.value.__vue_print__()
 // print(printRef.value
 // document.getElementById('printQrCode').__vue_print__()

 // print(printRef.value)
}


</script>

<style scoped>
.operation {
  width:18px;
  height:18px;
  padding: 2px 2px;
  margin-left: 4px;
}
.sample-dialog {

}
.el-dialog :deep(.el-dialog__footer) {
  padding-right: 40px !important;
}
.footer-inner-container {
  padding-right: 20px !important;
}
.none-border {
  padding: 2px 2px;
}
.flowing-border {
    position: relative;
    padding: 2px 2px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    background: #333;
    border-radius: 4px;
    overflow: hidden;
  }

  /* 动态边框效果 */
  .flowing-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 4px;
    padding: 1px; /* 边框宽度 */
    background: linear-gradient(90deg,
      #ff0000, #00ff00, #0000ff, #ff0000); /* 渐变色条 */
    background-size: 300% 100%;
    animation: flowing 3s linear infinite;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude; /* 仅保留边框部分 */
  }

  @keyframes flowing {
    from {
      background-position: 0% 50%;
    }
    to {
      background-position: 100% 50%;
    }
  }

  .header-with-tooltip {
    display: flex;
    align-items: center;
    justify-content: start;
  }
  .question-icon {
    margin-left: 4px;
    font-size: 14px;
    color: #909399;
    cursor: pointer;
  }

  .question-icon:hover {
    color: #409EFF;
  }

.print-container {
  display: flex;
  align-items: start;
  justify-content: start;
  width: 260px;
  padding: 25px 10px 10px 14px ;


  .print-label-container {
    width: 156px;
    height: 120px;
    padding-left: 4px;
    display: flex;
    flex-direction: column;
    .label-body {
      flex: 1;
    }
    .label-time {
      font-size: 11px;
      flex: 0;
    }
  }
}

.qrcode-container {
  width: 100px;
  height: 120px;
  :deep(img){
    width: 100px;
    height: 100px;
  }
}

</style>
