<template>
  <div :class="tagsView ? 'app-container-tags' : 'app-container'">
    <div :class="tagsView ? 'flow-tags' : 'flow'">
      <div class="content">
        <!--左侧工具栏-->
        <div id="stencil" class="stencil-station-editor" />
        <div class="panel" :style="{ width: `calc(100% - ${stencilWidth}px - ${configWidth}px)` }">
          <!--流程图工具栏-->
          <div class="toolbar" :style="{ width: `calc(100% - ${stencilWidth}px - ${configWidth}px)` }">
            <el-button-group class="toolbar-group">
              <el-button
                :loading="configLoading"
                type="primary"
                size="small"
                plain
                :icon="Finished"
                @click="doSubmit('DRAFT')"
              >保存</el-button>
              <el-tooltip class="item" effect="dark" content="放大" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomIn"
                  @click="graph?.zoom(0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="缩小" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomOut"
                  @click="graph?.zoom(-0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="调整到合适大小" placement="top">
                <el-button
                  size="small"
                  :icon="Aim"
                  @click="graph?.zoomToFit({ padding: 48, minScale: 0.8, maxScale: 4 });
                          graph?.centerContent()"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除选中的节点" placement="top">
                <el-button
                  size="small"
                  :icon="Delete"
                  @click="removeNodes()"
                />
              </el-tooltip>
            </el-button-group>
          </div>
          <!--流程图画板-->
          <div id="container" />
        </div>
        <!-- 可拖动分隔条 -->
        <Resizer v-model:width="configWidth" />


        <!--右侧工具栏-->
        <el-card class="config" :style="{ width: `${configWidth}px`}">
          <template #header>
            <div class="clearfix">
              <span>节点属性</span>
            </div>
          </template>
          <StationProperty v-if="selectedNode && selectedNode.getData().type=='STATION'" :cur-node="selectedNode" />
          <ActionProperty v-if="selectedNode && selectedNode.getData().type=='ACTION'" :cur-node="selectedNode" />
          <CommandProperty v-if="selectedNode && selectedNode.getData().type=='COMMAND'" :cur-node="selectedNode" :persistent="false" />
        </el-card>

      </div>
    </div>
  </div>

</template>

<script setup>
import { ref, reactive, computed, onMounted, provide } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { ElNotification } from 'element-plus'
import { 
  ZoomIn,
  ZoomOut,
  Aim,
  Delete,
  Finished
} from '@element-plus/icons-vue'

import { Graph, FunctionExt, Shape, Platform, Path, Edge, Node, StringExt } from '@antv/x6'
import { register } from '@antv/x6-vue-shape'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Selection } from '@antv/x6-plugin-selection'
import { Scroller } from '@antv/x6-plugin-scroller'

import { registerDataProcessingDag, isOverlapping, scaleRectangle } from '@/views/components/x6/dag/index'

import StationProperty from './StationProperty.vue'
import ActionProperty from './ActionProperty.vue'
import CommandProperty from './CommandProperty.vue'
import StationNode from './StationNode.vue'

import { createNode as createStationNode, createNode, appendNode } from '@/hooks/useDagNode'
import { findAllDevicesInLatestLayout } from '@/api/deviceLayout'
import { add, edit, queryStationById } from '@/api/station'
import Resizer from '@/components/Resizer'

// 状态定义
const router = useRouter()
const route = useRoute()
const store = useStore()

const graph = ref(null)
const stencil = ref(null)
const selectedNode = ref(null)
const configLoading = ref(false)
const addOrEdit = ref('add')
const stationId = ref(0)
const rootNode = ref(null)

const dataStore = reactive({})

const stencilWidth = ref(260) // 左侧工具栏宽度
const configWidth = ref(290) // 右侧属性面板宽度
const isResizing = ref(false)


const tagsView = computed(() => store.state.settings.tagsView)

// 组件列表
const allDeviceInstanceNodes = ref([])
const selectedDeviceInstanceCmdNodes = ref([])

// stencil 分组配置
const stencilGroups = [
  {
    name: '设备实例',
    collapsable: true,
    graphWidth: '100%',
    graphOptions: { 
      autoResize: true, 
      scroller: {
        enabled: true,
        pannable: true,
        pageVisible: true,
        pageBreak: true
      }
    },
    layoutOptions: {
      columns: 2,
      rowHeight: 120,
      columnWidth: 100,
      marginX: 10,
      marginY: 10,
     // dy: -20,
      resizeToFit: true,
      //start: { x: 10, y: 10 }
      padding: { top: 0, bottom: 0 }
    }
  },
  {
    name: '设备指令',
    collapsable: true,
    graphWidth: '100%',
    graphOptions: { 
      autoResize: true, 
      scroller: {
        enabled: true,
        pannable: true
      }
    },
    layoutOptions: {
      columns: 1,
      rowHeight: 50,
      marginY: 0,
      dy: 0,
      dx: 80
    }
  }
]

// 注册组件
register({
  shape: 'station-node',
  width: 100,
  height: 120,
  component: StationNode,
  ports: {
    groups: {
      in: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: 'transparent',
            strokeWidth: 1,
            fill: 'transparent'
          }
        }
      },
      out: {
        position: {
          name: 'right',
          args: {
            dx: 0,
            dy: 0
          }
        },
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: 'transparent',
            strokeWidth: 1,
            fill: 'transparent'
          }
        }
      }
    }
  }
})

// 注册X6 DAG组件
registerDataProcessingDag()

// 方法定义
const initGraph = () => {
  const container = document.getElementById('container')
  graph.value = new Graph({
    container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    autoResize: true,
    background: false,
    snapline: true,
    panning: {
      enabled: true,
      eventTypes: ['rightMouseDown']
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    },
    grid: {
      type: 'dot',
      size: 10,
      visible: true,
      args: {
        color: '#d0d0d0',
        thickness: 1
      }
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF',
            strokeWidth: 4
          }
        }
      }
    },
    connecting: {
      snap: true,
      allowBlank: false,
      allowMulti: true,
      allowLoop: false,
      highlight: true,
      sourceAnchor: {
        name: 'left',
        args: {
          dx: Platform.IS_SAFARI ? 4 : 8
        }
      },
      targetAnchor: {
        name: 'right',
        args: {
          dx: Platform.IS_SAFARI ? 4 : -8
        }
      },
      createEdge() {
        return graph.value.createEdge({
          shape: 'data-processing-curve',
          attrs: {
            line: {
              strokeDasharray: '5 5'
            }
          },
          zIndex: -1
        })
      },
      validateConnection({ sourceMagnet, targetMagnet }) {
        if (!sourceMagnet || sourceMagnet.getAttribute('port-group') === 'in') {
          return false
        }
        if (!targetMagnet || targetMagnet.getAttribute('port-group') !== 'in') {
          return false
        }
        return true
      }
    }
  })

  graph.value.use(
    new Selection({
      rubberband: true,
      showNodeSelectionBox: false
    })
  )
  .use(
    new Scroller({
      enabled: true
    })
  )
}

const initStencil = () => {
  stencil.value = new Stencil({
    title: '节点选择面板',
    search(cell, keyword) {
      return cell.shape.indexOf(keyword) !== -1
    },
    placeholder: '输入名称查找',
    notFoundText: 'Not Found',
    collapsable: false,
    stencilGraphHeight: 0,
    stencilGraphWidth: 230,
    target: graph.value,
    groups: stencilGroups,
    layoutOptions: {
       columns: 1}
  })

  const stencilContainer = document.getElementById('stencil')
  stencilContainer.appendChild(stencil.value.container)

  const devInsCommonAttrs = {
    body: {
      fill: '#f0fe00',
      stroke: '#8f8f23',
      strokeWidth: 1,
      class: 'node_icon'
    },
    label: {
      refX: '50%',
      refY: '100%',
      refY2: 14,
      height: 24,
      textAnchor: 'middle',
      textVerticalAnchor: 'middle',
      fontSize: 12
    },
    image: {
      fill: '#ff0000',
      stroke: '#ffff00'
    }
  }

  findAllDevicesInLatestLayout(null).then(res => {
    if (res) {
      allDeviceInstanceNodes.value = res.map(ins => {
        const imgRect = scaleRectangle(ins.layoutWidth, ins.layoutHeight, 100, 100)
        return graph.value.createNode({
          shape: 'image',
          width: imgRect.width,
          height: imgRect.height,
          imageUrl: store.getters.imageAccessUrl + ins.layoutImage,
          label: ins.name,
          attrs: devInsCommonAttrs,
          data: { 
            type: 'DEVICE', 
            id: ins.id, 
            name: ins.name, 
            layoutImage: ins.layoutImage, 
            positionConfig: ins.positionConfig,
            commands: ins.commands
          }
        })
      })

      if (allDeviceInstanceNodes.value.length > 0) {
        stencil.value.load(allDeviceInstanceNodes.value, '设备实例')
      }
    }
  })

  refreshStencilCommands()
}
// ... 前面的代码保持不变 ...

const refreshStencilCommands = () => {
  const commonAttrs = {
    body: {
      fill: '#f0fe00',
      stroke: '#8f8f23',
      strokeWidth: 1,
      class: 'node_icon'
    },
    image: {
      fill: '#ff0000',
      stroke: '#ffff00'
    }
  }

  stencil.value?.unload(selectedDeviceInstanceCmdNodes.value, '设备指令')
  if (!dataStore.devices || dataStore.devices.length === 0) {
    return
  }

  selectedDeviceInstanceCmdNodes.value = []
  for (const devInst of dataStore.devices) {
    const cmds = devInst.commands
    for (const cmd of cmds) {
      const cmdNode = graph.value?.createNode({
        shape: 'data-processing-dag-node',
        x: 10,
        attrs: commonAttrs,
        data: { 
          ...cmd, 
          type: 'COMMAND', 
          layoutImage: devInst.layoutImage || devInst.device.layoutImage,
          deviceInstanceId: devInst.id,
          deviceInstanceName: devInst.name,
          failedThen: 'PAUSE' 
        }
      })
      if (cmdNode) {
        selectedDeviceInstanceCmdNodes.value.push(cmdNode)
      }
    }
  }

  stencil.value?.load(selectedDeviceInstanceCmdNodes.value, '设备指令')
}

const initEvents = () => {
  if (!graph.value) return

  // 监听空白点击事件
  graph.value.on('blank:click', () => {
    // Cancelling operations
  })

  // 监听节点选中事件
  graph.value.on('cell:selected', ({ cell }) => {
    if (cell.isNode()) {
      selectedNode.value = cell
      console.log('selectedNode is changed to: ' + cell.id)
    }
  })

  // 节点撤销选中
  graph.value.on('cell:unselected', () => {
    selectedNode.value = null
  })

  // 边鼠标进入事件
  graph.value.on('edge:mouseenter', ({ cell }) => {
    cell.addTools([
      'source-arrowhead',
      {
        name: 'target-arrowhead',
        args: {
          attrs: {
            fill: 'red'
          }
        }
      }
    ])
  })

  // 边鼠标离开事件
  graph.value.on('edge:mouseleave', ({ cell }) => {
    cell.removeTools()
  })

  // 自定义放置节点的动作
  graph.value.on('cell:added', ({ cell }) => {
    if (cell.getData().type === 'ACTION') {
      cell.parentNodeId = getRootNode()?.id
      dataStore.actions.push({ ...cell.getData(), dagNodeId: cell.id })
      return
    }

    if (cell.isNode()) {
      appendGraphNode(cell)
    }
  })

  // 监听节点位置变化事件
  graph.value.on('node:moved', ({ node }) => {
    appendGraphNode(node)
  })
}

const appendGraphNode = (node) => {
  if (!graph.value) return
  console.log('Node added to the graph:', node)

  // 检查新节点是否与现有节点重叠
  const nodes = graph.value.getNodes()
  const newNode = node

  let isCorrect = false
  for (const existingNode of nodes) {
    if (existingNode !== newNode && isOverlapping(newNode, existingNode)) {
      console.log('Overlap detected with node:', existingNode)

      if (existingNode.getData().type === 'STATION' && newNode.getData().type === 'DEVICE') {
        if (existingNode.getData().devices) {
          existingNode.getData().devices.push(newNode.getData())
          const changeState = 'add new device'
          existingNode.setData({ ...existingNode.getData(), changeState })
          dataStore.devices.push(newNode.getData())
        }

        graph.value.select(existingNode)
        graph.value.removeCells([node])
        refreshStencilCommands()
        isCorrect = true
        break
      }

      if (existingNode.getData().type === 'ACTION' && newNode.getData().type === 'COMMAND') {
        if (newNode.ports.items.length === 0) {
          newNode.addPort({ id: newNode.id + '-in', group: 'in' })
        }
        appendNode(existingNode, newNode)

        const actionObj = getStoreObjectByNodeId(existingNode.id)
        if (!actionObj.commands) {
          actionObj.commands = []
        }
        const newActCmd = { 
          dagNodeId: newNode.id,
          deviceInstance: { id: newNode.getData().deviceInstanceId },
          name: newNode.getData().name,
          description: newNode.getData().description,
          failedThen: newNode.getData().failedThen,
          command: newNode.getData() 
        }
        actionObj.commands.push(newActCmd)
        isCorrect = true
        break
      }
    }
  }
}

const removeNodes = () => {
  if (!graph.value) return
  const nodes = graph.value.getSelectedCells()
  
  for (const node of nodes) {
    graph.value.removeCell(node)
    graph.value.removeConnectedEdges(node)
    const nodeType = node.getData().type
    
    const data = getStoreObjectByNodeId(node.id)
    const parentData = getStoreParentObjectByNodeId(node.id)
    
    if (nodeType === 'COMMAND' && parentData) {
      const idxCmd = parentData.commands.findIndex(item => item.dagNodeId === data.dagNodeId)
      if (idxCmd >= 0) {
        parentData.commands.splice(idxCmd, 1)
      }
    }
    if (nodeType === 'ACTION' && parentData) {
      const idxCmd = parentData.actions.findIndex(item => item.dagNodeId === data.dagNodeId)
      if (idxCmd >= 0) {
        parentData.actions.splice(idxCmd, 1)
      }
    }
  }
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  for (const actionObj of dataStore.actions || []) {
    if (actionObj.dagNodeId === nodeId) {
      return actionObj
    }
    for (const cmdObj of actionObj.commands || []) {
      if (cmdObj.dagNodeId === nodeId) {
        return cmdObj
      }
    }
  }
  return null
}

const getStoreParentObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return null
  }
  for (const actionObj of dataStore.actions || []) {
    if (actionObj.dagNodeId === nodeId) {
      return dataStore
    }
    for (const cmdObj of actionObj.commands || []) {
      if (cmdObj.dagNodeId === nodeId) {
        return actionObj
      }
    }
  }
  return null
}

const getRootNode = () => {
  let rootNode = null
  graph.value?.getNodes().forEach((graphNode) => {
    if (graphNode.getData().parentNodeId == null && graphNode.getData().type === 'STATION') {
      rootNode = graphNode
    }
  })
  return rootNode
}

const initData = () => {
  if (addOrEdit.value === 'edit') {
    queryStationById({ id: stationId.value }).then(res => {
      console.log('queryStationById.res=', res)
      if (res == null) {
        dataStore = {}
      } else {
        Object.assign(dataStore, res)
        if (graph.value) {
          graph.value.dataStore = dataStore
          graph.value.fromJSON(JSON.parse(dataStore.dagJson))
        }
        rootNode.value = getRootNode()
        refreshStencilCommands()
      }
    })
    return
  }

  // Station Object
  Object.assign(dataStore, {
    type: 'STATION',
    name: '新建工作站',
    devices: [],
    actions: [],
    parentNodeId: null,
    bizData: {}
  })

  rootNode.value = createStationNode(
    graph.value,
    { x: 40, y: 40 },
    dataStore
  )
  
  dataStore.dagNodeId = rootNode.value?.id
  if (graph.value) {
    graph.value.dataStore = dataStore
  }
}

const sortDataStore = () => {
  if (Array.isArray(dataStore.actions)) {
    dataStore.actions.sort((act1, act2) => {
      const cell1 = graph.value?.getCellById(act1.dagNodeId)
      const cell2 = graph.value?.getCellById(act2.dagNodeId)
      return (cell1?.position().y || 0) - (cell2?.position().y || 0)
    })
  }

  dataStore.actions?.forEach((act) => {
    if (Array.isArray(act.commands)) {
      
      act.commands.sort((cmd1, cmd2) => {
        
        const cell1 = graph.value?.getCellById(cmd1.dagNodeId)
        const cell2 = graph.value?.getCellById(cmd2.dagNodeId)
        return (cell1?.position().y || 0) - (cell2?.position().y || 0)
      })
    }
  })
}

const doSubmit = async (saveAs) => {
  const rootNodeData = getRootNode()?.getData()
  if (!rootNodeData) {
    console.log('Could not found rootNode!')
    return
  }

  configLoading.value = true

  try {
    const result = graph.value?.toJSON()
    if (!result) return
    debugger

    dataStore.dagJson = JSON.stringify(result)
    sortDataStore()
    dataStore.status = saveAs

    if (addOrEdit.value === 'edit') {
      const res = await edit(dataStore)

      // 返回失败
      debugger
      if (res.status && res.status =='400' && res.data ) {      
        // 获取工作站名称
        const resp = res
        
        // 构建引用列表HTML
        let referencesHtml = ''
        if (resp && resp.data && Array.isArray(resp.data)) {
          referencesHtml = '<ul style="padding-left: 20px; margin: 5px 0;">'
          resp.data.forEach(item => {
            referencesHtml += `<li>${item.procedureName} > ${item.methodName} > ${item.stepName} > ${item.actionName}</li>`
          })
          referencesHtml += '</ul>'
        }
        
        // 显示错误通知
        ElNotification({
          title: '更新失败',
          message: `待删除动作 已被如下流程引用，请删除对应引用后再删除动作 ${referencesHtml}`,
          type: 'error',
          dangerouslyUseHTMLString: true,
          duration: 5000
        })
        initData()
        return    
      }

      Object.assign(dataStore, res)
      if (graph.value) {
        graph.value.dataStore = dataStore
      }
      ElNotification({
        title: '保存成功',
        type: 'success',
        duration: 2000
      })
      console.log('Save successfully.')
    } else {
      const res = await add(dataStore)
      Object.assign(dataStore, res)
      if (graph.value) {
        graph.value.dataStore = dataStore
      }
      ElNotification({
        title: '保存成功',
        type: 'success',
        duration: 2000
      })
      router.push({
        path: '/config-list/station-editor',
        query: { 
          id: res.id, 
          addOrEdit: 'edit' 
        }
      })
      addOrEdit.value = 'edit'
      console.log('Save as New successfully.')
    }
  } catch (err) {
    console.error(err)
  } finally {
    configLoading.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  addOrEdit.value = route.query.addOrEdit || 'add'
  stationId.value = Number(route.query.id) || 0
  
  initGraph()
  initStencil()
  initEvents()
  initData()
})

// 提供数据
provide('getDataStore', () => dataStore)

</script>

<style rel="stylesheet/scss" lang="scss" scoped >

.app-container {
  width: 100%;
  height: calc(100vh - 50px);
  padding:0px;
}
.app-container-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

.flow {
  /* width: 100vw; */
  width: 100%;
  height: calc(100vh - 50px);
}
.flow-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  #stencil {
    width: 260px;
    height: 100%;
    position: relative;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
  }

  .panel {
    width: calc(100% - 550px);
    height: 100%;
    background-color: #e6fcff;
  }

  .panel .toolbar {
    width: calc(100% - 567px);
    height: 38px;
    padding-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
   // background-color: #f7f9fb80;
   // border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position:absolute;
    z-index: 1900;
  }
  .toolbar-group {
    border-radius: 20px;
    background-color: #FFFFFF;
    padding: 0px 20px;
  }
  .panel #container {
    width: 100%;
    height: calc(100% - 38px);
  }

  .config {
    width: 290px;
    height: 100%;
    padding: 0 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    overflow-y: auto;
    
    :deep(.el-card__body) {
      padding: 20px 0px;
    }
  }

  .node_icon {
    color: darkcyan;
  }

</style>
