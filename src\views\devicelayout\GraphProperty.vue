<template>
  <div class="property-container">
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-divider content-position="left">基本信息</el-divider>
      <el-form-item label="背景色" prop="backgroundColor">
        <ThemeColorPicker v-model="form.backgroundColor" @update:model-value="changeThemeColor" />
      </el-form-item>

    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { Graph, Node } from '@antv/x6'
import { useDict } from '@/hooks/useDict'
import { debounce } from '@/utils'
import ThemeColorPicker from '@/views/components/ThemeColorPicker.vue'

// Props
const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  },
  graph: {
    default: () => ({}),
    type: Graph
  },
  dataStore: {
    default: () => ({}),
    type: Object
  }
})

// Emits
const emit = defineEmits(["update:modelValue"]);


// 响应式数据
const defaultForm = { id: null, backgroundColor: null, description: null, positions: [], type: null, configTemplate: null, javaClassName: null, configJavaClassName: null, layoutImage: null, layoutWidth: null, layoutHeight: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }
const form = reactive({ ...defaultForm })
const rules = reactive({
  name: [
    { required: true, message: '设备实例名称', trigger: 'blur' }
  ]
})
const currentSelectedPosIndex = ref(-1)
const initing = ref(false)
const formRef = ref(null)
const tableRef = ref(null)

// 字典数据（需要根据实际项目调整字典获取方式）
const { dict } = useDict(['device_types', 'position_status'])

const changeThemeColor = (val) => {
  //form.backgroundColor = val
  emit('update:modelValue', val)
}

// 方法
const getCurDeviceInstance = () => {
  const curNode = props.curNode
  const { graph } = curNode.model || {}

  if (!graph) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === curNode.id) {
      return ins
    }
  }
  return null
}

const transformNodeToFormData = () => {

  if (props.dataStore.styleSetting){
    const styleSettingObj = JSON.parse(props.dataStore.styleSetting)
    form.backgroundColor = styleSettingObj.background
  }else{
    form.backgroundColor = null
  }

  const curNode = props.curNode
  
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }

 //form.backgroundColor = 
 // graphContainerBackgroundStyle


  form.name = deviceInst.name
  form.description = deviceInst.description
  form.type = deviceInst.device.type

  const devPosConfig = deviceInst.device.positionConfig
  if (devPosConfig) {
    const posArr = JSON.parse(devPosConfig)
    if (!deviceInst.positions) {
      deviceInst.positions = []
    }
    for (let idx = 0; idx < posArr.length; idx++) {
      if (deviceInst.positions.length === idx) {
        const newPos = { name: deviceInst.name + '点位', devicePosIndex: idx, mapPos: posArr[idx] }
        deviceInst.positions.push(newPos)
      } else {
        deviceInst.positions[idx] = { ...deviceInst.positions[idx], devicePosIndex: idx, mapPos: posArr[idx] }
      }
    }
    const needToDelCount = deviceInst.positions.length - posArr.length
    if (needToDelCount > 0) {
      deviceInst.positions.splice(posArr.length, needToDelCount)
    }

    form.positions.splice(0, form.positions.length)
    for (const pos of deviceInst.positions) {
      form.positions.push(pos)
    }
    console.log('this.form.positions=' + form.positions)
  }else{
    form.positions = []
  }
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }

  deviceInst.name = form.name
  deviceInst.description = form.description
  deviceInst.type = form.type

  for (const pos of form.positions) {
    if (!pos.status || pos.status === 'NONE') {
      pos.status = pos.initStatus
    }
  }
  deviceInst.positions = form.positions

  const changeState = 'changed from property'
  curNode.setData({ ...curNode.getData(), changeState })
  emit('update-device-instance', deviceInst)
}

// 创建防抖的更新函数
const debouncedTransformFormDataToNode = debounce(() => {
  transformFormDataToNode()
}, 300)

const onPosSelect = (devicePosIndex) => {
  const curNode = props.curNode
  console.log('onPosSelect devicePosIndex=' + devicePosIndex)
  if (currentSelectedPosIndex.value >= 0) {
    const state = { posIndex: currentSelectedPosIndex.value, state: null }
    const changeState = 'POSITION_CHANGE'
    curNode.setData({ ...curNode.getData(), ...state, changeState })
  }
  
  const state = { posIndex: devicePosIndex, state: 'SELECTED' }
  const changeState = 'POSITION_CHANGE'
  curNode.setData({ ...curNode.getData(), ...state, changeState })
  currentSelectedPosIndex.value = devicePosIndex
}

const onPosBlur = (devicePosIndex) => {
  const curNode = props.curNode
  console.log('onPosBlur devicePosIndex=' + devicePosIndex)
  
  if (currentSelectedPosIndex.value >= 0) {
    const state = { posIndex: currentSelectedPosIndex.value, state: null }
    const changeState = 'POSITION_CHANGE'
    curNode.setData({ ...curNode.getData(), ...state, changeState })
  }
  currentSelectedPosIndex.value = -1
}

// 监听器
watch(() => props.dataStore, (newVal, oldVal) => {
  transformNodeToFormData()
  console.log(`curNode changed from ${oldVal} to ${newVal}`)
},{deep:true})

watch(
  () => form.name,
  (newVal) => {
    console.log('form.name changed from ' + form.name + ' to ' + newVal)
    const deviceInst = getCurDeviceInstance()
    if (!deviceInst) return
    
    deviceInst.name = newVal
  }
)

// 生命周期
onMounted(() => {
  console.log('LayoutProperty created..')
  transformNodeToFormData()
})
</script>

<style lang="scss">
.property-container {
  width: 100%;
}
.circle-button {
  width: 40px;
  height: 40px;
}

.el-table .position-table-cell .cell {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell input {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell .input-pos input {
  width:56px;
}
.el-table .position-table-cell .cell .input-name {
  padding-right: 4px;
}

</style>
