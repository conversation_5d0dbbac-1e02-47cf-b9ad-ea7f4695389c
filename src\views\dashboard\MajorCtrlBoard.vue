<template>
  <div class="status-board-container">
    <StartAnalyseCtrl />
    <TaskStartCtrl />
    <DoorLockCtrl />

    <div class="button-wrapper">
      <div :style="backgroundStyle" class="background-container">
        <el-image
          :src=" emergentStopIcon "
          fit="contain"
          class="img"
        />
      </div>
      <div class="status-text"><span>紧急停止</span></div>
    </div>
  </div>
</template>

<script>
import { Graph } from '@antv/x6'
import { mapGetters } from 'vuex'

import EmergentStopIcon from '@/assets/images/emergent_stop.svg'
import StartIcon from '@/assets/images/start.svg'
import PauseIcon from '@/assets/images/pause.svg'
import LockIcon from '@/assets/images/lock.svg'
import UnlockIcon from '@/assets/images/unlock.svg'
import StartAnalyzeIcon from '@/assets/images/start_analyze.svg'

import StartAnalyseCtrl from './components/StartAnalyseCtrl.vue'
import DoorLockCtrl from './components/DoorLockCtrl.vue'
import TaskStartCtrl from './components/TaskStartCtrl.vue'

export default {
  components: { StartAnalyseCtrl, TaskStartCtrl, DoorLockCtrl },
  props: {
    graph: {
      default: () => {
        return {}
      },
      type: Graph
    }
  },
  data() {
    return {
      StartIcon: StartIcon,
      PauseIcon: PauseIcon,
      LockIcon: LockIcon,
      UnlockIcon: UnlockIcon,
      emergentStopIcon: EmergentStopIcon,
      StartAnalyzeIcon: StartAnalyzeIcon,
      startAnalyseDialogVisible: false,
      procedureOptions: [{ label: '新建流程', value: 0 }],
      startState: 'pause',
      startIcon: this.imageAccessUrl + '%E5%90%AF%E5%8A%A8%E5%88%86%E6%9E%90-20240620031632806.svg',
      websocketState: 'NONE' // OPENED,CLOSED
    }
  },
  computed: {
    ...mapGetters([
      'imagesUploadApi',
      'imageAccessUrl',
      'baseApi',
      'motion'
    ]),
    backgroundStyle() {
      return {
        backgroundImage: `url(${import('@/assets/icons/svg/status-btn-bg.svg')})`
      }
    }
  },
  created() {

  },
  beforeDestroy() {
    this.moveDropdownToBody()
  },
  methods: {
    
  }
}
</script>

<style lang="scss" scoped>
    .status-board-container {
      display: flex;
      justify-content: center; /* 水平居中 */
      align-items: center; /* 垂直居中 */
      flex-wrap: wrap; /* 换行布局 */
      gap: 20px; /* 元素之间的间距 */
      text-align: center; /* 文本居中对齐 */
    }

    .button-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center; /* 水平居中 */
      gap: 10px; /* 按钮和文本之间的间距 */
    }

    .background-container {
      width: 60px; /* 宽度 */
      height: 60px; /* 高度 */
      border-radius: 50%; /* 圆形 */
      background-size: cover; /* 背景图片适应按钮 */
      background-position: center; /* 背景图片居中 */
      background-image:none;
      border: 2px solid #59AED2; /* 边框颜色和样式 */
      display: flex;
      align-items: center; /* 内部内容垂直居中 */
      justify-content: center; /* 内部内容水平居中 */
      cursor: pointer; /* 鼠标移到按钮上变为手指 */
    }
    .background-container:hover {
      background-image: radial-gradient(circle at bottom, #FFFFFF,#399AC3 75%, #0580B4 125%,);
    }

    .status-text {
      font-size: 16px;
      font-weight: bold;
      color:#4f96c5;
    }
    .img{
      width:40px;
      height:40px;
      color:red;
      fill: red;
    }


.el-select-dropdown {
  position: fixed !important;
  z-index: 9999 !important;
  top: auto !important;
  left: auto !important;
}
</style>
