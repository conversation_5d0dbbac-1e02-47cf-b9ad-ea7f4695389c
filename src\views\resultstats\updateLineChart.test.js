/**
 * updateLineChart 函数测试用例
 * 测试从API获取真实数据并更新折线图的功能
 */

// 模拟API返回的数据格式
const mockApiResponse = {
  xAxisData: [
    '2023-01-01 08:30:15',
    '2023-01-01 10:15:22',
    '2023-01-01 14:45:33',
    '2023-01-02 09:20:11',
    '2023-01-02 11:35:44',
    '2023-01-02 16:10:55',
    '2023-01-03 07:45:12',
    '2023-01-03 13:25:38'
  ],
  serialsMap: {
    'pH值': [7.2, 7.1, 7.3, 7.0, 7.2, 7.4, 7.1, 7.3],
    '溶解氧': [8.5, 8.3, 8.7, 8.1, 8.4, 8.6, 8.2, 8.5],
    '浊度': [1.2, 1.5, 1.1, 1.8, 1.3, 1.0, 1.6, 1.4],
    '温度': [22.5, 23.1, 24.2, 21.8, 22.9, 23.5, 22.1, 23.8]
  }
}

// 测试用例1: 正常数据处理
console.log('测试用例1: 正常数据处理')
console.log('输入数据:', mockApiResponse)
console.log('预期输出: 包含4个序列的ECharts配置对象')

// 验证数据格式
const { xAxisData, serialsMap } = mockApiResponse
console.log('X轴数据长度:', xAxisData.length)
console.log('序列数量:', Object.keys(serialsMap).length)

// 验证每个序列的数据长度
Object.keys(serialsMap).forEach(seriesName => {
  const values = serialsMap[seriesName]
  console.log(`${seriesName} 数据长度:`, values.length)
  console.log(`${seriesName} 数据一致性:`, values.length === xAxisData.length ? '✓' : '✗')
})

// 测试用例2: 空数据处理
console.log('\n测试用例2: 空数据处理')
const emptyApiResponse = {
  xAxisData: [],
  serialsMap: {}
}
console.log('输入数据:', emptyApiResponse)
console.log('预期输出: 显示"暂无数据"的图表配置')

// 测试用例3: 数据格式错误处理
console.log('\n测试用例3: 数据格式错误处理')
const invalidApiResponse = {
  xAxisData: ['2023-01-01'],
  serialsMap: {
    'pH值': [7.2, 7.1] // 数据长度不匹配
  }
}
console.log('输入数据:', invalidApiResponse)
console.log('预期输出: 过滤掉长度不匹配的序列')

// 测试用例4: 多序列颜色分配
console.log('\n测试用例4: 多序列颜色分配')
const colors = [
  '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', 
  '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
]

Object.keys(serialsMap).forEach((seriesName, index) => {
  const assignedColor = colors[index % colors.length]
  console.log(`${seriesName} -> ${assignedColor}`)
})

// 测试用例5: 时间格式化
console.log('\n测试用例5: 时间格式化')
xAxisData.forEach(timeStr => {
  const formattedTime = timeStr.includes(' ') ? timeStr.split(' ')[0] : timeStr
  console.log(`${timeStr} -> ${formattedTime}`)
})

// 模拟ECharts配置生成
console.log('\n生成的ECharts配置结构:')
const mockEChartsOption = {
  title: {
    text: '检测值趋势图',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  legend: {
    top: 30,
    data: Object.keys(serialsMap)
  },
  xAxis: {
    type: 'category',
    data: xAxisData
  },
  yAxis: {
    type: 'value',
    scale: true
  },
  series: Object.keys(serialsMap).map((seriesName, index) => ({
    name: seriesName,
    type: 'line',
    data: serialsMap[seriesName],
    smooth: true,
    lineStyle: {
      color: colors[index % colors.length]
    }
  }))
}

console.log('配置对象结构验证:')
console.log('- 标题:', mockEChartsOption.title.text)
console.log('- 图例数据:', mockEChartsOption.legend.data)
console.log('- X轴数据长度:', mockEChartsOption.xAxis.data.length)
console.log('- 序列数量:', mockEChartsOption.series.length)

mockEChartsOption.series.forEach((series, index) => {
  console.log(`- 序列${index + 1}: ${series.name}, 数据点: ${series.data.length}, 颜色: ${series.lineStyle.color}`)
})

// 功能特性总结
console.log('\n✨ updateLineChart 函数功能特性:')
console.log('1. ✅ 从API获取真实数据')
console.log('2. ✅ 支持多个序列同时显示')
console.log('3. ✅ 自动颜色分配')
console.log('4. ✅ 数据格式验证')
console.log('5. ✅ 空数据状态处理')
console.log('6. ✅ 错误状态处理')
console.log('7. ✅ 时间格式化显示')
console.log('8. ✅ 交互式图表配置')
console.log('9. ✅ 数据缩放功能')
console.log('10. ✅ 响应式图例')

export { mockApiResponse, emptyApiResponse, invalidApiResponse }
