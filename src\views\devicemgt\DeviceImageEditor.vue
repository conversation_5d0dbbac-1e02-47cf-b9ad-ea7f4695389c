<template>
  <div class="editor-container">
    <div class="left">

      <el-popover
        placement="left"
        :width="400"
        :height="300"
        trigger="click"
        :teleported="false"
        @show="showDeviceNodeGraph"
        @hide="hideDeviceNodeGraph"
      >
        <el-tabs model-value="ImageSelect">
          <el-tab-pane label="图像选择" name="ImageSelect">
            <div style="height: 328px;">
              <el-form :inline="true" :model="localStorageForm" class="demo-form-inline">
                <el-form-item>
                  <el-input
                    v-model="localStorageForm.blurry"
                    placeholder="搜索图片"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button icon="el-icon-search" type="primary" @click="qryLocalStorage">搜索</el-button>
                </el-form-item>
              </el-form>
              <div style="overflow-y: scroll; height:300px;">
                <el-row :gutter="10">
                  <template v-if="localStorageList.length>0">
                    <el-col
                      v-for="(storage,index) in localStorageList"
                      :key="index"
                      :span="6"
                      class="image-select-col"
                      :title="storage.name"
                    >
                      <el-image
                        :src="imageAccessUrl + storage.realName"
                        fit="contain"
                        style="width: 60px; height: 60px;"
                        @click="localStorageClick(storage)"
                      />
                      <span class="image-label image-select-stration">{{ storage.name }}</span>
                    </el-col>
                  </template>
                </el-row>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="图像上传" name="ImageUpload">
            <div style="height: 328px;">
              <el-upload
                class="upload-demo"
                :headers="headers"
                :action="imagesUploadApi"
                :on-preview="handleUploadImagePreview"
                :on-remove="handleUploadImageRemove"
                :on-success="onPicUploadSuccess"
                :before-upload="beforeUpload"
                :file-list="uploadImageList"
                list-type="picture"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <div class="el-upload__tip">只能上传svg/jpg/png文件，且不超过500kb</div>
              </el-upload>
            </div>
          </el-tab-pane>
          <el-tab-pane label="点位设置" name="PositionSetting">
            <div style="height: 328px;">
              <el-button-group class="toolbar-group">
                <el-tooltip effect="dark" content="新增点位" placement="top">
                  <el-button
                    size="small"
                    :icon="CirclePlus"
                    @click="addNewCircleNode"
                  />
                </el-tooltip>
                <el-tooltip effect="dark" content="新增板位" placement="top">
                  <el-button
                    size="small"
                    :icon="Crop"
                    @click="addNewSquareNode"
                  />
                </el-tooltip>
                <el-tooltip effect="dark" content="删除点位" placement="top">
                  <el-button
                    size="small"
                    :icon="Delete"
                    @click="deleteCircleNode"
                  />
                </el-tooltip>
                <el-tooltip effect="dark" content="复制选中节点" placement="top">
                  <el-button
                    size="small"
                    :icon="CopyDocument"
                    @click="copySelectedNodes"
                  />
                </el-tooltip>
                <el-tooltip effect="dark" content="粘贴节点" placement="top">
                  <el-button
                    size="small"
                    :icon="Document"
                    :disabled="!copiedNodes.length"
                    @click="pasteNodes"
                  />
                </el-tooltip>
                <el-tooltip effect="dark" content="放大" placement="top">
                  <el-button
                    size="small"
                    :icon="ZoomIn"
                    @click="graph?.zoom(0.1)"
                  />
                </el-tooltip>
                <el-tooltip effect="dark" content="缩小" placement="top">
                  <el-button
                    size="small"
                    :icon="ZoomOut"
                    @click="graph?.zoom(-0.1)"
                  />
                </el-tooltip>
                <el-tooltip effect="dark" content="调整到合适大小" placement="top">
                  <el-button
                    size="small"
                    :icon="Aim"
                    @click="zoomToFit"
                  />
                </el-tooltip>
                <el-tooltip effect="dark" content="等距对齐及调整所选节点" placement="top">
                  <el-dropdown placement="bottom"  @command="alignMarkers"  @click.stop :teleported ="false">
                    <el-button
                      size="small"
                      :icon="Sort"
                      
                    />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="horizontal" @click.stop>水平对齐等距分布</el-dropdown-item>
                        <el-dropdown-item command="vertical" @click.stop>竖向对齐等距分布</el-dropdown-item>
                        <el-dropdown-item command="sameSize" @click.stop>长宽相等</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-tooltip>
              </el-button-group>
              <div id="deviceNodeGraph" />
              
            </div>
            <indicator-property v-if="isSelectedIndicatorNode" :cur-node="selectedNode" :device-instance="props.deviceInstance" />
          </el-tab-pane>
        </el-tabs>
        <template #reference>
          <div
            class="device-node-bg"
            :style="`background-image:url(${imageAccessUrl}${curDevice.layoutImage}); width:100px; height:100px; background-size:contain; background-repeat:no-repeat;`"
          >
            <div
              v-for="(marker, index) in markers"
              :key="index"
              :class="['marker', 'marker-'+marker.shape, 'marker-'+marker.state ]"
              :title="marker.tooltip"
              :style="{ top: marker.y + '%', left: marker.x + '%', width: marker.width + '%', height: marker.height + '%'}"
            />
          </div>
        </template>
      </el-popover>

    </div>
    <div class="right">
      <div><span style="margin-right: 4px;">宽度</span><el-input v-model="curDevice.layoutWidth" style="width: 60px;" /></div>
      <div style="margin-top: 8px;"><span style="margin-right: 4px;">高度</span><el-input v-model="curDevice.layoutHeight" style="width: 60px;" /></div>
      <div style="margin-top: 8px;"><span style="margin-right: 4px;">可视</span>
        <el-select v-model="curDevice.imageVisible" filterable placeholder="请选择" style="width: 60px" >
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, inject, watch, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { Graph } from '@antv/x6'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { getToken } from '@/utils/auth'
import { getLocalStorage } from '@/api/localStorage'
import { scaleRectangle } from '@/views/components/x6/dag/index'
import { CirclePlus, Crop, Delete,ZoomIn, CopyDocument, Document,
  ZoomOut, Aim, Sort } from '@element-plus/icons-vue'
import IndicatorProperty from '@/views/devicemgt/components/IndicatorProperty.vue'
const store = useStore()
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// 其他全局配置（根据实际存储位置调整）
const imagesUploadApi = computed(() => store.getters.imagesUploadApi)
const baseApi = computed(() => store.getters.baseApi)

// 使用 defineModel 替代 props
const curDevice = defineModel('device', {
  default: () => ({})
})

// Props
const props = defineProps({
  deviceInstance: {
    default: () => ({}),
    type: Object
  }
})

// 响应式数据
const graph = ref(null)
const markers = ref([])
const localStorageForm = reactive({ blurry: '' })
const localStorageList = ref([])
const uploadImageList = ref([])
const headers = reactive({ Authorization: getToken() })

const copiedNodes = ref([]) // 存储复制的节点

const selectedNode = ref(null)

const getCurNode = inject('getCurNode' )
const getNode = inject('getNode')

const isSelectedIndicatorNode = computed( () => {
  console.log('isSelectedIndicatorNode: type:'+ selectedNode.value?.data?.type )
  if (selectedNode.value ) {
    return true
  }
  return false
})

// 生命周期
onMounted(() => {
  
  showDeviceNodeGraph()
})

// 监听设备配置变化
watch(() => curDevice.value, (newVal) => {
  showDeviceNodeGraph()
}, { deep: true })

// 在 watch 部分添加新的监听
watch(
  () => [curDevice.value.layoutWidth, curDevice.value.layoutHeight, curDevice.value.imageVisible],
  ([newWidth, newHeight, newVisible]) => {
    console.log('curDevice.value.layoutWidth=' + curDevice.value.layoutWidth)
    console.log('curDevice.value.layoutHeight=' + curDevice.value.layoutHeight)
    console.log('curDevice.value.imageVisible=' + curDevice.value.imageVisible)
    
    if (getCurNode) {
      const curNode = getCurNode()
      if (curNode) {
        curNode.resize(Number(newWidth), Number(newHeight))
        curNode.data.imageVisible = newVisible
      }
    }
  }
)

watch(() => curDevice.value.positionConfig, (newVal) => {
  if (newVal) {
    markers.value = JSON.parse(newVal)
  }
})

watch(() => props.deviceInstance, (newVal) => {
  debugger
  if (newVal) {
    console.log('deviceInstance changed: ' + newVal.name)
  }
}, { deep: false })

// 方法
const showDeviceNodeGraph = () => {
  const container = document.getElementById('deviceNodeGraph')
  container.innerHTML = ''
  initGraph()
}

const hideDeviceNodeGraph = () => {
  updateMarkers()
  const container = document.getElementById('deviceNodeGraph')
  container.innerHTML = ''
}

const initGraph = () => {
  const container = document.getElementById('deviceNodeGraph')
  graph.value = new Graph({
    container,
    width: 370,
    height: 300,
    panning: { enabled: true, eventTypes: ['rightMouseDown'] },
    background: false,
    interacting: { nodeMovable: cell => (cell.shape === 'circle' || cell.shape === 'rect')  },
    grid: {
      type: 'dot',
      size: 1,
      args: { color: '#c0c0c0', thickness: 1 }
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 30
    },
    // 添加缩放补偿配置
    scaling: {
      widgets: true
    }
  })

  // 初始化插件
  graph.value
    .use(new Transform({
      resizing: { enabled: node => (node.shape === 'circle' || node.shape === 'rect') , 
          preserveAspectRatio: (node) => { 
            if (node.shape === 'circle') { 
              return true 
            } else if (node.shape === 'rect'){
              return false
            }} },
      rotating: false
    }))
    .use(new Selection({
      rubberband: true,
      showNodeSelectionBox: true,
      filter: cell => (cell.shape === 'circle' || cell.shape === 'rect')
    }))
    .use(new Snapline({ enabled: true }))

    
  graph.value.on('cell:selected', ({ cell }) => {
    selectedNode.value = null
    if (cell.isNode()) {
      selectedNode.value = cell
    }
  })

  graph.value.on('cell:unselected', ({ cell }) => {
    selectedNode.value = null
  })

  initGraphData()
  zoomToFit()
}

const zoomToFit = () => {
  graph.value?.zoomToFit({ padding: 10, minScale: 0.2, maxScale: 4 })
  graph.value?.centerContent()
}

const initGraphData = () => {

  graph.value?.addNode({
    shape: 'image',
    x: 290,
    y: 150,
    width: curDevice.value.layoutWidth,
    height: curDevice.value.layoutHeight,
    imageUrl: imageAccessUrl.value + curDevice.value.layoutImage

  })

  const posConfigs = curDevice.value.positionConfig ? JSON.parse(curDevice.value.positionConfig) : []
  posConfigs.forEach(cfg => {
    const imgNode = getImageNode()
    const imgX = imgNode.position().x
    const imgY = imgNode.position().y
    const imgW = imgNode.size().width
    const imgH = imgNode.size().height
    const nW = imgW * cfg.width / 100
    const nH = imgH * cfg.height / 100
    if (!cfg.nodeId){
      console.log('cfg.nodeId is null')
    }
    if (cfg.shape === 'circle'){
      graph.value?.addNode(createCircle(
        imgX + (imgW * cfg.x / 100) - nW / 2,
        imgY + (imgH * cfg.y / 100) - nH / 2,
        nW,
        nH,
        cfg
      ))
    }else if (cfg.shape === 'rect'){
      graph.value?.addNode(createSquare(
        imgX + (imgW * cfg.x / 100) ,
        imgY + (imgH * cfg.y / 100) ,
        nW,
        nH,
        cfg
      ))
    }
  })
}

const getImageNode = () => {
  return graph.value?.getCells().find(node => node.shape === 'image')
}

const addNewCircleNode = () => {
  const imgNode = getImageNode()
  const position = imgNode ? imgNode.position() : { x: 0, y: 0 }
  graph.value?.addNode(createCircle(position.x, position.y, 30, 30))
}

const createCircle = (x, y, width, height, data) => ({
  shape: 'circle',
  x,
  y,
  width,
  height,
  attrs: {
    body: {
      fill: '#ffffff09',
      stroke: '#ff0000',
      strokeWidth: 1,
      // 添加缩放补偿属性
      strokeScaleEnabled: false
    }
  },
  data: {
    nodeId: data?.nodeId,
    type: data?.type,
    popoverPlace: data?.popoverPlace,
    cmdControls: data?.cmdControls
  }
})


const addNewSquareNode = () => {
  const imgNode = getImageNode()
  const position = imgNode ? imgNode.position() : { x: 0, y: 0 }
  graph.value?.addNode(createSquare(position.x, position.y, 30, 30))
}

const createSquare = (x, y, width, height, data) => ({
  shape: 'rect',
  x,
  y,
  width,
  height,
  attrs: {
    body: {
      fill: '#ffffff09',
      stroke: '#00ff00',
      strokeWidth: 1,
      // 添加缩放补偿属性
      strokeScaleEnabled: false
    }
  },
  data: {
    nodeId: data?.nodeId,
    type: data?.type,
    popoverPlace: data?.popoverPlace,
    cmdControls: data?.cmdControls
  }
})


const deleteCircleNode = () => {
  graph.value?.getSelectedCells().forEach(node => {
    graph.value?.removeCell(node)
  })
}

// 复制选中的节点
const copySelectedNodes = () => {
  const selectedCells = graph.value?.getSelectedCells()
  if (!selectedCells || selectedCells.length === 0) {
    return // 没有选中节点
  }
  
  // 清空之前复制的节点
  copiedNodes.value = []
  
  // 保存选中节点的信息
  selectedCells.forEach(cell => {
    if (cell.shape === 'circle' || cell.shape === 'rect') {
      const { x, y } = cell.position()
      const { width, height } = cell.size()
      
      copiedNodes.value.push({
        shape: cell.shape,
        x, y, width, height
      })
    }
  })
}

// 粘贴节点
const pasteNodes = () => {
  if (!copiedNodes.value.length) return
  
  // 清除当前选择
  graph.value?.cleanSelection()
  
  // 计算偏移量，使粘贴的节点错开一定距离
  const offset = 20
  
  // 创建并添加新节点
  const newNodes = []
  copiedNodes.value.forEach(nodeInfo => {
    let newNode
    if (nodeInfo.shape === 'circle') {
      newNode = graph.value?.addNode(createCircle(
        nodeInfo.x + offset,
        nodeInfo.y + offset,
        nodeInfo.width,
        nodeInfo.height
      ))
    } else if (nodeInfo.shape === 'rect') {
      newNode = graph.value?.addNode(createSquare(
        nodeInfo.x + offset,
        nodeInfo.y + offset,
        nodeInfo.width,
        nodeInfo.height
      ))
    }
    
    if (newNode) {
      newNodes.push(newNode)
    }
  })
  
  // 选中新创建的节点
  newNodes.forEach(node => {
    graph.value?.select(node)
  })
}

/**
 * 对选中的节点进行对齐和等距分布
 * @param {string} direction - 对齐方向，'horizontal'或'vertical'
 */
 const alignMarkers = (direction) => {
   // 阻止事件冒泡
   event?.stopPropagation()
  const selectedCells = graph.value?.getSelectedCells()
  if (!selectedCells || selectedCells.length < 1) {
    return // 至少需要选择两个节点
  }

  if (direction === 'sameSize') {
    const referenceNode = selectedCells[0]
    // 首先获取选中节点的最小宽度和高度
    const minWidth = referenceNode.size().width
    // 然后遍历所有选中节点，将它们的大小设置为最小宽度和高度
    selectedCells.forEach(cell => {
      cell.size({ width: minWidth, height: minWidth })
    })
  }

  // 按照 x 或 y 坐标排序
  const sortedCells = [...selectedCells]
  if (direction === 'horizontal') {
    sortedCells.sort((a, b) => a.position().x - b.position().x)
  } else if (direction === 'vertical') {
    sortedCells.sort((a, b) => a.position().y - b.position().y)
  }

  // 获取参考节点（第一个选中的节点）
  const referenceNode = sortedCells[0]
  const refPosition = referenceNode.position()
  const refSize = referenceNode.size()

  // 计算起始位置和总距离
  const startPos = direction === 'horizontal' ? refPosition.x : refPosition.y
  const endPos = direction === 'horizontal' 
    ? sortedCells[sortedCells.length - 1].position().x 
    : sortedCells[sortedCells.length - 1].position().y
  const totalDistance = endPos - startPos
  
  // 计算每个节点之间的间距
  const spacing = totalDistance / (sortedCells.length - 1)

  // 对每个节点进行调整
  sortedCells.forEach((cell, index) => {
    // 设置统一的宽高（参考第一个节点）
    cell.resize(refSize.width, refSize.height)
    
    // 计算新位置
    if (direction === 'horizontal') {
      // 水平对齐：x 坐标等距分布，y 坐标统一
      const newX = startPos + spacing * index
      cell.position(newX, refPosition.y)
    } else if (direction === 'vertical') {
      // 垂直对齐：y 坐标等距分布，x 坐标统一
      const newY = startPos + spacing * index
      cell.position(refPosition.x, newY)
    }
  })
}

const updateMarkers = () => {
  markers.value = []
  const imgNode = getImageNode()
  if (!imgNode) return

  const { x: imgX, y: imgY } = imgNode.position()
  const { width: imgW, height: imgH } = imgNode.size()

  graph.value?.getCells()
  .filter(node => (node.shape === 'circle' || node.shape === 'rect'))
  .forEach(node => {
    const { x: nX, y: nY } = node.position()
    const nW = node.size().width
    const nH = node.size().height
    const _type = node.data?.type
    const _popoverPlace = node.data?.popoverPlace
    const _cmdControls = node.data?.cmdControls
    if (node.shape === 'circle') {
      const nCfgX = (nX - imgX + nW / 2) * 100 / imgW
      const nCfgY = (nY - imgY + nH / 2) * 100 / imgH
      markers.value.push({
        nodeId: node.id,
        shape: node.shape,
        x: nCfgX, 
        y: nCfgY, 
        width: (nW / imgW * 100),
        height: (nH / imgH * 100),
        state: 'NONE', 
        type: _type||'DEFAULT',
        tooltip: 'none',
        popoverPlace: _popoverPlace,
        cmdControls: _cmdControls
      })
    }else if (node.shape ==='rect'){
      const nCfgX = (nX - imgX) * 100 / imgW
      const nCfgY = (nY - imgY) * 100 / imgH
      markers.value.push({
        nodeId: node.id,
        shape: node.shape,
        boardNodeId: node.id,
        x: nCfgX,
        y: nCfgY,
        width: (nW / imgW * 100),
        height: (nH / imgH * 100),
        state: 'NONE',
        type: _type||'BOARD',
        tooltip: 'none',
        popoverPlace: _popoverPlace,
        cmdControls: _cmdControls
      })
    }
  })

  sortMarkers()
  debugger
  curDevice.value.positionConfig = JSON.stringify(markers.value)
}


const sortMarkers = () => {
  const equalThred = 2
  // 分离 rect 和 circle 标记
  const rects = markers.value.filter(marker => marker.type === 'BOARD')
  const circles = markers.value.filter(marker => marker.type === 'DEFAULT')
  const indicators = markers.value.filter(marker => marker.type === 'INDICATOR')
  const controllers = markers.value.filter(marker => marker.type === 'CONTROLLER')
  // 对 rect 按照从左至右，从上到下排序
  rects.sort((a, b) => {
    // 如果 x 坐标相差不大，则按 y 坐标排序
    if (Math.abs(a.y - b.y) < equalThred) {
      return a.x - b.x
    }
    // 否则按 y 坐标排序
    return a.y - b.y
  })
  
  // 创建结果数组，先放入排序后的 rect
  const result = []
  
  // 处理每个 rect 内部的 circle
  rects.forEach(rect => {
    result.push(rect)
    // 找出在当前 rect 内部的所有 circle
    const circlesInRect = circles.filter(circle => isPointInRect(circle, rect))
    
    // 对 rect 内部的 circle 按照从左至右，从上到下排序
    circlesInRect.sort((a, b) => {
      if (Math.abs(a.y - b.y) < equalThred) {
        return a.x - b.x
      }
      return a.y - b.y
    })
    
    // 将排序后的 circle 添加到结果数组中
    result.push(...circlesInRect)
    
    // 从 circles 中移除已处理的 circle
    circlesInRect.forEach(circle => {
      circle.boardNodeId = rect.boardNodeId
      const index = circles.findIndex(c => c.x === circle.x && c.y === circle.y)
      if (index !== -1) {
        circles.splice(index, 1)
      }
    })
  })
  
  // 处理不在任何 rect 内部的 circle
  if (circles.length > 0) {
    // 对剩余的 circle 按照从左至右，从上到下排序
    circles.sort((a, b) => {
      if (Math.abs(a.y - b.y) < equalThred) {
        return a.x - b.x
      }
      return a.y - b.y
    })
    
    // 将排序后的 circle 添加到结果数组中
    result.push(...circles)
    
    result.sort((a, b) => {
      if (Math.abs(a.y - b.y) < equalThred) {
        return a.x - b.x
      }
      return a.y - b.y
    })
  }

  if (indicators.length > 0) {
    indicators.sort((a, b) => {
      if (Math.abs(a.y - b.y) < equalThred) {
        return a.x - b.x
      }
      return a.y - b.y
    })
    result.push(...indicators)
  }

  if (controllers.length > 0) {
    controllers.sort((a, b) => {
      if (Math.abs(a.y - b.y) < equalThred) {
        return a.x - b.x
      }
      return a.y - b.y  
    })  
    result.push(...controllers)
  }

  
  // 更新 markers 数组
  markers.value = result
}

// 判断点是否在矩形内部的辅助函数
const isPointInRect = (point, rect) => {
  // 考虑到 circle 的中心点和 rect 的左上角坐标
  // circle 的 x,y 是中心点，而 rect 的 x,y 是左上角
  return (
    point.x >= rect.x && 
    point.x <= rect.x + rect.width && 
    point.y >= rect.y && 
    point.y <= rect.y + rect.height
  )
}

// 图片相关方法
const localStorageClick = (storage) => {
  curDevice.value.layoutImage = storage.realName
  const image = new Image()
    image.src = imageAccessUrl.value + curDevice.value.layoutImage
    image.onload = () => {
      // 获取图片实际尺寸
      const {width, height} = scaleRectangle(image.width,image.height)

      // 更新设备尺寸
      curDevice.value.layoutWidth = width
      curDevice.value.layoutHeight = height

      if (getCurNode){
        const curNode = getCurNode()
        curNode.resize(width, height)
      }
      
    }

//  if (!curDevice.value.layoutWidth) curDevice.value.layoutWidth = 100
//  if (!curDevice.value.layoutHeight) curDevice.value.layoutHeight = 100
}

const qryLocalStorage = async () => {
  const res = await getLocalStorage({ ...localStorageForm })
  localStorageList.value = res
}

const onPicUploadSuccess = (res) => {
  curDevice.value.layoutImage = res.realName
  if (!curDevice.value.layoutWidth) curDevice.value.layoutWidth = 100
  if (!curDevice.value.layoutHeight) curDevice.value.layoutHeight = 100
}

// 空方法保持模板结构
const handleUploadImagePreview = () => {}
const handleUploadImageRemove = () => {}

// 添加图片上传前的处理函数
const beforeUpload = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = (e) => {
      const image = new Image()
      image.src = e.target.result
      image.onload = () => {
        // 获取图片实际尺寸
        const {width, height} = scaleRectangle(image.width,image.height)

        // 更新设备尺寸
        curDevice.value.layoutWidth = width
        curDevice.value.layoutHeight = height

        if (getCurNode){
          const curNode = getCurNode()
          curNode.resize(width, height)
        }
        
        resolve(file)
      }
    }
  })
}


onMounted(() => {
  qryLocalStorage()
})


// 导出需要的方法供模板使用
defineExpose({
  getCurNode
})
</script>

<style  rel="stylesheet/scss" lang="scss" scoped>

.device-node-bg {
  width: 100px;
  height: 100px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  background-color: antiquewhite;
 // border: 4px solid antiquewhite;

  cursor: pointer;
  position: relative;
}

.marker {
  position: absolute;
  width: 20px;

  cursor: pointer;
  border: 1px solid white; /* Optional for better visibility */
}
.marker-circle {
  aspect-ratio: 1 / 1;

  transform: translate(-50%, -50%);
  border-radius: 50%;
}
.marker-rect {
  border-radius: 0;
}
.marker-NONE {
  background-color: rgb(185, 185, 185);
  display: block;
}
.marker-IDLE {
  background-color: white;
  display: block;
}
.marker-IDLE_CONTAINER {
  background-color: rgb(6, 185, 235);
  display: none;
}
.marker-HOLD {
  background-color: rgb(0, 255, 0);
  display: block;
}
.marker-HOLD_RUNNING {
  background-color: rgb(0, 255, 0);
  display: block;
}
.marker-TO_HOLD {
  background-color: rgb(0, 255, 0);
  animation: opacity-blink 1s infinite alternate;
  display: block;
}
.marker-DONE {
  background-color: rgb(0, 140, 255);
  display: block;
}
@keyframes opacity-blink {
    0% {
        background-color: rgba(0, 255, 0, 1);
    }
    100% {
        background-color: rgba(255, 0, 0, 0);
    }
}

.marker-LEAVE {
  background-color: rgb(221, 232, 18);
  display: block;
}
.marker-SELECTED {
  border: 2px solid rgb(233, 126, 3);
  animation: selected-opacity-blink 1s infinite alternate;
  display: block;
}

@keyframes selected-opacity-blink {
    0% {
        border-color: rgba(233, 126, 3, 1);
    }
    100% {
      border-color: rgba(255, 0, 0, 0);
    }
}

.runner {
  display: none;
}
.runner-HOLD_RUNNING {
  display:block;

  width: 100%;
  aspect-ratio: 1;
  border-radius: 50%;
  background:
    radial-gradient(farthest-side,#ffa516 94%,#0000) top/4px 4px no-repeat,
    conic-gradient(#0000 30%,#ffa516);
  -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 4px),#000 0);
  animation: l13 1s infinite linear;
}
@keyframes l13{
  100%{transform: rotate(1turn)}
}
.badge-item {
  margin-top: 10px;
  margin-right: 40px;
  position:absolute;
  z-index: 3998;
}

.editor-container {
  display: flex;
  height: 100px;
  .left{
    width: 108px;
    height: 108px;
    background-color: antiquewhite;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .right{
    margin-left: 20px;
    width: 100px;
    display: flex;
    flex-direction: column;
  }
}

#deviceNodeGraph {
  width: 100%;
  height: 300px;
  background-color: antiquewhite;
}

.image-select-col{
  background-color: antiquewhite;
  padding:5px;
  border: 1px solid #fff;
}
.image-select-col:hover {
  background-color:lightskyblue;
  border: 1px solid #ffa516;
  border-radius: 4px;
  cursor: pointer;
}
.image-label {
  display: block;
  width: 60px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

}
</style>
