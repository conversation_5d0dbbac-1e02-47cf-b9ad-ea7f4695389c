<template>
  <el-input v-model="model" placeholder="请输入内容" class="input-with-json" :readonly="readonly">
    <template #append>
      <el-popover
        placement="left"
        width="400"
        trigger="click">
          <VueJsonPretty 
            v-model:data="jsonObj" 
            :editable="false"
            :showDoubleQuotes="true"
            :showLength="true"
          />
          <template #reference>
            <el-icon>
              <View />
            </el-icon>
          </template>
      </el-popover>
    </template>
  </el-input>
</template>

<script setup>
import { ref, reactive, watch, watchEffect, defineModel, onMounted, inject, nextTick } from 'vue'

import { Search, View } from '@element-plus/icons-vue'

import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'

const model = defineModel({
  prop: 'modelValue',
  event: 'update:modelValue'
})

const readonly = ref(false)

const jsonObj = ref({})

onMounted(() => {
  console.log('model', model.value)
  if (!model.value) return
  jsonObj.value = JSON.parse(model.value)
})

</script>

<style lang="scss" scoped>
.input-with-json {
  :deep(.el-input-group__append) {
    padding: 0 10px !important;
    cursor: pointer;
  }
}


</style>