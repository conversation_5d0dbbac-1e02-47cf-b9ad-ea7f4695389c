<template>
  <el-popover
    v-bind="$attrs"
    v-on="$listeners"
    placement="right" :width="430"
            :height="540" trigger="click"
            @show="onShow"
    @hide="onHide" >
            
            <template #default>
              <div style="width:540px; height:430px;">
                

      <el-button-group class="toolbar-group">

        <el-tooltip effect="dark" content="放大" placement="top">
          <el-button
            size="small"
            :icon="ZoomIn"
            @click="graph?.zoom(0.2)"
          />
        </el-tooltip>
        <el-tooltip effect="dark" content="缩小" placement="top">
          <el-button
            size="small"
            :icon="ZoomOut"
            @click="graph?.zoom(-0.2)"
          />
        </el-tooltip>
        <el-tooltip effect="dark" content="调整到合适大小" placement="top">
          <el-button
            size="small"
            :icon="Aim"
            @click="zoomToFit"
          />
        </el-tooltip>

      </el-button-group>
      
    <div style="height: 428px; width:400px;">
      <div :id="'deviceNodeGraph'+props.deviceInstanceId+props.name" class="deviceNodeGraph" />
    </div>
  
  </div>
            </template>
            
              <template #reference>
                <slot name="reference" />
              </template>
            </el-popover>
</template>

<script setup>
import { ref, reactive, inject, watch, computed, onMounted, nextTick } from 'vue'
import { useStore } from 'vuex'
import { Graph } from '@antv/x6'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { getToken } from '@/utils/auth'
import {  queryDeviceInstanceById, updatePositionDetailConfig  } from '@/api/deviceInstance'
import { scaleRectangle } from '@/views/components/x6/dag/index'
import { CirclePlus, Crop, Delete,ZoomIn, CopyDocument, Document,
  ZoomOut, Aim, Sort, 
  Finished} from '@element-plus/icons-vue'
import { ElNotification, ElPopover } from 'element-plus'

const store = useStore()
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// 其他全局配置（根据实际存储位置调整）
const imagesUploadApi = computed(() => store.getters.imagesUploadApi)
const baseApi = computed(() => store.getters.baseApi)

// Props
const props = defineProps({
  deviceInstanceId: {
    default: null,
    type: Number,
    required: true
  },
  name: {
    default: null,
    type: String,
    required: true
  },
  readonly: {
    default: false,
    type: Boolean,
    required: false
  }
})


// 使用 defineModel 替代 props
const curDevice = defineModel('device', {
  default: () => ({})
})

// 响应式数据
const graph = ref(null)
const markers = ref([])
const localStorageForm = reactive({ blurry: '' })
const localStorageList = ref([])
const uploadImageList = ref([])
const headers = reactive({ Authorization: getToken() })

const copiedNodes = ref([]) // 存储复制的节点

const getCurNode = inject('getCurNode' )
const getNode = inject('getNode')

// 生命周期
onMounted(() => {
 // showDeviceNodeGraph()
})

const onShow = () => {
  nextTick(() => {
    showDeviceNodeGraph()
  })
  // showDeviceNodeGraph()
}

const onHide = () => {
 
}


// 监听设备配置变化
//watch(() => curDevice.value, (newVal) => {
//  showDeviceNodeGraph()
//}, { deep: true })

// 在 watch 部分添加新的监听
watch(
  () =>props.deviceInstanceId,
  () => {
 //   showDeviceNodeGraph()
  }
)

/*
watch(() => curDevice.value.positionConfig, (newVal) => {
  if (newVal) {
    markers.value = JSON.parse(newVal)
  }
})
*/

// 方法
const showDeviceNodeGraph = async () => {
  if (props.deviceInstanceId) {
   const res = await queryDeviceInstanceById({deviceInstanceId:props.deviceInstanceId})
   curDevice.value = res
   //.then(res => {
   //   curDevice.value = res.data
   // })
  }
  const id = 'deviceNodeGraph'+props.deviceInstanceId+props.name
  const container = document.getElementById(id)
  if (!container) {
    console.error('Container not found by id='+id)
    return
  }
  container.innerHTML = ''
  initGraph()
}

const saveConfig = async () => {
 
  const result = graph.value.toJSON()
  curDevice.value.positionDetailConfig = JSON.stringify(result)

  const res = await updatePositionDetailConfig(curDevice.value)
  ElNotification.success({
    title: '保存成功',
    message: '保存成功',
    duration: 2000
  })
}

const initGraph = () => {
  const id = 'deviceNodeGraph'+props.deviceInstanceId+props.name
  const container = document.getElementById(id)
  if (!container) {
    console.error('Container not found by id='+id)
    return
  }
  debugger
  graph.value = new Graph({
    container,
    width: 400,
    height: 400,
    panning: { enabled: true, eventTypes: ['rightMouseDown'] },
    background: false,
    interacting: { nodeMovable: cell => (cell.shape === 'circle' || cell.shape === 'rect')  },
    grid: {
      type: 'dot',
      size: 1,
      args: { color: '#c0c0c0', thickness: 1 }
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 30
    },
    // 添加缩放补偿配置
    scaling: {
      widgets: true
    }
  })

  // 初始化插件

  initGraphData()
  zoomToFit()
}

const zoomToFit = () => {
  graph.value?.zoomToFit({ padding: 20, minScale: 0.8, maxScale: 4 })
  graph.value?.centerContent()
}

const initGraphData = () => {

  // 初始化节点
  const posDetailConfig = curDevice.value.positionDetailConfig
  if (posDetailConfig){
    const layoutSvgObj = JSON.parse(posDetailConfig)
    graph.value.fromJSON(layoutSvgObj)
    refreshStatus()
    return
  }

  // 从positionConfig中读取
  graph.value?.addNode({
    x: 10,
    y: 10,
    width: 200,
    height: 200,
    attrs: {
      body: {
        stroke: '#237804',
       
        rx: 10,
        ry: 10,
      },
    },
    data:{id:'bgImage'}
  })

  
  const positionConfig = curDevice.value.positionConfig ? JSON.parse(curDevice.value.positionConfig) : []

  let devPosIdx = -1;
  positionConfig.forEach(cfg => {
    devPosIdx = devPosIdx + 1
    const imgNode = getImageNode()
    const imgX = imgNode.position().x
    const imgY = imgNode.position().y
    const imgW = imgNode.size().width
    const imgH = imgNode.size().height
    const nW = imgW * cfg.width / 100
    const nH = imgH * cfg.height / 100
    
    if (cfg.shape === 'circle'){
      graph.value?.addNode(createCircle(
        imgX + (imgW * cfg.x / 100) - nW / 2,
        imgY + (imgH * cfg.y / 100) - nH / 2,
        nW,
        nH,
        {serviceCode:cfg.serviceCode, serviceCode2:cfg.serviceCode2,devPosIdx:devPosIdx}
      ))
    }else if (cfg.shape === 'rect'){
      graph.value?.addNode(createSquare(
        imgX + (imgW * cfg.x / 100) ,
        imgY + (imgH * cfg.y / 100) ,
        nW,
        nH
      ))
    }
  })
}

const refreshStatus = () => {
  const positions = curDevice.value.positions
  if (!positions){
    return
  }
  for (let i = 0; i < positions.length; i++) {
    const position = positions[i]
    let node = null

    if ( position.serviceCode){
      const serviceCodeArr = position.serviceCode.split('-')
      if (serviceCodeArr.length > 1){
        const serviceCode = serviceCodeArr[0]
        const serviceCode2 = serviceCodeArr[1]
        node = graph.value?.getCells().find(node => node.data.serviceCode === serviceCode2)
        if (node) {
          node.attr('label/text', position.serviceCode2)
        }
      }else{
        node = graph.value?.getCells().find(node => node.data.serviceCode === position.serviceCode)
      }
    }
     
    if (node){
      let statusColor = '#ffffff'
      if (position.status === 'READY'){
        statusColor = 'rgba(43, 209, 255, 0.64)'
      }else if (position.status === 'LEAVE'){
        statusColor = 'rgba(221, 232, 18, 0.64)'
      }else if (position.status === 'DONE'){
        statusColor = 'rgba(0, 140, 255, 0.64)'
      }
      node.attr('body/fill', statusColor)
    }
  }
  
}

const getImageNode = () => {
  return graph.value?.getCells().find(node => node?.data?.id === 'bgImage')
}

const addNewCircleNode = () => {
  const imgNode = getImageNode()
  const position = imgNode ? imgNode.position() : { x: 0, y: 0 }
  graph.value?.addNode(createCircle(position.x, position.y, 30, 30))
}

const createCircle = (x, y, width, height,data) => ({
  shape: 'circle',
  x,
  y,
  width,
  height,
  label: data.serviceCode,
  attrs: {
    body: {
      fill: '#ffffff09',
      stroke: '#ff0000',
      strokeWidth: 1,
      // 添加缩放补偿属性
      strokeScaleEnabled: false
    },
    label: {
      text: data.serviceCode,
      fontSize: 7
    }
  },
  data
})


const addNewSquareNode = () => {
  const imgNode = getImageNode()
  const position = imgNode ? imgNode.position() : { x: 0, y: 0 }
  graph.value?.addNode(createSquare(position.x, position.y, 30, 30))
}

const createSquare = (x, y, width, height) => ({
  shape: 'rect',
  x,
  y,
  width,
  height,
  attrs: {
    body: {
      fill: '#ffffff09',
      stroke: '#00ff00',
      strokeWidth: 1,
      // 添加缩放补偿属性
      strokeScaleEnabled: false
    }
  }
})


const deleteCircleNode = () => {
  graph.value?.getSelectedCells().forEach(node => {
    graph.value?.removeCell(node)
  })
}

// 复制选中的节点
const copySelectedNodes = () => {
  const selectedCells = graph.value?.getSelectedCells()
  if (!selectedCells || selectedCells.length === 0) {
    return // 没有选中节点
  }
  
  // 清空之前复制的节点
  copiedNodes.value = []
  
  // 保存选中节点的信息
  selectedCells.forEach(cell => {
    if (cell.shape === 'circle' || cell.shape === 'rect') {
      const { x, y } = cell.position()
      const { width, height } = cell.size()
      
      copiedNodes.value.push({
        shape: cell.shape,
        x, y, width, height
      })
    }
  })
}

// 粘贴节点
const pasteNodes = () => {
  if (!copiedNodes.value.length) return
  
  // 清除当前选择
  graph.value?.cleanSelection()
  
  // 计算偏移量，使粘贴的节点错开一定距离
  const offset = 20
  
  // 创建并添加新节点
  const newNodes = []
  copiedNodes.value.forEach(nodeInfo => {
    let newNode
    if (nodeInfo.shape === 'circle') {
      newNode = graph.value?.addNode(createCircle(
        nodeInfo.x + offset,
        nodeInfo.y + offset,
        nodeInfo.width,
        nodeInfo.height
      ))
    } else if (nodeInfo.shape === 'rect') {
      newNode = graph.value?.addNode(createSquare(
        nodeInfo.x + offset,
        nodeInfo.y + offset,
        nodeInfo.width,
        nodeInfo.height
      ))
    }
    
    if (newNode) {
      newNodes.push(newNode)
    }
  })
  
  // 选中新创建的节点
  newNodes.forEach(node => {
    graph.value?.select(node)
  })
}

/**
 * 对选中的节点进行对齐和等距分布
 * @param {string} direction - 对齐方向，'horizontal'或'vertical'
 */
 const alignMarkers = (direction) => {
   // 阻止事件冒泡
   event?.stopPropagation()
  const selectedCells = graph.value?.getSelectedCells()
  if (!selectedCells || selectedCells.length < 2) {
    return // 至少需要选择两个节点
  }

  // 按照 x 或 y 坐标排序
  const sortedCells = [...selectedCells]
  if (direction === 'horizontal') {
    sortedCells.sort((a, b) => a.position().x - b.position().x)
  } else if (direction === 'vertical') {
    sortedCells.sort((a, b) => a.position().y - b.position().y)
  }

  // 获取参考节点（第一个选中的节点）
  const referenceNode = sortedCells[0]
  const refPosition = referenceNode.position()
  const refSize = referenceNode.size()

  // 计算起始位置和总距离
  const startPos = direction === 'horizontal' ? refPosition.x : refPosition.y
  const endPos = direction === 'horizontal' 
    ? sortedCells[sortedCells.length - 1].position().x 
    : sortedCells[sortedCells.length - 1].position().y
  const totalDistance = endPos - startPos
  
  // 计算每个节点之间的间距
  const spacing = totalDistance / (sortedCells.length - 1)

  // 对每个节点进行调整
  sortedCells.forEach((cell, index) => {
    // 设置统一的宽高（参考第一个节点）
    cell.resize(refSize.width, refSize.height)
    
    // 计算新位置
    if (direction === 'horizontal') {
      // 水平对齐：x 坐标等距分布，y 坐标统一
      const newX = startPos + spacing * index
      cell.position(newX, refPosition.y)
    } else if (direction === 'vertical') {
      // 垂直对齐：y 坐标等距分布，x 坐标统一
      const newY = startPos + spacing * index
      cell.position(refPosition.x, newY)
    }
  })
}

const updateMarkers = () => {
  markers.value = []
  const imgNode = getImageNode()
  if (!imgNode) return

  const { x: imgX, y: imgY } = imgNode.position()
  const { width: imgW, height: imgH } = imgNode.size()
debugger
  graph.value?.getCells()
  .filter(node => (node.shape === 'circle' || node.shape === 'rect'))
  .forEach(node => {
    const { x: nX, y: nY } = node.position()
    const nW = node.size().width
    const nH = node.size().height
    if (node.shape === 'circle') {
      const nCfgX = (nX - imgX + nW / 2) * 100 / imgW
      const nCfgY = (nY - imgY + nH / 2) * 100 / imgH
      const markerItem = {...node.data, ...{
        shape: node.shape,
        x: nCfgX, 
        y: nCfgY, 
        width: (nW / imgW * 100),
        height: (nH / imgH * 100),
        state: 'NONE', 
        type: 'DEFAULT',
        tooltip: 'none'
      }}
      markers.value.push(markerItem)

    }else if (node.shape ==='rect'){
      const nCfgX = (nX - imgX) * 100 / imgW
      const nCfgY = (nY - imgY) * 100 / imgH
      markers.value.push({
        shape: node.shape,
        boardNodeId: node.id,
        x: nCfgX,
        y: nCfgY,
        width: (nW / imgW * 100),
        height: (nH / imgH * 100),
        state: 'NONE',
        type: 'BOARD',
        tooltip: 'none'
      })
    }
  })

  sortMarkers()
 
  curDevice.value.positionDetailConfig = JSON.stringify(markers.value)
}


const sortMarkers = () => {
  const equalThred = 2
  // 分离 rect 和 circle 标记
  const rects = markers.value.filter(marker => marker.shape === 'rect')
  const circles = markers.value.filter(marker => marker.shape === 'circle')
  
  // 对 rect 按照从左至右，从上到下排序
  rects.sort((a, b) => {
    // 如果 x 坐标相差不大，则按 y 坐标排序
    if (Math.abs(a.y - b.y) < equalThred) {
      return a.x - b.x
    }
    // 否则按 y 坐标排序
    return a.y - b.y
  })
  
  // 创建结果数组，先放入排序后的 rect
  const result = []
  
  // 处理每个 rect 内部的 circle
  rects.forEach(rect => {
    result.push(rect)
    // 找出在当前 rect 内部的所有 circle
    const circlesInRect = circles.filter(circle => isPointInRect(circle, rect))
    
    // 对 rect 内部的 circle 按照从左至右，从上到下排序
    circlesInRect.sort((a, b) => {
      if (Math.abs(a.y - b.y) < equalThred) {
        return a.x - b.x
      }
      return a.y - b.y
    })
    
    // 将排序后的 circle 添加到结果数组中
    result.push(...circlesInRect)
    
    // 从 circles 中移除已处理的 circle
    circlesInRect.forEach(circle => {
      circle.boardNodeId = rect.boardNodeId
      const index = circles.findIndex(c => c.x === circle.x && c.y === circle.y)
      if (index !== -1) {
        circles.splice(index, 1)
      }
    })
  })
  
  // 处理不在任何 rect 内部的 circle
  if (circles.length > 0) {
    // 对剩余的 circle 按照从左至右，从上到下排序
    circles.sort((a, b) => {
      if (Math.abs(a.y - b.y) < equalThred) {
        return a.x - b.x
      }
      return a.y - b.y
    })
    
    // 将排序后的 circle 添加到结果数组中
    result.push(...circles)
  }
  
  // 更新 markers 数组
  markers.value = result
}

// 判断点是否在矩形内部的辅助函数
const isPointInRect = (point, rect) => {
  // 考虑到 circle 的中心点和 rect 的左上角坐标
  // circle 的 x,y 是中心点，而 rect 的 x,y 是左上角
  return (
    point.x >= rect.x && 
    point.x <= rect.x + rect.width && 
    point.y >= rect.y && 
    point.y <= rect.y + rect.height
  )
}



// 导出需要的方法供模板使用
defineExpose({
  getCurNode
})
</script>

<style  rel="stylesheet/scss" lang="scss" scoped>

.device-node-bg {
  width: 100px;
  height: 100px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  background-color: antiquewhite;
 // border: 4px solid antiquewhite;

  cursor: pointer;
  position: relative;
}

.marker {
  position: absolute;
  width: 20px;

  cursor: pointer;
  border: 1px solid white; /* Optional for better visibility */
}
.marker-circle {
  aspect-ratio: 1 / 1;

  transform: translate(-50%, -50%);
  border-radius: 50%;
}
.marker-rect {
  border-radius: 0;
}
.marker-NONE {
  background-color: rgb(185, 185, 185);
  display: block;
}
.marker-IDLE {
  background-color: white;
  display: block;
}
.marker-IDLE_CONTAINER {
  background-color: rgb(6, 185, 235);
  display: none;
}
.marker-HOLD {
  background-color: rgb(0, 255, 0);
  display: block;
}
.marker-HOLD_RUNNING {
  background-color: rgb(0, 255, 0);
  display: block;
}
.marker-TO_HOLD {
  background-color: rgb(0, 255, 0);
  animation: opacity-blink 1s infinite alternate;
  display: block;
}
.marker-DONE {
  background-color: rgb(0, 140, 255);
  display: block;
}
@keyframes opacity-blink {
    0% {
        background-color: rgba(0, 255, 0, 1);
    }
    100% {
        background-color: rgba(255, 0, 0, 0);
    }
}

.marker-LEAVE {
  background-color: rgb(221, 232, 18);
  display: block;
}
.marker-SELECTED {
  border: 2px solid rgb(233, 126, 3);
  animation: selected-opacity-blink 1s infinite alternate;
  display: block;
}

@keyframes selected-opacity-blink {
    0% {
        border-color: rgba(233, 126, 3, 1);
    }
    100% {
      border-color: rgba(255, 0, 0, 0);
    }
}

.runner {
  display: none;
}
.runner-HOLD_RUNNING {
  display:block;

  width: 100%;
  aspect-ratio: 1;
  border-radius: 50%;
  background:
    radial-gradient(farthest-side,#ffa516 94%,#0000) top/4px 4px no-repeat,
    conic-gradient(#0000 30%,#ffa516);
  -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 4px),#000 0);
  animation: l13 1s infinite linear;
}
@keyframes l13{
  100%{transform: rotate(1turn)}
}
.badge-item {
  margin-top: 10px;
  margin-right: 40px;
  position:absolute;
  z-index: 3998;
}

.editor-container {
  display: flex;
  height: 100px;
  width: 100%;
  .left{
    width: 108px;
    height: 108px;
    background-color: antiquewhite;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .right{
    margin-left: 20px;
    width: 100px;
    display: flex;
    flex-direction: column;
  }
}

.deviceNodeGraph {
  width: 100%;
  height: 100%;
  background-color: antiquewhite;
}

.image-select-col{
  background-color: antiquewhite;
  padding:5px;
  border: 1px solid #fff;
}
.image-select-col:hover {
  background-color:lightskyblue;
  border: 1px solid #ffa516;
  border-radius: 4px;
  cursor: pointer;
}
.image-label {
  display: block;
  width: 60px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

}
</style>
