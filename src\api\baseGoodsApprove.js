import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/baseGoodsApprove',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/baseGoodsApprove/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/baseGoodsApprove',
    method: 'put',
    data
  })
}

export function batchUpdate(data) {
  return request({
    url: 'api/baseGoodsApprove/batchUpdate',
    method: 'put',
    data
  })
}

export default { add, edit, del, batchUpdate }
