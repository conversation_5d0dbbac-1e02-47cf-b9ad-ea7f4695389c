import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/sampleCustomer',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/sampleCustomer/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sampleCustomer',
    method: 'put',
    data
  })
}

export function selectSampleCustomers(params) {
  return request({
    url: 'api/sampleCustomer/selectSampleCustomers',
    method: 'get',
    params
  })
}

export default { add, edit, del, selectSampleCustomers }
