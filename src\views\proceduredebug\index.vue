<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="主键ID">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="工作站名称">
            <el-input v-model="form.name" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="工作站描述">
            <el-input v-model="form.description" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="开启进程方法ID">
            <el-input v-model="form.entryMethodId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="结束进程方法ID">
            <el-input v-model="form.exitMethodId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="DAG图,X6实现">
            <el-input v-model="form.dagJson" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="工作站状态" prop="status">
            <el-input v-model="form.status" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="是否已被删除,Y/N">
            <el-input v-model="form.deleteFlag" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.createBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新人">
            <el-input v-model="form.updateBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="form.updateTime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="主键ID" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="entryMethodId" label="开启进程方法ID" />
        <el-table-column prop="exitMethodId" label="结束进程方法ID" />
        <el-table-column prop="status" label="工作站状态" />
        <el-table-column prop="createBy" label="创建人" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updateBy" label="更新人" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column v-if="checkPer(['admin','procedure:edit','procedure:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudProcedure from '@/api/procedure'
import CRUD, { presenter, header, form, crud } from '@/components/crud/crud'
import rrOperation from '@/components/crud/RR.operation'
import crudOperation from '@/components/crud/crud.operation'
import udOperation from '@/components/crud/UD.operation'
import pagination from '@/components/crud/Pagination'

const defaultForm = { id: null, name: null, description: null, entryMethodId: null, exitMethodId: null, dagJson: null, status: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }
export default {
  name: 'Procedure',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '流程管理', url: 'api/procedure', idField: 'id', sort: 'id,desc', crudMethod: { ...crudProcedure }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'procedure:add'],
        edit: ['admin', 'procedure:edit'],
        del: ['admin', 'procedure:del']
      },
      rules: {
        status: [
          { required: true, message: '工作站状态不能为空', trigger: 'blur' }
        ]
      }}
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    // 新增前将多选的值设置为空
    [CRUD.HOOK.beforeToAdd]() {
      this.$router.push('/config-list/procedure_config')
      return false
    },
    // 初始化编辑时候的角色与岗位
    [CRUD.HOOK.beforeToEdit](crud) {
      this.$router.push(
        {
          path: '/config-list/procedure_config',
          query: { id: crud.form.id, form: crud.form, addOrEdit: 'edit' }
        })
      return false
    }
  }
}
</script>

<style scoped>

</style>
