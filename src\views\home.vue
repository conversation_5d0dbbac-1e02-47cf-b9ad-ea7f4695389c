<template>
  <div ref="dashboardContainer" class="dashboard-container" :style="{'background': graphBackground}">
    <div class="top">
      <header-board @screenfull="toggleFullscreen" />
    </div>
    <div class="middle">
      <div class="left">
        <control-board />
      </div>
      <div class="center">
        <div class="main-board">
          <main-board @graph-created-event="handleGraphCreated" />
        </div>
        <div class="bottom">
          <StationStatusBoard :graph="graph" ref="statusBoard" />
        </div>
      </div>

      <div class="right">
        <task-board />
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import StationStatusBoard from './dashboard/StationStatusBoard'
import ControlBoard from './dashboard/ControlBoard'
import TaskBoard from './dashboard/TaskBoard'
import MainBoard from './dashboard/MainBoard.vue'
import HeaderBoard from './dashboard/HeaderBoard.vue'
import screenfull from 'screenfull'
import { getLatestLayoutStyle } from '@/api/deviceLayout'

const dashboardContainer = ref(null)
const statusBoard = ref(null)
const graph = ref(null)

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
}

// 响应式数据
const currentLineChartData = ref(lineChartData.newVisitis)

const graphBackground = ref(null)

// 方法
const handleSetLineChartData = (type) => {
  currentLineChartData.value = lineChartData[type]
}

const handleGraphCreated = (g) => {
  graph.value = g
}

const toggleFullscreen = () => {
  if (!screenfull.enabled) {
    ElMessage({
      message: 'you browser can not work',
      type: 'warning'
    })
    return false
  }
  screenfull.toggle(dashboardContainer.value)
  nextTick(() => {
    adjustSelectPosition()
  })
}

const adjustSelectPosition = () => {
  const selectDropdown = findComponentByRef(statusBoard.value, 'popper')
  if (selectDropdown) {
    selectDropdown.style.position = 'fixed'
  }
}

const findComponentByRef = (component, ref) => {
  if (!component) return null
  
  if (component.$refs && component.$refs[ref]) {
    return component.$refs[ref]
  }
  
  for (let key in component.$refs) {
    const child = component.$refs[key]
    if (child.$refs && child.$refs[ref]) {
      return child.$refs[ref]
    }
    const result = findComponentByRef(child, ref)
    if (result) return result
  }
  return null
}
const handleGraphBackground = async (data) => {
  const devLayout = await getLatestLayoutStyle()
  if (devLayout && devLayout.styleSetting) {
    const styleSettingObj = JSON.parse(devLayout.styleSetting)
    graphBackground.value = styleSettingObj.background
  }
  //graphBackground.value = data
}
onMounted(() => {
  handleGraphBackground()
})
</script>

<style lang="scss" scoped>
.hasTagsView .dashboard-container {
  height: calc(100vh - 84px);
}
.dashboard-container {
  width: 100%;
  height: calc(100vh - 50px);
  background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
  background-size: cover;
  display: flex;
  flex-direction: column;
  z-index: 1;

  .top {
    display: flex;
    height: 60px;
  }


  .middle {
    flex: 1;
    min-height: 300px;
    display: flex;

    .left {
      width: 290px;
      min-width: 200px;
      max-width: 400px;
      display: flex;
      flex-direction: column;
      padding-right: 10px;
    }
    
    .right {
      width: 400px;
      min-width: 200px;
      max-width: 400px;
      display: flex;
      flex-direction: column;
    }

    .center {
      flex: 1;
      min-width: 300px;
      padding: 0px;
      display: flex;
      flex-direction: column;
      .main-board {
        flex: 1 1 auto;
      }    
      .bottom {
        height: 200px;
        display: flex;
        flex-direction: column;

        .inner {
          flex: 1;
          background-color: #d0d0d0;
          margin: 10px;
        }
      }
    }
  }
}

.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .github-corner {
    position: absolute;
    top: 0;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
