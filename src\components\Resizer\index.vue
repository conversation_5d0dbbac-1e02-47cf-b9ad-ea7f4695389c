<template>
  <div class="resizer" @mousedown="startResize"></div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'

const emit = defineEmits(["onSizeChange"]);


const stencilWidth = ref(260) // 左侧工具栏宽度
// 使用 defineModel 替代 ref 实现双向绑定
const configWidth = defineModel('width', { default: 290 })
const isResizing = ref(false)



// 添加拖动相关的方法
const startResize = (e) => {
  isResizing.value = true
  
  const handleMouseMove = (moveEvent) => {
    if (isResizing.value) {
      // 计算新的右侧面板宽度
      const containerWidth = document.querySelector('.content').offsetWidth
      const newWidth = containerWidth - moveEvent.clientX
      
      // 设置最小和最大宽度限制
      if (newWidth >= 200 && newWidth <= 600) {
        configWidth.value = newWidth
       
      }
    }
  }
  
  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    emit("onSizeChange", configWidth.value)
    // 调整图表大小以适应新的布局
   // if (graph.value) {
   //   const container = document.getElementById('container')
   //   graph.value.resize(container.offsetWidth, container.offsetHeight)
   // }
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  
  // 阻止默认行为和冒泡
  e.preventDefault()
  e.stopPropagation()
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped >

// 添加分隔条样式
.resizer {
  width: 4px;
  height: 100%;
  background-color: #e0e0e0;
  cursor: col-resize;
  transition: background-color 0.2s;
  z-index: 1000;
  
  &:hover, &:active {
    background-color: #1890ff;
  }
}

</style>