<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="动作名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="节点图标">
        <el-radio-group v-model="form.dagNodeImage" class="checked-radio" >
            <el-radio
               v-for="dev in dataStore.devices" :key="dev.id"
               :label="dev.layoutImage||dev.device.layoutImage"
              border
            >
            <template #default>
              <div class="device-icon-container">
                <i 
                  class="device-image" 
                  :style="'background-image: url('+getIconUrl(dev.layoutImage||dev.device.layoutImage)+')'"
                />
              </div>
            </template>
          </el-radio>
          </el-radio-group>
      </el-form-item>
    </el-form>
    <el-divider content-position="left">所属工作站</el-divider>
    <el-form ref="stationFormRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="工作站名" prop="stationName">
        <el-button link @click="openStationDialog">选择或新增工作站</el-button>
      </el-form-item>
      <el-form-item label="设备列表">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, inject, watch, onMounted, computed } from 'vue'
import { Node } from '@antv/x6'
import { useStore } from 'vuex'

// Store
const store = useStore()

// Props 定义
const props = defineProps({
  curNode: {
    type: Object,
    default: () => ({})
  }
})

// 注入
const getDataStore = inject('getDataStore')
//const dataStore = getDataStore()

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  dagNodeImage: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

const formRef = ref(null)
const stationFormRef = ref(null)
const dataStore = reactive({})
const initing = ref(false)
const devPosition = ref([{ xpos: 0.0, ypos: 0.0, zpos: 0.0 }])
// 计算属性
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// 方法定义
const init = () => {
  initing.value = true
}

const openStationDialog = () => {
  console.log('openStationDialog ...')
}

const transformNodeToFormData = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  form.name = curNode.getData().name
  form.description = curNode.getData().description
  form.dagNodeImage = curNode.getData().dagNodeImage || ''
  if (!form.dagNodeImage || form.dagNodeImage === '') {
    if (dataStore.devices.length > 0) {
      form.dagNodeImage = dataStore.devices[0].layoutImage || dataStore.devices[0].device.layoutImage
    }
  }
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  
  for (const actionObj of dataStore.actions || []) {
    if (actionObj.dagNodeId === nodeId) {
      return actionObj
    }
    for (const cmdObj of actionObj.commands || []) {
      if (cmdObj.dagNodeId === nodeId) {
        return cmdObj
      }
    }
  }
  return null
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  curNode.getData().name = form.name
  curNode.getData().description = form.description
  curNode.getData().dagNodeImage = form.dagNodeImage
  
  const storeObj = getStoreObjectByNodeId(curNode.id)
  if (storeObj) {
    storeObj.name = form.name
    storeObj.description = form.description
    storeObj.dagNodeImage = form.dagNodeImage 
  }

  const changeState = 'changed from property'
  curNode.setData({ ...curNode.getData(), changeState })
}

const getIconUrl = (imagUrl) => {
  return imageAccessUrl.value + imagUrl
}

// 监听器
watch(dataStore, () => {
  transformNodeToFormData()
}, { deep: true })

watch(() => props.curNode, () => {
  transformNodeToFormData()
})

watch(form, () => {
  transformFormDataToNode()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  form.name = props.curNode.getData().name
  form.description = props.curNode.getData().description
  transformNodeToFormData()
})
</script>

<style lang="scss" scoped>

.checked-radio{

  text-align: left;
  width:48px;
  height:48px;
  flex-wrap:nowrap;
}
.checked-radio :deep( .el-radio ) {
margin-right: 2px;
margin-left: 0px;
margin-bottom: 0px;
height:48px;
}
.checked-radio :deep( .el-radio.is-bordered+.el-radio.is-bordered ){
margin-left: 0px;
}

.checked-radio :deep( .el-radio__input ){
display: none;
}
.checked-radio :deep( .el-radio__label ){
padding-left: 4px;
}

.checked-radio :deep( .el-radio.is-checked ){

    border-radius:4px ;
    position: relative;
    text-align: center;
    color: #29679A;
   // box-shadow: 0px 2px 7px 0px rgba(85, 110, 97, 0.35);
}

.checked-radio :deep(  .el-radio.is-checked:before ) {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    border: 10px solid #13ce66;
    border-top-right-radius: 4px;
    border-bottom-color: transparent;
    border-left-color: transparent;
}

.checked-radio :deep(  .el-radio.is-checked:after ) {
    content: '';
    width: 6px;
    height: 10px;
    position: absolute;
    right: 2px;
    top: 0px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-left-color: transparent;
    transform: rotate(35deg);
}
.device-image {
  display: block;
  width: 48px;
  height: 48px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
</style>
