<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <el-input v-model="query.name" clearable size="default" placeholder="请输入表名" style="width: 200px;" class="filter-item" @keyup.enter="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
    </div>
  
    <crudOperation :crud="crud">
      <template #right>
        <el-tooltip class="item" effect="dark" content="数据库中表字段变动时使用该功能" placement="top-start">
          <el-button
            class="filter-item"
            size="default"
            type="success"
            :icon="Refresh"
            :loading="syncLoading"
            :disabled="crud.selections.length === 0"
            @click="handleSyncData"
          >同步</el-button>
        </el-tooltip>
      </template>
    </crudOperation>

    <!--表格渲染-->
    <el-table ref="tableRef" v-loading="crud.loading" :data="crud.data" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
      <el-table-column type="selection" width="55" />
      <el-table-column :show-overflow-tooltip="true" prop="tableName" label="表名" />
      <el-table-column :show-overflow-tooltip="true" prop="engine" label="数据库引擎" />
      <el-table-column :show-overflow-tooltip="true" prop="coding" label="字符编码集" />
      <el-table-column :show-overflow-tooltip="true" prop="remark" label="备注" />
      <el-table-column prop="createTime" label="创建日期" />
      <el-table-column label="操作" width="160px" align="center" fixed="right">
        <template #default="scope">
          <el-button size="small" style="margin-right: 2px" type="primary" link>
            <router-link :to="'/sys-tools/generator/preview/' + scope.row.tableName">
              预览
            </router-link>
          </el-button>
          <el-button size="small" style="margin-left: -1px;margin-right: 2px" type="primary" link @click="toDownload(scope.row.tableName)">下载</el-button>
          <el-button size="small" style="margin-left: -1px;margin-right: 2px" type="primary" link>
            <router-link :to="'/sys-tools/generator/config/' + scope.row.tableName">
              配置
            </router-link>
          </el-button>
          <el-button type="primary" style="margin-left: -1px" size="small" link @click="toGen(scope.row.tableName)">生成</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination :crud="crud" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElNotification } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { generator, sync as syncData } from '@/api/generator/generator'
import { downloadFile } from '@/utils/index'
import { useCrud } from '@/hooks/useCrud'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import pagination from '@/components/crud/Pagination.vue'

// 表格引用
const tableRef = ref(null)

// 同步加载状态
const syncLoading = ref(false)

// 使用 crud hook
const { crud, query } = useCrud({
  title: '代码生成',
  url: 'api/generator/tables',
  optShow: { add: false, edit: false, del: false, download: false }
})

// 生成代码
const toGen = (tableName) => {
  generator(tableName, 0).then(() => {
    ElNotification({
      title: '生成成功',
      type: 'success',
      duration: 2500
    })
  })
}

// 打包下载
const toDownload = (tableName) => {
  generator(tableName, 2).then(data => {
    downloadFile(data, tableName, 'zip')
  })
}

// 同步数据库
const handleSyncData = () => {
  const tables = []
  crud.selections.forEach(val => {
    tables.push(val.tableName)
  })
  syncLoading.value = true
  syncData(tables).then(() => {
    crud.refresh()
    crud.notify('同步成功', 'success')
    syncLoading.value = false
  }).catch(() => {
    syncLoading.value = false
  })
}
</script>

<style scoped>
</style>
