import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/procedure',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/procedure/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/procedure',
    method: 'put',
    data
  })
}

export function queryProceduresForSelection() {
  return request({
    url: 'api/procedure/queryProceduresForSelection',
    method: 'get'
  })
}
export function queryAllMethods() {
  return request({
    url: 'api/procedure/queryAllMethods',
    method: 'get'
  })
}
export function queryAllSteps() {
  return request({
    url: 'api/procedure/queryAllSteps',
    method: 'get'
  })
}
export function queryAllActions() {
  return request({
    url: 'api/procedure/queryAllActions',
    method: 'get'
  })
}

export function queryProcedureById(params) {
  return request({
    url: 'api/procedure/queryProcedureById',
    method: 'get',
    params
  })
}

export function queryActionVarDefinesByActionId(params) {
  return request({
    url: 'api/procedure/queryActionVarDefinesByActionId',
    method: 'get',
    params
  })
}
export function queryStepVarDefinesByStepId(params) {
  return request({
    url: 'api/procedure/queryStepVarDefinesByStepId',
    method: 'get',
    params
  })
}
export function queryMethodVarDefinesByMethodId(params) {
  return request({
    url: 'api/procedure/queryMethodVarDefinesByMethodId',
    method: 'get',
    params
  })
}

export function queryAllUnRefProcNodes(params) {
  return request({
    url: 'api/procedure/queryAllUnRefProcNodes',
    method: 'get',
    params
  })
}
export function cleanUnRefProcNodes(data) {
  return request({
    url: 'api/procedure/cleanUnRefProcNodes',
    method: 'put',
    data
  })
}

export function queryMainMethodNamesForSelection() {
  return request({
    url: 'api/procedure/queryMainMethodNamesForSelection',
    method: 'get'
  })
}
export default { add, edit, del, queryProcedureById, queryProceduresForSelection, 
  queryAllMethods, queryAllSteps, queryAllActions, queryActionVarDefinesByActionId,queryStepVarDefinesByStepId,queryMethodVarDefinesByMethodId,
  queryAllUnRefProcNodes, cleanUnRefProcNodes, queryMainMethodNamesForSelection
 }
