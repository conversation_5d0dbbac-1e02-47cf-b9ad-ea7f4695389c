<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="步骤名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="步骤调度模式" prop="scheduleMode">
        <el-select v-model="form.scheduleMode" size="small" placeholder="请选择">
          <el-option v-for="item in dict.data.step_schedule_mode" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <el-divider content-position="left">预置条件</el-divider>
    <PredicatesEditor :cur-node="curNode" />
    <el-form-item label="条件不满足时" prop="unmatchedThen">
      <el-select v-model="form.unmatchedThen" placeholder="请选择" size="small">
        <el-option label="等待" value="WAIT" />
        <el-option label="跳过" value="SKIP" />
      </el-select>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, watch, onMounted } from 'vue'
import { Node } from '@antv/x6'
import { useDict } from '@/hooks/useDict'
import PredicatesEditor from './components/PredicatesEditor.vue'

// Props 定义
const props = defineProps({
  curNode: {
    type: Object as () => Node,
    default: () => ({})
  }
})

// 注入
const getDataStore = inject('getDataStore')

// 使用字典
const { dict } = useDict(['step_schedule_mode'])

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  scheduleMode: null,
  unmatchedThen: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const formRef = ref(null)
const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

const initing = ref(false)
const dataStore = reactive({})

// 方法定义
const init = () => {
  initing.value = true
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  if (!Array.isArray(dataStore.methods)) {
    return null
  }
  
  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    if (!Array.isArray(methodObj.method?.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      if (!Array.isArray(stepObj.step?.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }
  return null
}

const transformNodeToFormData = () => {
  const curNode = props.curNode
  const stepData = getStoreObjectByNodeId(curNode.id)
  
  Object.assign(dataStore, getDataStore())
  
  form.name = curNode.getData().name
  form.description = curNode.getData().description
  form.scheduleMode = stepData?.scheduleMode
  form.unmatchedThen = stepData?.unmatchedThen
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())

  const curNodeData = curNode.getData()
  curNodeData.name = form.name
  curNodeData.description = form.description
  
  const stepData = getStoreObjectByNodeId(curNode.id)
  if (stepData) {
    if (!stepData.step) {
      stepData.step = {}
    }
    stepData.step.name = form.name
    stepData.step.description = form.description
    stepData.scheduleMode = form.scheduleMode
    stepData.unmatchedThen = form.unmatchedThen
  }

  const changeState = 'changed from property'
  curNode.setData({ ...curNodeData, changeState })
}

// 监听器
watch(dataStore, () => {
  transformNodeToFormData()
}, { deep: true })

watch(() => props.curNode, () => {
  transformNodeToFormData()
})

watch(form, () => {
  transformFormDataToNode()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  transformNodeToFormData()
})
</script>

<style lang="scss" scoped>
</style>
