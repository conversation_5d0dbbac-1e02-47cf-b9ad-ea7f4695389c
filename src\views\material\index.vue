<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item label="物料名称" prop="name">
            <el-input
              v-model="query.name"
              clearable
              :placeholder="$t('物料名称')"
              style="width: 200px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="创建时间">
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rr-operation />
          </el-form-item>
        </el-form>
      </div>
    </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crud-operation :permission="permission" :crud="crud" />
      <!--表单组件-->
    <el-drawer 
      align-center 
      append-to-body 
      :close-on-click-modal="false" 
      :before-close="crud.cancelCU" 
      v-model="crud.dialogVisible" 
      :title="crud.status.add ? '新增物料' : '编辑物料'" 
      width="500px"
    >
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="图标" prop="image">
          <upload-image-selector v-model="form.image" />
        </el-form-item>
        <el-form-item label="设备实例" prop="deviceInstanceName">
          <DeviceInstanceInput v-model:deviceInstance="form"  @blur="onDevInstanceBlur" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="总量" prop="totalValue">
          <el-input v-model="form.totalValue" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="剩余量" prop="remainValue">
          <el-input v-model="form.remainValue" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="预警值" prop="warnValue">
          <el-input v-model="form.warnValue" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="异常值" prop="errorValue">
          <el-input v-model="form.errorValue" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="状态" style="width: 120px">
              <el-option
                v-for="item in dict.data.material_status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="点位分布" prop="toUseValue">
          <PositionDetailConfigEditor :deviceInstanceId="form.deviceInstanceId" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" link @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </template>
    </el-drawer>
      <!--表格渲染-->
      <el-table
        ref="tableRef"
        v-loading="crud.loading"
        :data="crud.data"
        size="small"
        style="width: 100%;"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="image" label="图标" >
          <template #default="{ row }">
            <div class="material-image" :style="'background-image:url('+imageAccessUrl+row.image+');'" />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="totalValue" label="总量" />
        <el-table-column prop="remainValue" label="剩余量" />
        <el-table-column prop="toUseValue" label="预估使用量" />
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="status" label="状态" >
          <template #default="{ row }">
            <el-tag>{{getDictLabel('material_status', row.status)}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deviceInstanceName" label="物料存储设备" >
          <template #default="{ row }">
            <PositionDetailConfigPopover :deviceInstanceId="row.deviceInstanceId" :name="row.name">
              <template #reference>
                <el-tag type="info" style="cursor:pointer;">{{row.deviceInstanceName}}</el-tag>
              </template>
            </PositionDetailConfigPopover>
          </template>
        </el-table-column>
        <el-table-column prop="lastAddedBy" label="最近入库人" />
        <el-table-column prop="lastAddedTime" label="最近入库时间" />
        <el-table-column prop="warnValue" label="预警值" />
        <el-table-column prop="lastWarnTime" label="最近预警时间" />
        <el-table-column prop="errorValue" label="异常值" />
        <el-table-column prop="lastErrorTime" label="最近异常时间" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column v-if="checkPer(['admin','procedure:edit','procedure:del'])" label="操作" width="300px" align="center">
          <template #default="{ row }">
            <div class="ud-operation-container">
              <div class="ud-operation-append">
                <el-button
                  v-permission="permission.edit"
                  size="small"
                  type="danger"
                  :icon="Unlock"
                  @click="onDebug(row)" >
                  解锁
                </el-button>
                <el-button
                  v-permission="permission.edit"
                  size="small"
                  type="primary"
                  :icon="VideoPlay"
                  @click="resetMaterial(row)" >
                  复位
                </el-button>
              </div>
              <ud-operation
                :data="row"
                :permission="permission"
                :crud="crud"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    
  </div>
</template>

<script setup>
import { ref, provide, inject, onMounted, computed, readonly } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useCrud } from '@/hooks/useCrud'
import { useDict } from '@/hooks/useDict'
import crudMaterial from '@/api/material'
import CrudOperation from '@/components/crud/CRUD.Operation.vue'
import RrOperation from '@/components/crud/RR.Operation.vue'
import UdOperation from '@/components/crud/UD.Operation.vue'
import Pagination from '@/components/crud/Pagination.vue'
import DateRangePicker from '@/components/DateRangePicker'

import UploadImageSelector from '@/views/components/UploadImageSelector.vue'

import DeviceInstanceInput from '@/views/components/x6/DeviceInstanceInput.vue'

import PositionDetailConfigEditor from './components/PositionDetailConfigEditor.vue'
import PositionDetailConfigPopover from './components/PositionDetailConfigPopover.vue'

import {
  VideoPlay,Unlock
} from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'

const store = useStore()
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// 响应式数据
const { dict, getDictLabel } = useDict(['material_status'])

const defaultForm = {
  id: null,
  number: null,
  name: null,
  description: null,
  image: null,
  totalValue: null,
  remainValue: null,
  unit: null,
  lastAddedBy: null,
  lastAddedTime: null,
  warnValue: null,
  lastWarnTime: null,
  errorValue: null,
  lastErrorTime: null,
  status: 'NORMAL',
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const router = useRouter()
const checkPer = inject('checkPer')
const formRef = ref(null)
const tableRef = ref(null)

const permission = {
  add: ['admin', 'procedure:add'],
  edit: ['admin', 'procedure:edit'],
  del: ['admin', 'procedure:del']
}
// 表单验证规则
const rules = {
  name: [
    { required: true, message: '名称不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}
const { crud, CRUD, query, form } = useCrud({
  title: '流程管理',
  url: 'api/material',
  idField: 'id',
  sort: 'id,desc',
  crudMethod: { ...crudMaterial },
  defaultForm,
  formRef,
  tableRef,
  rules,
  permission
})

const resetMaterial = (row) => {
  debugger
  crudMaterial.reset(row).then(res => {
    crud.toQuery()
    ElNotification.success({
      title: '成功',
      message: '复位成功'
    })
  })
}


onMounted(() => {
  crud.toQuery()
})

// 初始化
provide('crud', crud)
</script>

<style scoped>
.ud-operation-container {
  display: flex;
  flex-direction: row;
}
.ud-operation-append {
  width:180px;
}


.material-image {
  width: 60px;
  height: 60px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  background-color: antiquewhite;
  border: 4px solid antiquewhite;
  border-radius: 4px;
}
</style>
