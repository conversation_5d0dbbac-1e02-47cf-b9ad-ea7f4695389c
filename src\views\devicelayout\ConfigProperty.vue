<template>
  <div class="property-container">
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="启用模拟" label-width="100px" prop="name" class="config-form-item">
        <el-switch
          v-model="form.enableMock"
          active-color="#13ce66"
          inactive-color="#999999"
        />
      </el-form-item>
      <el-divider content-position="left">生产配置</el-divider>
      <el-form-item v-for="item in configs" :key="item.name" :label="item.name" prop="arg1">
        <el-input v-model="item.value" />
      </el-form-item>
      <el-divider content-position="left">模拟配置</el-divider>
      <el-form-item v-for="item in mockConfigs" :key="item.name" :label="item.name" prop="arg2">
        <el-input v-model="item.value" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { Node } from '@antv/x6'
import { debounce } from '@/utils'

// Props
const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  }
})

// Emits
const emit = defineEmits(['update-device-instance'])

// 响应式数据
const defaultForm = { 
  id: null, 
  name: null, 
  description: null, 
  enableMock: null, 
  config: null, 
  mockConfig: null, 
  deleteFlag: null, 
  createBy: null, 
  createTime: null, 
  updateBy: null, 
  updateTime: null 
}

const form = reactive({ ...defaultForm })
const rules = reactive({
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
})

const formRef = ref(null)
const configs = ref([])
const mockConfigs = ref([])
const commands = ref([])
const initing = ref(false)

// 方法
const getCurDeviceInstance = () => {
  const curNode = props.curNode
  const { graph } = curNode.model || {}

  if (!graph) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === curNode.id) {
      return ins
    }
  }
  return null
}

const transformNodeToFormData = () => {
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }
  form.enableMock = deviceInst.enableMock
  configs.value = []
  mockConfigs.value = []
  commands.value = deviceInst.commands

  if (deviceInst.device.config) {
    const configTemplate = JSON.parse(deviceInst.device.config)
    let instConfig = deviceInst.config
    if (instConfig) {
      if (configTemplate.children) {
        instConfig = JSON.parse(instConfig)
      }
    }
    let instMockConfig = deviceInst.mockConfig
    if (instMockConfig) {
      if (configTemplate.children) {
        instMockConfig = JSON.parse(instMockConfig)
      }
    }
    
    if (configTemplate.children) {
      configTemplate.children.forEach(item => {
        configs.value.push({
          key: item.key,
          name: item.name,
          value: (!instConfig) ? '' : instConfig[item.key]
        })
        mockConfigs.value.push({
          key: item.key,
          name: item.name,
          value: (!instMockConfig) ? '' : instMockConfig[item.key]
        })
      })
    } else {
      configs.value.push({
        name: configTemplate.name,
        value: instConfig
      })
      mockConfigs.value.push({
        name: configTemplate.name,
        value: instMockConfig
      })
    }
  }
  
  form.name = deviceInst.name
  form.description = deviceInst.description
}

const transformFormDataToNode = () => {
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }
  deviceInst.enableMock = form.enableMock
  
  if (deviceInst.device.config) {
    const configTemplate = JSON.parse(deviceInst.device.config)
    if (configTemplate.children) {
      const instConfigObj = {}
      configs.value.forEach(item => {
        instConfigObj[item.key] = item.value
      })
      deviceInst.config = JSON.stringify(instConfigObj)
      const instMockConfigObj = {}
      mockConfigs.value.forEach(item => {
        instMockConfigObj[item.key] = item.value
      })
      deviceInst.mockConfig = JSON.stringify(instMockConfigObj)
    } else {
      deviceInst.config = configs.value[0].value
      deviceInst.mockConfig = mockConfigs.value[0].value
    }
  }

  const curNode = props.curNode
  const changeState = 'changed from property'
  curNode.setData({ ...curNode.getData(), changeState })
  emit('update-device-instance', deviceInst)
}

// 创建防抖的更新函数
const debouncedTransformFormDataToNode = debounce(() => {
  transformFormDataToNode()
}, 300)

// 监听器
watch(() => props.curNode, (newVal, oldVal) => {
  transformNodeToFormData()
  console.log(`curNode changed from ${oldVal} to ${newVal}`)
})

watch(
  () => form.name,
  (newVal, oldVal) => {
    console.log(`form.name changed from "${oldVal}" to "${newVal}"`)
    debouncedTransformFormDataToNode()
  }
)

watch(
  () => form.description,
  (newVal, oldVal) => {
    console.log(`description changed from "${oldVal}" to "${newVal}"`)
    debouncedTransformFormDataToNode()
  }
)

watch(
  [() => form.enableMock, () => configs.value, () => mockConfigs.value],
  () => {
    debouncedTransformFormDataToNode()
  },
  { deep: true }
)

// 生命周期
onMounted(() => {
  console.log('ConfigProperty created..')
  transformNodeToFormData()
})
</script>

<style lang="scss" scoped>
.property-container {
  width: 100%;
}

:deep(.el-form-item__label label) {
  font-weight: 400;
}
</style>
