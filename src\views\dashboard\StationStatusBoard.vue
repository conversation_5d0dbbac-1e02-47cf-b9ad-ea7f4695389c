<template>
  <div class="station-status-container">
    <el-carousel 
      :interval="5000" 
      :autoplay="false" 
      height="200px"
      @change="handleCarouselChange"
    >
      <el-carousel-item v-for="(group, index) in stationGroups" :key="index">
        <div class="carousel-card-group">
          <div 
            v-for="station in group" 
            :key="station.id" 
            class="carousel-card"
          >
            <el-badge 
              v-if="station.actionQueueSize > 0" 
              :value="station.actionQueueSize" 
              class="station-inner-container"
            >
              <station-card :station="station" />
            </el-badge>

            <div v-else class="station-inner-container">
              <station-card :station="station" />
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import { queryAllStationsInRunning } from '@/api/station'
import StationCard from './components/StationCard.vue'

// store
const store = useStore()
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// 状态
const stationGroups = ref([])
const currentCarouselIndex = ref(0)

// WebSocket 状态
const websocketState = ref('NONE') // OPENED, CLOSED
let ws = null

// 重新分组站点数据
const reGroupStations = (stations) => {
  // 按照actionQueueSize排序
  const sortedData = stations.sort((a, b) => {
    const aV = a.actionQueueSize > 0 ? 0 : 1
    const bV = b.actionQueueSize > 0 ? 0 : 1
    return aV - bV
  })

  // 每组5个站点
  let idx = -1
  for (let i = 0; i < sortedData.length; i += 5) {
    const gdata = sortedData.slice(i, i + 5)
    idx++
    if (idx < stationGroups.value.length) {
      stationGroups.value.splice(idx, 1, gdata)
    } else {
      stationGroups.value.push(gdata)
    }
  }
}

// 初始化数据
const initData = async () => {
  try {
    const res = await queryAllStationsInRunning()
    reGroupStations(res)
    
    // 设置下一次刷新
    setTimeout(() => {
      initData()
    }, 2000)
  } catch (error) {
    console.error('获取站点数据失败:', error)
  }
}

// 生成唯一ID
const generateUniqueId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// WebSocket 连接
const connect = () => {
  const wsUri = import.meta.env.VITE_APP_WS_API + '/webSocket/taskMonitor_' + generateUniqueId()
  ws = new WebSocket(wsUri)

  ws.onopen = () => {
    websocketState.value = 'OPENED'
    console.log('WebSocket connection opened')
  }

  ws.onmessage = (event) => {
    console.log('StatusBoard Received message:', event.data)
    const obj = JSON.parse(event.data)
    onTaskResponse(obj)
  }

  ws.onclose = (event) => {
    websocketState.value = 'CLOSED'
    console.log('WebSocket connection closed:', event)
    // 自动重连
    setTimeout(connect, 5000)
  }

  ws.onerror = (error) => {
    console.log('WebSocket error:', error)
    ws.close()
    websocketState.value = 'CLOSED'
  }
}

// 断开 WebSocket
const disconnect = () => {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.close(1000, 'Client closed connection')
    console.log('WebSocket connection is closing')
    websocketState.value = 'CLOSED'
  }
}

// 任务响应处理
const onTaskResponse = (data) => {
  const dto = JSON.parse(data.msg)
  // 处理任务响应数据
  console.log('Task response:', dto)
}

// 轮播切换事件
const handleCarouselChange = (index) => {
  currentCarouselIndex.value = index
}

// 生命周期钩子
onMounted(() => {
  initData()
  connect()
})

onBeforeUnmount(() => {
  disconnect()
})
</script>

<style lang="scss" scoped>
.station-status-container {
  width: 100%;
  padding-left: 8px;
}

.carousel-card-group {
  display: flex;
  flex-direction: row;
}

.carousel-card {
  width: 20%;
  margin: 10px 5px;
  height: 180px;
  background-color: #158CBE40;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  border: 1px solid #238DBD;
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.station-inner-container {
  width: 100%;
  height: 100%;
}
</style>
