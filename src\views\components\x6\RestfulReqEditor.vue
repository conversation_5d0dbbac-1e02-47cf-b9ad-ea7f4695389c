<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="URL" prop="url" label-position="top">
        <el-input v-model="form.url" placeholder="请输入内容" class="input-with-select" :readonly="readonly">
        </el-input>
      </el-form-item>
      <el-form-item label="请求参数" prop="parameter" label-position="top">
        <VueJsonPretty 
          v-model:data="form.parameter" 
          :editable="false"
          :showDoubleQuotes="true"
          :showLength="true"
        >
          <template #renderNodeValue="{ node, defaultValue }">
            <!-- 根据节点类型和路径提供不同的编辑控件 -->
            <template v-if="!readonly">
              <!-- 对于类型字段使用下拉选择框 -->
              <el-select 
                v-if="isSelectField(node)"
                v-model="node.content.value"
                placeholder="请选择类型"
                size="small"
                style="width: 100%"
                @change="(val) => updateNodeValue(node, val)"
              >
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
              </el-select>
              
              <!-- 对于布尔类型使用开关 -->
              <el-switch 
                v-else-if="typeof node.content.value === 'boolean'"
                v-model="node.content.value"
                @change="(val) => updateNodeValue(node, val)"
              />
              
              <!-- 对于数字类型使用数字输入框 -->
              <el-input-number 
                v-else-if="typeof node.content.value === 'number'"
                v-model="node.content.value"
                :controls-position="'right'"
                size="small"
                style="width: 100%"
                @change="(val) => updateNodeValue(node, val)"
              />
              
              <!-- 对于特定字段使用弹出框编辑按钮 
              <div 
                v-else-if="isGetPositionCodeField(node)"
                class="custom-editor-trigger"
                @click="openEditDialog(node)"
              >
                <span>{{ node.content }}</span>
              </div>
              -->
              <PositionFunctionEditor  v-else-if="isGetPositionCodeField(node)"
               v-model:content="node.content" @blur="updateNodeValueFromContent(node)"/>
              
              <!-- 默认使用默认渲染 -->
              <el-input
                v-else-if="node.showEditorInput"
                v-model="node.content"
                size="small"
                :data-path="node.path"
                class="vjp-node"
                @blur=" (val) => { updateNodeValueFromContent(node); node.showEditorInput = false}"
              />
              <span v-else @click="triggerEditor(node)" >{{ node.content }}</span>

            </template>
            
            <!-- 只读模式下使用默认渲染 -->
            <span v-else >{{ node.content }}</span>
          </template>
        </VueJsonPretty>
      </el-form-item>
    </el-form>
    
    <!-- 自定义弹出框 -->
    <el-dialog v-model="dialogVisible" title="编辑字段值" width="30%">
      <el-form>
        <el-form-item label="值">
          <component 
            :is="currentEditor.component" 
            v-model="currentEditor.value" 
            v-bind="currentEditor.props"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEdit">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, watchEffect, onMounted, inject, nextTick } from 'vue'
import { Graph } from '@antv/x6'
import { Selection } from '@antv/x6-plugin-selection'
import { register } from '@antv/x6-vue-shape'
import { Search } from '@element-plus/icons-vue'
import { getLatest } from '@/api/deviceLayout'
import DeviceNode from '@/views/devicelayout/DeviceNode.vue'
import PositionFunctionEditor from '@/views/components/x6/PositionFunctionEditor.vue'
import { Edit } from '@element-plus/icons-vue'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'

// Props
const props = defineProps({
  curNode: {
    type: Object,
    default: () => ({})
  },
  formParameters: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// 注入
const getDataStore = inject('getDataStore')

// 响应式状态
const defaultForm = {
  url: null,
  parameter: {}
}

const formRef = ref(null)
const form = reactive({ ...defaultForm })
const rules = {
  url: [
    { required: true, message: 'URL不能为空', trigger: 'blur' }
  ]
}

// 自定义编辑器相关状态
const dialogVisible = ref(false)
const currentEditor = reactive({
  node: null,
  value: null,
  component: 'el-input',
  props: {}
})

const triggerEditor = (node) => {
  node.showEditorInput = true
  // 使用 nextTick 确保 DOM 更新后再获取焦点
  nextTick(() => {
    // 查找当前节点对应的输入框元素
    const selector = `.el-input input[data-path="${node.path}"]`
    const inputElement = document.querySelector(selector)
    if (inputElement) {
      inputElement.focus()
    }
  })
}

// 判断是否为类型字段
const isSelectField = (node) => {
  
  return false
  const pathStr = path.join('.')
  return pathStr === 'type' || pathStr.endsWith('.type')
}

// 判断是否为复杂字段（需要弹窗编辑）
const isGetPositionCodeField = (node) => {
  
  const content = node.content
  if (!content) return false
  if (typeof content !== 'string'){
    return false
  }
  if (content.startsWith('${GET_POSITION_CODE')){
    return true
  }
  return false
}

// 获取显示值
const getDisplayValue = (value) => {
  if (value === null || value === undefined) {
    return '点击编辑'
  }
  if (typeof value === 'object') {
    return '点击编辑对象'
  }
  return String(value)
}

// 更新节点值的辅助函数
const updateNodeValue = (node, newValue) => {
  // 获取节点路径
  const path = node.path.split('.') || []
  
  // 更新 form.parameter 中对应的值
  if (path.length === 0) return
  
  let target = form.parameter
  for (let i = 1; i < path.length - 1; i++) {
    const key = path[i]
    if (target[key] === undefined || target[key] === null) {
      target[key] = typeof path[i + 1] === 'number' ? [] : {}
    }
    target = target[key]
  }
  
  const lastKey = path[path.length - 1]
  target[lastKey] = newValue
  
  // 更新节点内容以保持显示一致
  node.content = String(newValue)
  
  // 确保更新到表单数据
  transformFormDataToNode()
}

// 从节点内容更新节点值
const updateNodeValueFromContent = (node) => {
  // 获取节点路径
  const path = node.path || []
  
  // 尝试解析节点内容
  let newValue = node.content
  
  // 根据内容类型进行转换
  if (typeof newValue === 'string') {
    // 尝试将字符串转换为数字或布尔值
    if (newValue === 'true') {
      newValue = true
    } else if (newValue === 'false') {
      newValue = false
    } else if (!isNaN(Number(newValue)) && newValue.trim() !== '') {
      newValue = Number(newValue)
    }
  }
  
  // 更新值
  updateNodeValue(node, newValue)
}

// 打开编辑对话框
const openEditDialog = (node) => {
  currentEditor.node = node
  currentEditor.value = node.content.value
  
  if (typeof node.content.value === 'object') {
    currentEditor.component = 'el-input'
    currentEditor.props = { 
      type: 'textarea', 
      rows: 4,
      placeholder: '请输入JSON对象',
      modelValue: JSON.stringify(node.content.value, null, 2)
    }
  } else {
    currentEditor.component = 'el-input'
    currentEditor.props = { 
      type: 'textarea', 
      rows: 4,
      placeholder: '请输入值'
    }
  }
  
  dialogVisible.value = true
}

// 确认编辑
const confirmEdit = () => {
  if (currentEditor.node) {
    try {
      // 如果是对象类型，尝试解析JSON
      if (typeof currentEditor.node.content.value === 'object' && currentEditor.value) {
        currentEditor.node.content.value = JSON.parse(currentEditor.value)
      } else {
        currentEditor.node.content.value = currentEditor.value
      }
      
      // 确保更新到表单数据
      transformFormDataToNode()
    } catch (e) {
      console.error('解析JSON失败:', e)
    }
  }
  dialogVisible.value = false
}

const transformNodeToFormData = () => {
  const formParameters = props.formParameters
  if (!formParameters) return
  form.url = formParameters[0].value
  const parameter = formParameters[1].value
  if (parameter) {
    try {
      form.parameter = JSON.parse(parameter)
    } catch (e) {
      console.error('解析参数失败:', e)
      form.parameter = {}
    }
  }
}

const transformFormDataToNode = () => {
  
  const formParameters = props.formParameters
  if (!formParameters) return

  formParameters[0].value = form.url
  formParameters[1].value = JSON.stringify(form.parameter)
}

watch(() => props.formParameters, () => {
  transformNodeToFormData()
},{ deep: true})

watch(form, () => {
  transformFormDataToNode()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  console.log('RestfulReqEditor mounted..')
  transformNodeToFormData()
})

watchEffect(() => {
  console.log(' props.formParameters=' + props.formParameters)
})
</script>

<style lang="scss">
.dev-graph-container {
  width: 800px;
  height: 500px;
  
  #main_board_container {
    height: 100%;
    background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
    background-size: cover;
  }
}

// 自定义编辑器触发器样式
.custom-editor-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 2px 5px;
  border: 1px dashed transparent;
  
  &:hover {
    border-color: #409eff;
    background-color: rgba(64, 158, 255, 0.1);
  }
}

.vjp-node {
  display: inline;
}

</style>