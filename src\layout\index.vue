<template>
  <div>
    <div v-if="naviMode === 'horizontal'" :class="classObj" class="app-wrapper layout-top">
      <MenuNavibar />
      <app-main />
    </div>
    <div v-if="naviMode !== 'horizontal'" :class="classObj" class="app-wrapper">
      <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
      <sidebar v-if="!isFullScreen" class="sidebar-container" />
      <div :class="{ hasTagsView: needTagsView }" class="main-container">
        <div v-if="!isFullScreen" :class="{ 'fixed-header': fixedHeader }">
          <navbar />
          <tags-view v-if="needTagsView" />
        </div>
        <app-main />
        <right-panel v-if="showSettings">
          <settings />
        </right-panel>
      </div>
      <!--  防止刷新后主题丢失 
      <Theme v-show="false" ref="themeRef" />
       -->
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import RightPanel from '@/components/RightPanel'
import { AppMain, Navbar, MenuNavibar, Settings, Sidebar, TagsView } from './components'
import useResize from '@/hooks/useResize'
import Theme from '@/components/ThemePicker'
import Cookies from 'js-cookie'

// 状态管理
const store = useStore()
const { t } = useI18n()

// 响应式状态
const isFullScreen = ref(false)
const naviMode = ref('horizontal')
const themeRef = ref(null)

// 从 store 获取状态
const sidebar = computed(() => store.state.app.sidebar)
const device = computed(() => store.state.app.device)
const showSettings = computed(() => store.state.settings.showSettings)
const needTagsView = computed(() => store.state.settings.tagsView)
const fixedHeader = computed(() => store.state.settings.fixedHeader)
const roles = computed(() => store.state.user.user.roles)

// 计算 class 对象
const classObj = computed(() => {
  return {
    hideSidebar: !sidebar.value.opened,
    openSidebar: sidebar.value.opened,
    withoutAnimation: sidebar.value.withoutAnimation,
    mobile: device.value === 'mobile'
  }
})

// 使用 resize 钩子
useResize()

// 生命周期钩子
onMounted(() => {
  /*
  if (Cookies.get('theme')) {
    themeRef.value.theme = Cookies.get('theme')
    store.dispatch('settings/changeSetting', {
      key: 'theme',
      value: Cookies.get('theme')
    })
  }
    */

  if (roles.value && roles.value.length > 0 && t(roles.value[0].name) === t('实验员')) {
    naviMode.value = 'horizontal'
  }
  
  // 加载系统设置
  store.dispatch('setGlobalSettingLatestOne')
})

// 方法
const handleClickOutside = () => {
  store.dispatch('app/closeSideBar', { withoutAnimation: false })
}

const toggleFullScreen = (fullScreen) => {
  isFullScreen.value = fullScreen
  if (fullScreen) {
    enterFullScreen()
  } else {
    exitFullScreen()
  }
}

const enterFullScreen = () => {
  const elem = document.documentElement
  if (elem.requestFullscreen) {
    elem.requestFullscreen()
  } else if (elem.mozRequestFullScreen) { // Firefox
    elem.mozRequestFullScreen()
  } else if (elem.webkitRequestFullscreen) { // Chrome, Safari, and Opera
    elem.webkitRequestFullscreen()
  } else if (elem.msRequestFullscreen) { // IE/Edge
    elem.msRequestFullscreen()
  }
}

const exitFullScreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  } else if (document.mozCancelFullScreen) { // Firefox
    document.mozCancelFullScreen()
  } else if (document.webkitExitFullscreen) { // Chrome, Safari, and Opera
    document.webkitExitFullscreen()
  } else if (document.msExitFullscreen) { // IE/Edge
    document.msExitFullscreen()
  }
}

// 暴露方法给父组件使用
defineExpose({
  toggleFullScreen
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/mixin.scss' as *;
@use '@/assets/styles/variables.scss' as *;

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
  padding: 0;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px)
}

.mobile .fixed-header {
  width: 100%;
}

body .el-menu--horizontal :deep(.svg-icon) {
  margin-right: 6px;
}

.layout-top {
  .navbar {
    position: sticky;
    z-index: 999;
    display: flex;
    width: 100% !important;
    height: $navbar-height;

    :deep(.el-scrollbar) {
      flex: 1;
      height: $navbar-height;
    }

    :deep(.el-menu-item),
    :deep(.el-sub-menu__title),
    :deep(.el-menu--horizontal) {
      height: $navbar-height;
      line-height: $navbar-height;
      border-bottom: none;
    }

    :deep(.el-menu--collapse) {
      width: 100%;
    }
  }

  .main-container {
    height: calc(100vh - $navbar-height);
    margin-left: 0;
  }
}
</style>
