# 检测值趋势图优化 - 混合图表实现

## 概述

已成功优化"检测值趋势图"，实现了统计数量（柱状图）+ 平均值（折线图）的混合显示模式。新的 `updateLineChart` 函数支持多特征数据的可视化分析。

## 🔄 主要改进

### 1. 数据格式适配
- ✅ 适配新的API数据格式：`[{featureName, fCreateTime, cnt, avgValue}]`
- ✅ 自动数据分组和时间排序
- ✅ 多特征数据处理

### 2. 混合图表实现
- ✅ **柱状图**：显示统计数量 (cnt)
- ✅ **折线图**：显示平均值 (avgValue)
- ✅ **双Y轴**：左轴-数量，右轴-平均值
- ✅ **多特征**：支持同时显示多个检测指标

### 3. 视觉优化
- ✅ 差异化颜色方案
- ✅ 时间轴格式化显示
- ✅ 交互式图例控制
- ✅ 数据缩放功能

## 📊 数据流程

### 输入数据格式
```javascript
// API返回的原始数据
[
  { featureName: 'pH值', fCreateTime: '2023-01-01 08:30:15', cnt: 15, avgValue: 7.2 },
  { featureName: 'pH值', fCreateTime: '2023-01-01 14:20:30', cnt: 12, avgValue: 7.1 },
  { featureName: '溶解氧', fCreateTime: '2023-01-01 08:30:15', cnt: 8, avgValue: 8.5 },
  { featureName: '溶解氧', fCreateTime: '2023-01-01 14:20:30', cnt: 6, avgValue: 8.3 },
  // ... 更多数据
]
```

### 数据处理流程
```javascript
function processRawDataForChart(rawData) {
  // 1. 按时间排序
  const sortedData = rawData.sort((a, b) => new Date(a.fCreateTime) - new Date(b.fCreateTime))
  
  // 2. 提取唯一时间点和特征
  const timeAxis = [...new Set(rawData.map(item => item.fCreateTime))].sort()
  const features = [...new Set(rawData.map(item => item.featureName))]
  
  // 3. 构建数据映射
  const featureDataMap = {}
  features.forEach(feature => {
    featureDataMap[feature] = {
      cntData: new Array(timeAxis.length).fill(0),
      avgValueData: new Array(timeAxis.length).fill(null)
    }
  })
  
  // 4. 填充数据
  sortedData.forEach(item => {
    const timeIndex = timeAxis.indexOf(item.fCreateTime)
    featureDataMap[item.featureName].cntData[timeIndex] = item.cnt
    featureDataMap[item.featureName].avgValueData[timeIndex] = item.avgValue
  })
  
  return { timeAxis, features, featureDataMap }
}
```

### 输出图表格式
```javascript
// 处理后的数据结构
{
  timeAxis: ['2023-01-01 08:30:15', '2023-01-01 14:20:30', ...],
  features: ['pH值', '溶解氧', '浊度'],
  featureDataMap: {
    'pH值': {
      cntData: [15, 12, 18, 10],
      avgValueData: [7.2, 7.1, 7.3, 7.0]
    },
    '溶解氧': {
      cntData: [8, 6, 10, 5],
      avgValueData: [8.5, 8.3, 8.7, 8.1]
    }
  }
}
```

## 🎨 图表配置

### 混合序列生成
```javascript
// 为每个特征生成柱状图和折线图序列
processedData.features.forEach((feature, index) => {
  // 柱状图 - 统计数量
  seriesData.push({
    name: `${feature}-数量`,
    type: 'bar',
    yAxisIndex: 0,  // 使用左Y轴
    data: featureData.cntData,
    itemStyle: { color: barColors[index % barColors.length] }
  })
  
  // 折线图 - 平均值
  seriesData.push({
    name: `${feature}-平均值`,
    type: 'line',
    yAxisIndex: 1,  // 使用右Y轴
    data: featureData.avgValueData,
    lineStyle: { color: lineColors[index % lineColors.length] }
  })
})
```

### 双Y轴配置
```javascript
yAxis: [
  {
    type: 'value',
    name: '统计数量',
    position: 'left',
    axisLabel: { color: '#5470c6' },
    axisLine: { lineStyle: { color: '#5470c6' } }
  },
  {
    type: 'value',
    name: '平均值',
    position: 'right',
    axisLabel: { color: '#fc8452' },
    axisLine: { lineStyle: { color: '#fc8452' } }
  }
]
```

### 时间轴优化
```javascript
xAxis: {
  type: 'category',
  data: processedData.timeAxis,
  axisLabel: {
    formatter: (value) => {
      // 格式化为两行显示：日期 + 时间
      if (value.includes(' ')) {
        const [date, time] = value.split(' ')
        return `${date}\n${time.substring(0, 5)}`
      }
      return value
    },
    rotate: 45
  }
}
```

## 🎯 功能特性

### 1. 数据可视化
- **柱状图**：直观显示各时间点的统计数量
- **折线图**：清晰展示平均值的变化趋势
- **多特征**：同时对比多个检测指标
- **时间序列**：按时间顺序展示数据变化

### 2. 交互功能
- **图例控制**：点击图例显示/隐藏特定序列
- **数据缩放**：支持鼠标滚轮和拖拽缩放
- **工具提示**：鼠标悬停显示详细数值
- **十字准线**：精确查看数据点

### 3. 视觉设计
- **颜色区分**：柱状图和折线图使用不同色系
- **双Y轴标识**：左右Y轴使用不同颜色标识
- **时间格式化**：优化时间显示避免重叠
- **响应式布局**：自适应容器大小

## 🔧 使用方式

### API参数更新
```javascript
// 全局筛选参数已更新
const globalFilters = reactive({
  featureCreateTime: null,  // 原 dateRange
  methodName: null,         // 原 taskMethodName
  sampleCategory: null      // 原 sampleType
})

// 图表级筛选
const lineChartFilters = reactive({
  detectionItem: null  // 对应 featureName
})
```

### 调用方式
```javascript
// 手动刷新
await updateLineChart()

// 筛选变化时自动调用
<el-select @change="updateLineChart">
```

## 📱 响应式特性

- **自适应布局**：图表自动适应容器大小变化
- **滚动图例**：特征过多时图例支持滚动显示
- **数据缩放**：内置缩放控件，支持大数据量浏览
- **移动端优化**：触摸友好的交互体验

## 🧪 测试验证

### 测试数据
```javascript
const testData = [
  { featureName: 'pH值', fCreateTime: '2023-01-01 08:30:15', cnt: 15, avgValue: 7.2 },
  { featureName: 'pH值', fCreateTime: '2023-01-01 14:20:30', cnt: 12, avgValue: 7.1 },
  { featureName: '溶解氧', fCreateTime: '2023-01-01 08:30:15', cnt: 8, avgValue: 8.5 }
]
```

### 验证要点
- ✅ 数据格式转换正确性
- ✅ 时间轴排序准确性
- ✅ 多特征数据分组
- ✅ 柱状图和折线图数据对应
- ✅ 双Y轴刻度合理性
- ✅ 交互功能完整性

## 🔮 扩展建议

- [ ] 添加数据导出功能
- [ ] 支持数据预警阈值线
- [ ] 实现数据钻取功能
- [ ] 添加统计汇总信息
- [ ] 支持自定义时间粒度
- [ ] 实现数据对比模式

## 📝 注意事项

1. **数据完整性**：确保每个时间点的数据完整
2. **性能考虑**：大数据量时建议分页或采样
3. **颜色一致性**：保持同一特征的柱状图和折线图颜色关联
4. **Y轴范围**：注意统计数量和平均值的数值范围差异
