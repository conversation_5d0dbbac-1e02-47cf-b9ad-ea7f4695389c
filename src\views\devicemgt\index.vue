<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item label="设备名称" prop="name">
            <el-input
              v-model="query.name"
              clearable
              placeholder="设备名称"
              style="width: 200px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="设备类型" prop="type">
            <el-select v-model="query.type" clearable placeholder="类型" style="width: 120px">
              <el-option v-for="item in dict.data.device_types" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('创建时间')" prop="createTime">
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rrOperation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" :crud="crud" />
      <!--表单组件-->
      <el-drawer
        align-center
        append-to-body
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :model-value="crud.status.cu > 0"
        :title="crud.status.title"
        width="500px"
      >
        <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="设备名称" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item label="设备描述">
            <el-input v-model="form.description"  />
          </el-form-item>
          <el-form-item label="设备类型">
            <el-select v-model="form.type" clearable placeholder="类型"  >
              <el-option
                v-for="item in dict.data.device_types"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="设备厂商" prop="manufacturer">
            <el-input v-model="form.manufacturer" />
          </el-form-item>
          <el-form-item label="设备型号" prop="model">
            <el-input v-model="form.model"  />
          </el-form-item>
          <el-form-item label="设备图像">
            <DeviceImageEditor  v-model:device="form" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="指令实现类">
            <el-input v-model="form.javaClassName"  disabled />
          </el-form-item>
          <el-form-item label="指令列表">
            <el-table
              ref="tableCommandsRef"
              :data="form.commands"
              row-key="id"
            >
              <el-table-column prop="name" label="指令名称" />
              <el-table-column prop="parameterTemplate" label="参数模板" >
                <template #default="{ row }">
                  <JsonInput v-model="row.parameterTemplate" readonly style="width: 100%;" />
                </template>
              </el-table-column>
              <el-table-column prop="description" label="指令描述" />
            </el-table>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="text" @click="crud.cancelCU">取消</el-button>
            <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">
              确认
            </el-button>
          </div>
        </template>
      </el-drawer>
      <!--表格渲染-->
      <el-table
        ref="tableRef"
        v-loading="crud.loading"
        :data="crud.data"
        row-key="id"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="layoutImage" label="设备图像" width="80">
          <template #default="{ row }">
            <div class="device-image" :style="'background-image:url('+imageAccessUrl+row.layoutImage+');'" />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="设备名称" />
        <el-table-column prop="description" label="设备描述" />
        <el-table-column prop="type" label="设备类型" />
        <el-table-column prop="manufacturer" label="设备厂商" />
        <el-table-column prop="model" label="设备型号" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column v-if="checkPer(['admin','device:edit','device:del'])" label="操作" width="150px" align="center">
          <template #default="{ row }">
            <udOperation
              :data="row"
              :permission="permission"
              :crud="crud"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination :crud="crud" />
    
  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted, computed } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { useDict } from '@/hooks/useDict'
import { useStore } from 'vuex'
import crudDevice from '@/api/device'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import udOperation from '@/components/crud/UD.operation.vue'
import pagination from '@/components/crud/Pagination'
import DeviceImageEditor from './DeviceImageEditor.vue'
import DateRangePicker from '@/components/DateRangePicker'

import JsonInput from '@/components/JsonInput'

// 初始化表单
const defaultForm = {
  id: null,
  name: null,
  description: null,
  type: null,
  manufacturer: null,
  model: null,
  javaClassName: null,
  commands: [],
  layoutImage: null
}
const rules = {
  name: [
    { required: true, message: '设备名称不能为空', trigger: 'blur' }
  ]
}

// refs
const formRef = ref(null)
const tableRef = ref(null)

// 使用CRUD钩子
const { crud, query, form } = useCrud({
  title: '设备管理',
  url: 'api/device',
  crudMethod: { ...crudDevice },
  defaultForm,
  rules,
  formRef,
  tableRef
})

// 权限校验
const checkPer = inject('checkPer')

// 响应式数据
const { dict } = useDict(['device_types'])

const permission = {
  add: ['admin', 'device:add'],
  edit: ['admin', 'device:edit'],
  del: ['admin', 'device:del']
}


// 图片相关

const store = useStore()
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// 初始化字典
const initDict = () => {
  // 这里替换为实际的字典获取逻辑
  dict.device_types = [
    { value: '1', label: '类型1' },
    { value: '2', label: '类型2' }
  ]
}

// 生命周期
onMounted(() => {
  initDict()
  crud.refresh()
})
</script>

<style scoped>

.device-image {
  width: 60px;
  height: 60px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  background-color: antiquewhite;
  border: 4px solid antiquewhite;
  border-radius: 4px;
}

</style>
