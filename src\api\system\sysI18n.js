import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/sysI18n',
    method: 'post',
    data
  })
}
export function batchSave(data) {
  return request({
    url: 'api/sysI18n/batchSave',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/sysI18n/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sysI18n',
    method: 'put',
    data
  })
}

export default { add, edit, del, batchSave }
