<template>
  <div class="property-container">

    <!--表单组件-->
    <el-dialog 
      align-center
      append-to-body
     :close-on-click-modal="false" 
     :model-value="crud.dialogVisible" 
     :title="'设备命令编辑'" 
     width="500px" 
     @open="openCommandEditDialog"
     @close="crud.cancelCU">
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="代理设备指令" prop="proxyInstanceCmd.id">
          <el-select v-model="form.proxyInstanceCmd.id" filterable placeholder="请选择" style="width: 370px" @change="onProxyCmdChange">
            <el-option v-for="item in candidateCommands" :key="item.id" :label="item.deviceInstanceName+' '+item.name" :value="item.id" >
              <span>
                <label style="color: #8492a6; padding-right:10px;">{{ item.deviceInstanceName }}</label>
                <span style="color: #c0c4cc;">{{ item.name }}</span>
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="命令名称" prop="name">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="命令描述">
          <el-input v-model="form.description" style="width: 370px;" />
        </el-form-item>
        <el-divider content-position="left">指令参数</el-divider>
        <el-form-item v-for="item in form.parameters" :key="item.name" :label="item.name" prop="arg1">
          <el-input v-model="item.value" type="textarea"
          :rows="1"  style="width: 370px;" />
        </el-form-item>
        <el-divider content-position="left">选项</el-divider>
        <el-form-item label="指令类型" prop="type">
          <el-select v-model="form.type" filterable placeholder="请选择" style="width: 370px">
            <el-option :label="'NORMAL'" :value="'NORMAL'" />
            <el-option :label="'STATUS'" :value="'STATUS'" />
            <el-option :label="'INIT'" :value="'INIT'" />
          </el-select>
        </el-form-item>
        <el-form-item label="后处理脚本" prop="postExecution">
          <LesScriptInput v-model="form.postExecution" style="width: 370px" />
        </el-form-item>
        <el-form-item label="控件编码" prop="bindControlCode">
          <el-select v-model="form.bindControlCode" filterable placeholder="请选择" style="width: 370px">
            <el-option :label="'LOCK'" :value="'LOCK'" />
            <el-option :label="'UNLOCK'" :value="'UNLOCK'" />
            <el-option :label="'UPDATE_ROBOT_SPEED'" :value="'UPDATE_ROBOT_SPEED'" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button text @click="crud.dialogVisible=false">取消</el-button>
          <el-button type="primary" @click="crud.submitCU()">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <el-table ref="tableRef" :data="commands" size="small">
      <el-table-column prop="name" label="指令名" />
      <el-table-column prop="description" label="描述" />
      <el-table-column v-if="checkPer(['admin','result:edit','result:del'])" label="操作" width="68px" align="center">
        <template #header>
          <span>操作</span>
          <el-button :icon="Plus" class="operation" @click="crud.toAdd()" />
        </template>
        <template #default="scope">
          <div class="operation-container">
            <el-button v-if="scope.row.commandType==='PROXY'" :icon="Edit" class="operation" @click="editCommand(scope.row)" />
            <el-button v-if="scope.row.commandType==='PROXY'" :icon="Delete" class="operation" @click="deleteCommand(scope.row)" />
          </div>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted, defineProps, defineEmits } from 'vue'
import { Node } from '@antv/x6'
import { queryNoneProxyCmdForSection } from '@/api/deviceLayout'
import { useCrud } from '@/hooks/useCrud'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'
import LesScriptInput from '@/views/components/LesScriptInput.vue'

const props = defineProps({
  curNode: {
    type: Node,
    default: () => ({})
  }
})

const emit = defineEmits(['update-device-instance'])


// 响应式状态
const commands = ref([])
const candidateCommands = ref([])
const formRef = ref(null)
const tableRef = ref(null)
const deviceInstance = ref(null)
const dialogVisible = ref(false)

// 计算属性
const getCurDeviceInstance = () => {
  const { graph } = props.curNode.model || {}
  return graph?.dataStore?.deviceInstanceList?.find(ins => ins.layoutNodeId === props.curNode.id)
}


// 使用CRUD Hook
const { CRUD, crud, form, rules } = useCrud({
  title: '设备命令',
  formRef,
  tableRef,
  defaultForm: {
    id: null,
    name: '',
    description: '',
    proxyInstanceCmd: { id: '' },
    parameters: [],
    commandType: 'PROXY',
    type: 'NORMAL'
  },
  rules: {
    name: [
      { required: true, message: '步骤名称不能为空', trigger: 'blur' }
    ],
    'proxyInstanceCmd.Id': [
      { required: true, message: '代理设备指令不能为空', trigger: 'blur' }
    ]
  },
  crudMethod: {
    add: ()=>{},
    edit: ()=>{},
  }
})

// 方法
const transformNodeToFormData = () => {
  deviceInstance.value = getCurDeviceInstance()
  if (!deviceInstance.value) return
  commands.value = deviceInstance.value.commands || []
}

const transformFormDataToNode = () => {
  if (!deviceInstance.value) return
  deviceInstance.value.commands = commands.value
  props.curNode.setData({ ...props.curNode.getData(), changeState: 'changed from property' })
  emit('update-device-instance', deviceInstance.value)
}

const openCommandEditDialog = async () => {
  try {
    const res = await queryNoneProxyCmdForSection()
    candidateCommands.value = res.sort((a, b) => {
      return a.deviceInstanceName.localeCompare(b.deviceInstanceName)
    })
 //   crud.form.parameters = []
  } catch (error) {
    console.error('获取候选命令失败:', error)
  }
}

const submitCommandEdit = () => {
  debugger
  // 命令参数显示控件转换为数值
  if (crud.form.proxyInstanceCmd.command.parameterTemplate) {
    const configTemplate = JSON.parse(crud.form.proxyInstanceCmd.command.parameterTemplate)
    if (configTemplate.children) {
      const instConfigObj = {}
      crud.form.parameters.forEach(item => {
        instConfigObj[item.key] = item.value
      })
      crud.form.parameter = JSON.stringify(instConfigObj)
    } else {
      crud.form.parameter = crud.form.parameters[0].value
    }
  }
  if (crud.form.id) {
    const index = commands.value.findIndex(cmd => cmd.id === crud.form.id)
    if (index !== -1) commands.value.splice(index, 1, {...crud.form})
  } else {
    commands.value.push({ ...crud.form })
  }
  crud.dialogVisible = false
  transformFormDataToNode()
}

const addCommand = () => {
  crud.resetForm()
  crud.dialogVisible = true
}

const editCommand = (cmd) => {
  debugger
  cmd.parameters = []
  crud.resetForm(cmd)
  trannsformParameterToDisplay()
  crud.dialogVisible = true
}

const onProxyCmdChange = (id) => {
  let selectedItem = null
  for (const item of candidateCommands.value) {
    if (item.id === id) {
      selectedItem = item
      break
    }
  }
  if (selectedItem) {
    crud.form.proxyInstanceCmd = selectedItem
    crud.form.name = selectedItem.name
    crud.form.description = selectedItem.description

    // 命令参数转换为显示控件
    trannsformParameterToDisplay()
  }
}

const trannsformParameterToDisplay = () => {
  const selectedItem = crud.form.proxyInstanceCmd
  if (!selectedItem) {
    return
  }
  // 命令参数转换为显示控件
  if (selectedItem.command.parameterTemplate) {
    const configTemplate = JSON.parse(selectedItem.command.parameterTemplate)
    let instConfig = crud.form.parameter
    if (instConfig) {
      if (configTemplate.children) {
        try {
          instConfig = JSON.parse(instConfig)
        } catch (error) {
          console.log('error: ' + error)
          instConfig = null
        }
      }
    }
    crud.form.parameters.splice(0, crud.form.parameters.length)
    if (configTemplate.children) {
      configTemplate.children.forEach(item => {
        crud.form.parameters.push({
          key: item.key,
          name: item.name,
          value: (!instConfig) ? '' : instConfig[item.key]
        })
      })
    } else {
      crud.form.parameters.push({
        name: configTemplate.name,
        value: instConfig
      })
    }
  }
}

const deleteCommand = (cmd) => {
  // 实现删除逻辑
  crud.doDelete(cmd)
}

crud.hooks[CRUD.HOOK.beforeSubmit] = () => {
  submitCommandEdit()
  return true
}
// 无需刷新列表清单
crud.hooks[CRUD.HOOK.beforeRefresh] = () => {
  return false
}

// 生命周期
onMounted(transformNodeToFormData)

// 监听当前节点变化
watch(() => props.curNode, transformNodeToFormData)

// 监听表单变化
watch(() => crud.form, transformFormDataToNode, { deep: true })
</script>

<style lang="scss" scoped>
.property-container {
  width: 100%;
}

.property-container label {
  font-weight: 400;
}

.operation {
  width: 18px;
  height: 18px;
  padding: 2px 2px;
  margin-left: 4px;
  margin-bottom: 4px;
}
.operation-container {
  display: flex;
}
</style>
