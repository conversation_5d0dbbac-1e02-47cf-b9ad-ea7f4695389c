import request from '@/utils/request'

export function getAreaInfos(params) {
  return request({
    url: 'api/areaInfo',
    method: 'get',
    params
  })
}
export function add(data) {
  return request({
    url: 'api/areaInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/areaInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/areaInfo',
    method: 'put',
    data
  })
}

export function queryAreaListByUserId(params) {
  return request({
    url: 'api/areaInfo/queryAreaListByUserId',
    method: 'get',
    params
  })
}
export default { add, edit, del, getAreaInfos, queryAreaListByUserId }
