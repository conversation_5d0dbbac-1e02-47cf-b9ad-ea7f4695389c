<template>
  <div>
    <div v-if="query.dictName === ''">
      <div class="my-code">点击字典查看详情</div>
    </div>
    <div v-else>
      <!--工具栏-->
      <div class="head-container">
        <div v-if="crud.props.searchToggle">
          <!-- 搜索 -->
          <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
            <el-form-item label="" prop="blurry">
              <el-input
                v-model="query.label"
                clearable
                size="default"
                placeholder="输入字典标签查询"
                style="width: 200px;"
                @keyup.enter="crud.toQuery"
              />
            </el-form-item>
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!--表单组件-->
      <el-dialog
        v-model="dialogVisible"
        :title="crud.status.title"
        width="500px"
        align-center
        append-to-body
        :close-on-click-modal="false"
        @close="crud.cancelCU"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          size="default"
          label-width="80px"
        >
          <el-form-item label="字典标签" prop="label">
            <el-input v-model="form.label" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="字典值" prop="value">
            <el-input v-model="form.value" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="排序" prop="dictSort">
            <el-input-number
              v-model="form.dictSort"
              :min="0"
              :max="999"
              controls-position="right"
              style="width: 370px;"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </template>
      </el-dialog>
      <!--表格渲染-->
      <el-table
        ref="tableRef"
        v-loading="crud.loading"
        :data="crud.data"
        highlight-current-row
        style="width: 100%"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column label="所属字典">
          {{ query.dictName }}
        </el-table-column>
        <el-table-column prop="label" label="字典标签" />
        <el-table-column prop="value" label="字典值" />
        <el-table-column prop="dictSort" label="排序" />
        <el-table-column
          v-if="checkPer(['admin','dict:edit','dict:del'])"
          label="操作"
          width="130px"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <udOperation
              :crud="crud"
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination :crud="crud" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, inject } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import crudDictDetail from '@/api/system/dictDetail'
import pagination from '@/components/Crud/Pagination.vue'
import rrOperation from '@/components/Crud/RR.operation.vue'
import udOperation from '@/components/Crud/UD.operation.vue'

// 注入权限检查函数
const checkPer = inject('checkPer')
// 定义 dictId
const dictId = ref(null)
// refs
const tableRef = ref(null)
const formRef = ref(null)

// 使用 crud hook
const { crud, query, form, rules } = useCrud({
  title: '字典详情',
  url: 'api/dictDetail',
  query: { dictName: '' },
  sort: ['dictSort,asc', 'id,desc'],
  crudMethod: { ...crudDictDetail },
  formRef,
  tableRef,
  defaultForm: {
    id: null,
    label: null,
    value: null,
    dictSort: 999,
    dict: { id: dictId }
  },
  rules: {
    label: [
      { required: true, message: '请输入字典标签', trigger: 'blur' }
    ],
    value: [
      { required: true, message: '请输入字典值', trigger: 'blur' }
    ],
    dictSort: [
      { required: true, message: '请输入序号', trigger: 'blur', type: 'number' }
    ]
  }
})

// 计算属性
const dialogVisible = computed(() => crud.status.cu > 0)

// 权限配置
const permission = {
  add: ['admin', 'dict:add'],
  edit: ['admin', 'dict:edit'],
  del: ['admin', 'dict:del']
}

// 暴露属性给父组件
defineExpose({
  query,
  crud,
  dictId
})
</script>

<style lang="scss" scoped>
.my-code {
  margin: 20px;
  text-align: center;
  color: #909399;
}
</style>
