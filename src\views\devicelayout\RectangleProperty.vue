<template>
  <div class="property-container">
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-divider content-position="left">基本信息</el-divider>
      <el-form-item label="背景色" prop="backgroundColor">
        <el-color-picker
          v-model="form.backgroundColor"
          :predefine="colorPresets"
          show-alpha
          color-format	="hex"
          popper-class="theme-picker-dropdown"
        />
      </el-form-item>
      <el-form-item label="线条颜色" prop="strokeColor">
        <el-color-picker
          v-model="form.strokeColor"
          :predefine="colorPresets"
          show-alpha
          color-format	="hex"
          popper-class="theme-picker-dropdown"
        />
      </el-form-item>
      <el-form-item label="线条宽度" prop="strokeWidth">
        <el-input-number
          v-model="form.strokeWidth"
          :min="1"
          :max="10"
          size="small"
          controls-position="right"
        />
      </el-form-item>
      <el-form-item label="阴影" prop="filterShadow">
        <el-switch v-model="form.filterShadowEnable" clearable size="small" placeholder="请选择" >
          </el-switch>
          <div style="width:100%">
            <div><label>dx</label><el-slider v-model="form.filterShadow.dx" :min="0" :max="20" label="dx" /></div>
            <div><label>dy</label><el-slider v-model="form.filterShadow.dy" :min="0" :max="20" /></div>
            <div><label>blur</label><el-slider v-model="form.filterShadow.blur" :min="0" :max="20" /></div>
            <div><label>opacity</label><el-slider v-model="form.filterShadow.opacity" :min="0" :max="1" step="0.01" /></div>
          </div>
      </el-form-item>

      
      <el-form-item label="zIndex" prop="zIndex">
        <el-input v-model="form.zIndex" />
      </el-form-item>
      
      <el-form-item label="锁定" prop="locked">
        <el-switch v-model="form.locked" clearable size="small" placeholder="请选择" >
          </el-switch>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { Graph, Node } from '@antv/x6'
import { debounce } from '@/utils'

// Props
const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  },
  graph: {
    default: () => ({}),
    type: Graph
  },
  dataStore: {
    default: () => ({}),
    type: Object
  }
})

// Emits
const emit = defineEmits(["update:modelValue"]);

// 颜色预设
const colorPresets = [
  "#4080FF",
  "#ff4500",
  "#ff8c00",
  "#90ee90",
  "#00ced1",
  "#1e90ff",
  "#c71585",
  "rgba(255, 69, 0, 0.68)",
  "rgb(255, 120, 0)",
  "hsva(120, 40, 94)",
];

// 响应式数据
const defaultForm = { id: null, backgroundColor: null,strokeColor: null,strokeWidth: 1, 
  filterShadowEnable: false, filterShadow: {dx:0,dy:0,blur:5,opacity:0.5}, zIndex: 0, locked: false,
  description: null, positions: [], type: null, configTemplate: null, javaClassName: null, configJavaClassName: null, layoutImage: null, layoutWidth: null, layoutHeight: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }
const form = reactive({ ...defaultForm })
const rules = reactive({
  name: [
    { required: true, message: '设备实例名称', trigger: 'blur' }
  ]
})
const currentSelectedPosIndex = ref(-1)
const initing = ref(false)
const formRef = ref(null)
const tableRef = ref(null)

const changeThemeColor = (val) => {
  //form.backgroundColor = val
  emit('update:modelValue', val)
}

// 方法
const getCurDeviceInstance = () => {
  const curNode = props.curNode
  const { graph } = curNode.model || {}

  if (!graph) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === curNode.id) {
      return ins
    }
  }
  return null
}

const transformNodeToFormData = () => {

  const curNode = props.curNode
  if (!curNode) {
    return
  }

  form.backgroundColor = curNode.attr('body/fill');
  form.strokeColor =  curNode.attr('body/stroke' );
  form.strokeWidth =  curNode.attr('body/strokeWidth');
  debugger
  if (curNode.attr('body/filter')) {
    form.filterShadowEnable = true;
    form.filterShadow.dx = curNode.attr('body/filter').args.dx;
    form.filterShadow.dy = curNode.attr('body/filter').args.dy;
    form.filterShadow.blur = curNode.attr('body/filter').args.blur;
    form.filterShadow.opacity = curNode.attr('body/filter').args.opacity;
  }
  form.zIndex = curNode.prop('zIndex');
  form.locked = curNode.getData()?.locked;
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  if (!curNode) {
    return
  }

  curNode.attr('body/fill', form.backgroundColor);
  curNode.attr('body/stroke', form.strokeColor);
  curNode.attr('body/strokeWidth', form.strokeWidth);
  
}

// 创建防抖的更新函数
const debouncedTransformFormDataToNode = debounce(() => {
  transformFormDataToNode()
}, 300)

// 监听器
watch(
  () => form,
  (newVal) => {
    const curNode = props.curNode
    if (!curNode) {
      return
    }
    
    curNode.attr('body/fill', form.backgroundColor);
    curNode.attr('body/stroke', form.strokeColor);
    curNode.attr('body/strokeWidth', form.strokeWidth);
    debugger
    if (form.filterShadowEnable) {
      curNode.attr('body/filter', {
        name: 'dropShadow',
        args: {
          dx: form.filterShadow.dx,
          dy: form.filterShadow.dy,
          blur: form.filterShadow.blur,
          opacity: form.filterShadow.opacity,
        },
      });
    }else{
      curNode.attr('body/filter', null);
    }

    curNode.prop('zIndex', form.zIndex);
    curNode.setData({'locked': form.locked})
    
    // 直接设置节点的movable属性
    curNode.prop('movable', !form.locked)
  },
  {deep:true}
)

// 生命周期
onMounted(() => {
  console.log('LayoutProperty created..')
  transformNodeToFormData()
})
</script>

<style lang="scss">
.property-container {
  width: 100%;
}
.circle-button {
  width: 40px;
  height: 40px;
}

.el-table .position-table-cell .cell {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell input {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell .input-pos input {
  width:56px;
}
.el-table .position-table-cell .cell .input-name {
  padding-right: 4px;
}

</style>
