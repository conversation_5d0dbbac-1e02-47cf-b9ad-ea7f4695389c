import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/task',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/task/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/task',
    method: 'put',
    data
  })
}

export function queryLatestTasks(params) {
  return request({
    url: 'api/task/queryLatestTasks',
    method: 'get',
    params
  })
}

export function queryTask(params) {
  return request({
    url: 'api/task',
    method: 'get',
    params
  })
}

export function queryForSelection(params) {
  return request({
    url: 'api/task/queryForSelection',
    method: 'get',
    params
  })
}

export function querySchedulerGanttData(params) {
  return request({
    url: 'api/task/querySchedulerGanttData',
    method: 'get',
    params
  })
}

export function querySchedulerGanttDataOnline(params) {
  return request({
    url: 'api/task/querySchedulerGanttDataOnline',
    method: 'get',
    params
  })
}



export function querySchedulerOnlineData(params) {
  return request({
    url: 'api/task/querySchedulerOnlineData',
    method: 'get',
    params
  })
}

export function querySchedulerGanttDataDeamon(params) {
  return request({
    url: 'api/task/querySchedulerGanttDataDeamon',
    method: 'get',
    params
  })
}

export function setFavoriteScheduler(data) {
  return request({
    url: 'api/task/setFavoriteScheduler',
    method: 'post',
    data
  })
}

export function getStatistics(params) {
  return request({
    url: 'api/task/getStatistics',
    method: 'get',
    params
  })
}

export function getRunningStatistics(params) {
  return request({
    url: 'api/task/getRunningStatistics',
    method: 'get',
    params
  })
}


export function getCurrentTaskGlobalInfo(params) {
  return request({
    url: 'api/task/getCurrentTaskGlobalInfo',
    method: 'get',
    params
  })
}

export function setStarted(data) {
  return request({
    url: 'api/task/setStarted',
    method: 'put',
    data
  })
}

export default { add, edit, del, queryLatestTasks, queryTask, queryForSelection, querySchedulerGanttData, querySchedulerGanttDataOnline, querySchedulerOnlineData,
  querySchedulerGanttDataDeamon, setFavoriteScheduler, getStatistics, getRunningStatistics,
  getCurrentTaskGlobalInfo, setStarted }
