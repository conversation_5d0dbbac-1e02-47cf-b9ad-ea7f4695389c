import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/baseCascategoryProp',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/baseCascategoryProp/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/baseCascategoryProp',
    method: 'put',
    data
  })
}

export function getAll(params) {
  return request({
    url: 'api/baseCascategoryProp/all',
    method: 'get',
    params
  })
}

export function get(params) {
  return request({
    url: 'api/baseCascategoryProp',
    method: 'get',
    params
  })
}

export default { add, edit, del, getAll, get }
