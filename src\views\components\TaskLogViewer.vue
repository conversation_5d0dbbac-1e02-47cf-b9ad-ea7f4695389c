<template>
  <el-scrollbar ref="logScrollBar" class="log-scrollbar" wrap-style="overflow-x:hidden;" :bar-style="{ backgroundColor: '#409EFF', width: '8px' }">
    <div v-for="(log, index) in logs" :key="index" class="log-entry">
      <span class="log-task-tag" :style="getTaskTagStyle(log.taskId)" />
      <span class="log-timestamp" :style="getTextStyle(log)">{{ getTruncatedTime(log.updateTime) }}</span>
      <span class="log-message" :style="getTextStyle(log)">{{ log.name + (log.message ? (': ' + log.message) : '') }}</span>
      <span class="log-status" :style="getTextStyle(log)">{{ log.status }}</span>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import moment from 'moment'
import crudTaskExecuteLog from '@/api/taskExecuteLog'

import useWebSocket from '@/hooks/useWebSocket'

// Props 定义
const props = defineProps({
  // nodeType: TASK/METHOD/STEP/ACTION/COMMAND
  // id: 对应节点的主键ID
  dataFilter: {
    default: () => {
      return { nodeType: 'NONE', id: 0 }
    },
    type: Object
  }
})

// 响应式状态
const logs = ref([{ timestamp: '', level: 'INFO', message: 'Starting...' }])
const pagable = ref({})
const loading = ref(false)
const ws = ref(null)
const websocketState = ref('')
const logScrollBar = ref(null)

// 任务颜色定义
const taskColorDefs = ['tomato', 'teal', 'crimson', 'yellowgreen', 'blueviolet', 'forestgreen', 'steelblue', 'sandybrown', 'seagreen', 'slateblue']

// 生成唯一ID
function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 加载更多日志
function loadMoreLogs() {
  if (loading.value) return
  loading.value = true
}

// 获取截断的时间
function getTruncatedTime(tm) {
  if (!tm) {
    return ''
  }
  return tm.substring(5, tm.length)
}

// 获取文本样式
function getTextStyle(log) {
  const status = log.status
  const logLevel = log.logLevel
  if (logLevel === 'DEBUG') {
    return 'color: #75777a'
  } else if (logLevel === 'INFO' && status === 'SUCCESS') {
    return 'color: #67c23a'
  } else if (logLevel === 'WARN') {
    return 'color: #e6a23c'
  } else if (logLevel === 'ERROR' || status === 'FAILED') {
    return 'color: #f56c6c'
  } else {
    return 'color: #303133'
  }
}

// 获取任务标签样式
function getTaskTagStyle(taskId) {
  return 'background-color:' + taskColorDefs[(Number(taskId) % taskColorDefs.length)]
}


// 任务返回响应
function onTaskResponse(data) {
  const dto = JSON.parse(data.msg)
  if (!checkDataFilter(dto)) {
    return
  }
  logs.value.push({ ...dto, updateTime: moment(dto.updateTime).format('YYYY-MM-DD HH:mm:ss') })
  scrollDown()
}

// 滚动到底部
function scrollDown() {
  nextTick(() => {
    
    if (logScrollBar.value && logScrollBar.value.wrapRef) {
      
      setTimeout(() => {
        // 获取滚动区域的总高度
        const scrollHeight = logScrollBar.value.wrapRef?.scrollHeight || 0;
        // 设置滚动位置到底部
        logScrollBar.value.setScrollTop(scrollHeight);
      }, 10)
      //logScrollBar.value.wrap.scrollTop = logScrollBar.value.wrap.scrollHeight
    }
  })
}

// 检查数据过滤
function checkDataFilter(dto) {
  if (!dto) {
    return false
  }
  if (!props.dataFilter || props.dataFilter.nodeType === 'NONE') {
    return true
  }

  if (props.dataFilter.nodeType === 'TASK') {
    return props.dataFilter.id === dto.taskId
  } else if (props.dataFilter.nodeType === 'METHOD') {
    return props.dataFilter.id === dto.taskMethodId
  } else if (props.dataFilter.nodeType === 'STEP') {
    return props.dataFilter.id === dto.taskStepId
  } else if (props.dataFilter.nodeType === 'ACTION') {
    return props.dataFilter.id === dto.taskActionId
  } else if (props.dataFilter.nodeType === 'COMMAND') {
    return props.dataFilter.id === dto.taskCommandId
  }
  return false
}

// 初始化数据
function initData() {
  
  if (!props.dataFilter || props.dataFilter.nodeType === 'NONE') {
    const queryParam = { size: 100, sort: 'id,asc' }
    crudTaskExecuteLog.queryLatestTaskExecuteLog(queryParam).then(res => {
      logs.value = res.content
      scrollDown()
    })
  } else if (props.dataFilter && props.dataFilter.nodeType !== 'NONE') {
    crudTaskExecuteLog.queryTaskExecuteLogForNode(props.dataFilter).then(res => {
      logs.value = res
      scrollDown()
    })
  }
}

// WebSocket 相关方法
const {} = useWebSocket({
  url: ( id ) => `${import.meta.env.VITE_APP_WS_API}/webSocket/taskMonitorLog_${id}`,
  onMessage: ( message ) => {
    onTaskResponse(message)
  }
})

// 监听 dataFilter 变化
watch(() => props.dataFilter, (newVal, oldVal) => {
  initData()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  initData()
})

</script>

<style scoped>
.log-scrollbar {
  flex: 1;
  height: 100%;
}

.log-entry:last-child {
  font-weight: bold;
}

.log-timestamp {
  color: #535559;
  margin-right: 10px;
}

.log-message {
  color: #303133;
  flex: 1;
  word-break: break-word;
}

.log-entry:hover {
  background-color: #f5f7fa;
}

.log-entry {
  flex: 1;
  padding: 5px 0;
  border-bottom: 1px solid #eaeaea;
  white-space: pre-wrap;
  transition: background-color 0.3s;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.log-task-tag {
  width: 8px;
  height: 18px;
  display: inline-block;
  background-color: chocolate;
  border-radius: 4px;
  margin-right: 4px;
}

.log-status {
  width: 80px;
  overflow: hidden;
  padding-left: 4px;
}
</style>
