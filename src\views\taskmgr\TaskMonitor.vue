<template>
  <div class="app-container">
    <div class="task-header-container">
      <span>自动刷新</span>
      <el-switch
        v-model="isAutoRefresh"
        active-color="#13ce66"
        inactive-color="#999999"
        @change="autoRefresh"
      />
    </div>
    
    <div class="task-outter-container">
      <!-- 队列标题 -->
      <div 
        v-for="(title, index) in queueTitles" 
        :key="index" 
        class="item-header-container"
      >
        <span>{{ title }}</span>
      </div>
    </div>

    <div class="task-outter-container">
      <div class="item-container">
        <div v-for="item in onlineData.tasks" :key="item.id" class="item" :style="getTaskTagStyle(item.id)">
          <el-tooltip class="item" effect="dark" :content="'ID:'+item.id+', taskNumber:'+item.taskNumber " placement="top">
            <span style="display: inline-block;">{{ item.taskName + '['+item.status+']' }}</span>
          </el-tooltip>
        </div>
      </div>
      <div class="item-container">
        <div v-for="item in onlineData.taskMethods" :key="item.id" class="item" :style="getTaskTagStyle(item.taskId)">
          <el-tooltip class="item" effect="dark" :content="'ID:'+item.id+', Name:'+item.name + ', Status:' + item.status " placement="top">
            <span style="display: inline-block;">{{ item.name+ '['+item.status+']' }}</span>
          </el-tooltip>
        </div>
      </div>
      <div class="item-container">
        <div v-for="item in onlineData.taskSteps" :key="item.id" class="item" :style="getTaskTagStyle(item.taskId)">
          <el-tooltip class="item" effect="dark" :content="'ID:'+item.id+', Name:'+item.name + ', Status:' + item.status + ', Message:'+item.message" placement="top">
            <span style="display: inline-block;">{{ item.name+ '['+item.status+']' }}</span>
          </el-tooltip>
        </div>
      </div>
      <div class="item-container">
        <div v-for="item in onlineData.taskActions" :key="item.id" class="item" :style="getTaskTagStyle(item.taskId)">
          <el-tooltip class="item" effect="dark" :content="'ID:'+item.id+', Name:'+item.name + ', Status:' + item.status" placement="top">
            <span style="display: inline-block;">{{ item.name+ '['+item.status+']' }}</span>
          </el-tooltip>
        </div>
      </div>
      <div class="item-container">
        <div v-for="item in onlineData.taskCommands" :key="item.id" class="item" :style="getTaskTagStyle(item.taskId)">
          <el-tooltip class="item" effect="dark" :content="'ID:'+item.id+', Name:'+item.name + ', Status:' + item.status" placement="top">
            <span style="display: inline-block;">{{ item.name+ '['+item.status+']' }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import crudTask from '@/api/task'
import { ElMessage } from 'element-plus'

// 响应式状态
const isAutoRefresh = ref(true)
const onlineData = reactive({
  tasks: [],
  taskMethods: [],
  taskSteps: [],
  taskActions: [],
  taskCommands: []
})

// 常量定义
const taskColorDefs = [
  'tomato', 'teal', 'crimson', 'yellowgreen', 
  'blueviolet', 'forestgreen', 'steelblue', 
  'sandybrown', 'seagreen', 'slateblue'
]

// 队列配置
const queueTitles = ['任务队列', '方法队列', '步骤队列', '动作队列', '指令队列']
const queues = reactive([
  { data: onlineData.tasks, idKey: 'id' },
  { data: onlineData.taskMethods, idKey: 'taskId' },
  { data: onlineData.taskSteps, idKey: 'taskId' },
  { data: onlineData.taskActions, idKey: 'taskId' },
  { data: onlineData.taskCommands, idKey: 'taskId' }
])

// 工具方法
const tooltipContent = (item) => {
  const base = `ID:${item.id}`
  return item.taskNumber ? `${base}, taskNumber:${item.taskNumber}` : base
}

const displayText = (item) => {
  return `${item.taskName || item.name}[${item.status}]`
}

const getTaskTagStyle = (taskId) => {
  return {
    borderLeftStyle: 'solid',
    borderLeftWidth: '4px',
    borderTopLeftRadius: '4px',
    borderBottomLeftRadius: '4px',
    borderLeftColor: taskColorDefs[Number(taskId) % taskColorDefs.length]
  }
}

// 核心逻辑
let refreshTimer = null

const querySchedulerOnlineData = async () => {
  try {
    const res = await crudTask.querySchedulerOnlineData()
    Object.assign(onlineData, res)
    
    if (isAutoRefresh.value) {
      refreshTimer = setTimeout(querySchedulerOnlineData, 1000)
    }
  } catch (error) {
    ElMessage.error('数据获取失败: ' + error.message)
  }
}

const autoRefresh = (val) => {
  if (val) querySchedulerOnlineData()
  else clearTimeout(refreshTimer)
}

// 生命周期
onMounted(querySchedulerOnlineData)
onBeforeUnmount(() => {
  clearTimeout(refreshTimer)
  onlineData.tasks = []
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.task-header-container {
  padding: 4px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-outter-container {
  display: flex;
  gap: 2px;
  margin-bottom: 10px;

  .item-header-container {
    background-color: #bbb;
    padding: 2px 8px;
    width: 240px;
    border: 1px solid white;
    
    span {
      font-weight: bold;
    }
  }

  .item-container {
    padding: 2px 8px;
    width: 240px;
    
    .item {
      display: block;
      padding: 4px;
      margin: 2px 4px;
      background-color: #9bc2e7;
      border-radius: 4px;
      font-size: small;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
