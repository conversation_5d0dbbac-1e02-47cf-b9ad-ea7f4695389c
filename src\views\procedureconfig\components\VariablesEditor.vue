<template>
  <div class="variables-editor">
    <!-- 变量列表 -->
    <div v-if="variablesArr.length > 0" class="variables-list">
      <el-form-item 
        v-for="item in variablesArr" 
        :key="item.key" 
        :title="getVariableTitle(item)"
        prop="variablesArr" 
        class="variable-item" 
        :label-width="labelWidth"
      >
      <template #label>
        <span class="name">{{ `${item.name || item.key}` }}</span>
        <span class="key">{{ `${item.key}` }}</span>
      </template>
        <el-input 
          v-model="item.value" 
          :placeholder="item.defaultValue || '请输入值'" 
          clearable
          @input="handleVariableChange"
        />
        <div v-if="item.description" class="variable-description">
          {{ item.description }}
        </div>
      </el-form-item>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="暂无可配置的变量" :image-size="40" />
    </div>
    
    <!-- 调试信息 -->
    <div v-if="showDebugInfo" class="debug-info">
      <el-divider content-position="left">调试信息</el-divider>
      <pre>{{ JSON.stringify(variablesArr, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { queryActionVarDefinesByActionId,queryStepVarDefinesByStepId,queryMethodVarDefinesByMethodId } from '@/api/procedure'

// Props 定义
const props = defineProps({
  modelValue: {
    type: [String, Object],
    default: null
  },
  // 用于获取变量定义的参数
  actionId: {
    type: [String, Number],
    default: null
  },
  stepId: {
    type: [String, Number],
    default: null
  },
  methodId: {
    type: [String, Number],
    default: null
  },
  // 自定义变量数组（如果不通过API获取）
  variables: {
    type: Array,
    default: () => []
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: '30%'
  },
  // 是否显示调试信息
  showDebugInfo: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const variablesArr = ref([])
const loading = ref(false)

// 计算属性
const getVariableLabel = computed(() => (item) => {
  return `${item.name || item.key} ${item.key}`
})

const getVariableTitle = computed(() => (item) => {
  const title = `${item.name || item.key} ${item.key}`
  return item.description ? `${title} - ${item.description}` : title
})

// 方法定义
const loadVariableDefines = async () => {
  if (!props.actionId && !props.stepId && !props.methodId) {
    console.warn('VariablesEditor: actionId|stepId|methodId is required to load variable defines')
    return
  }
  
  loading.value = true
  try {
    if (props.actionId){
      const res = await queryActionVarDefinesByActionId({ actionId: props.actionId })
      variablesArr.value = res || []
    }else if (props.stepId){
      const res = await queryStepVarDefinesByStepId({ stepId: props.stepId })
      variablesArr.value = res || []
    }else if (props.methodId){
      const res = await queryMethodVarDefinesByMethodId({ methodId: props.methodId })
      variablesArr.value = res || []
    }
    // 如果有初始值，合并到变量数组中
    if (props.modelValue) {
      mergeInitialValues()
    }
  } catch (error) {
    console.error('加载变量定义失败:', error)
    variablesArr.value = []
  } finally {
    loading.value = false
  }
}

const mergeInitialValues = () => {
  if (!props.modelValue) return
  
  let varObj = {}
  
  // 处理不同类型的modelValue
  if (typeof props.modelValue === 'string') {
    try {
      varObj = JSON.parse(props.modelValue)
    } catch (error) {
      console.error('解析变量值失败:', error)
      return
    }
  } else if (typeof props.modelValue === 'object') {
    varObj = props.modelValue
  }
  
  // 将值合并到变量数组中
  for (const [key, value] of Object.entries(varObj)) {
    const idx = variablesArr.value.findIndex(item => item.key === key)
    if (idx !== -1) {
      variablesArr.value[idx] = { ...variablesArr.value[idx], value }
    }
  }
}

const handleVariableChange = () => {
  // 将变量数组转换为变量对象
  const variables = {}
  variablesArr.value.forEach(item => {
    if (item.value !== undefined && item.value !== null && item.value !== '') {
      variables[item.key] = item.value
    }
  })
  
  // 发出更新事件
  const result = Object.keys(variables).length > 0 ? JSON.stringify(variables) : null
  emit('update:modelValue', result)
  emit('change', result, variables)
}

// 初始化变量数组（使用自定义变量或空数组）
const initializeVariables = () => {
  if (props.variables && props.variables.length > 0) {
    variablesArr.value = [...props.variables]
    if (props.modelValue) {
      mergeInitialValues()
    }
  }
}

// 监听器
watch(() => props.modelValue, () => {
  mergeInitialValues()
}, { deep: true })

watch(() => [props.actionId,props.stepId,props.methodId], ([newActionId,newStepId,newMethodId]) => {
  debugger
  if (newActionId|newStepId|newMethodId) {
    loadVariableDefines()
  } else {
   // variablesArr.value = []
  }
})

watch(() => props.variables, () => {
  initializeVariables()
}, { deep: true, immediate: true })

// 生命周期
onMounted(() => {
  if (props.actionId||props.stepId||props.methodId) {
    loadVariableDefines()
  } else {
    initializeVariables()
  }
})

// 暴露方法给父组件
defineExpose({
  loadVariableDefines,
  variablesArr
})
</script>

<style lang="scss" scoped>

.variables-editor {
  .variables-list {
    .variable-item {
      margin-bottom: 16px;
      
      :deep(.el-form-item__label) {
        line-height: 13px;
        text-align: right;
        text-overflow: ellipsis;
        overflow: hidden;
       // white-space: nowrap;
       .name {

       }
       .key {
        
       }
      }
      
      .variable-description {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
        line-height: 1.4;
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 0px 0px;
    color: #909399;
  }
  
  .debug-info {
    margin-top: 20px;
    
    pre {
      background: #f5f7fa;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}
</style>
