<template>
  <div class="app-container">

    
    <!-- 表单渲染 -->
    <el-dialog
      v-model="dialogVisible"
      :title="crud.status.title"
      width="520px"
      align-center
      append-to-body
      :close-on-click-modal="false"
      @close="crud.cancelCU"
    >
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        size="default"
        label-width="80px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" style="width: 380px;" />
        </el-form-item>
        <el-form-item label="角色级别" prop="level">
          <el-input-number
            v-model="form.level"
            :min="1"
            controls-position="right"
            style="width: 145px;"
          />
        </el-form-item>
        <el-form-item label="数据范围" prop="dataScope">
          <el-select
            v-model="form.dataScope"
            style="width: 140px"
            placeholder="请选择数据范围"
            @change="changeScope"
          >
            <el-option
              v-for="item in dateScopes"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.dataScope === '自定义'"
          label="数据权限"
          prop="depts"
        >
          <el-tree-select
            v-model="deptDatas"
            :data="depts"
            :props="{ label: 'name', value: 'id' }"
            multiple
            style="width: 380px"
            placeholder="请选择"
            check-strictly
            :load="loadDepts"
            lazy
          />
        </el-form-item>
        <el-form-item label="描述信息" prop="description">
          <el-input
            v-model="form.description"
            style="width: 380px;"
            rows="5"
            type="textarea"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </template>
    </el-dialog>

    <el-row :gutter="15">
      <!--角色管理-->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="17" style="margin-bottom: 10px">
        <el-card class="box-card" >
          <template #header>
            <span class="role-span">角色列表</span>
          </template>
              <!--工具栏-->
          <div class="head-container">
            <div v-if="crud.props.searchToggle">
              <!-- 搜索 -->
              <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
                <el-form-item label="" prop="blurry">
                  <el-input
                    v-model="query.blurry"
                    clearable
                    size="default"
                    placeholder="输入名称或者描述搜索"
                    style="width: 200px;"
                    @keyup.enter="crud.toQuery"
                  />
                </el-form-item>
                <el-form-item label="" >
                  <date-range-picker v-model="query.createTime" />
                </el-form-item>
                <el-form-item>
                  <rrOperation />
                </el-form-item>
              </el-form>
            </div>
          </div>

          <crudOperation :permission="permission" />

          <el-table
            ref="tableRef"
            v-loading="crud.loading"
            :data="crud.data"
            highlight-current-row
            style="width: 100%"
            @selection-change="crud.selectionChangeHandler"
            @current-change="handleCurrentChange"
          >
            <el-table-column :selectable="checkboxT" type="selection" width="55" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="dataScope" label="数据权限" />
            <el-table-column prop="level" label="角色级别" />
            <el-table-column :show-overflow-tooltip="true" prop="description" label="描述" />
            <el-table-column
              :show-overflow-tooltip="true"
              width="135px"
              prop="createTime"
              label="创建日期"
            />
            <el-table-column
              v-if="checkPer(['admin','roles:edit','roles:del'])"
              label="操作"
              width="130px"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <udOperation
                  v-if="scope.row.level >= level"
                  :data="scope.row"
                  :permission="permission"
                />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-card>
      </el-col>

      <!-- 菜单授权 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="7">
        <el-card class="box-card" >
          <template #header>
            <div class="clearfix">
              <el-tooltip
                class="item"
                effect="dark"
                content="选择指定角色分配菜单"
                placement="top"
              >
                <span class="role-span">菜单分配</span>
              </el-tooltip>
              <el-button
                v-permission="['admin','roles:edit']"
                :disabled="!showButton"
                :loading="menuLoading"
                type="primary"
                size="small"
                style="float: right; padding: 6px 9px"
                @click="saveMenu"
              >
                <el-icon><Check /></el-icon>
                保存
              </el-button>
            </div>
          </template>
          <el-tree
            ref="menuRef"
            lazy
            :data="menus"
            :default-checked-keys="menuIds"
            :load="getMenuDatas"
            :props="defaultProps"
            check-strictly
            accordion
            show-checkbox
            node-key="id"
            @check="menuChange"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, provide, inject, onMounted } from 'vue'
import { Check } from '@element-plus/icons-vue'
import { useCrud } from '@/hooks/useCrud'
import crudRoles from '@/api/system/role'
import { getDepts, getDeptSuperior } from '@/api/system/dept'
import { getMenusTree, getChild } from '@/api/system/menu'
import pagination from '@/components/Crud/Pagination.vue'
import crudOperation from '@/components/Crud/crud.operation.vue'
import rrOperation from '@/components/Crud/RR.operation.vue'
import udOperation from '@/components/Crud/UD.operation.vue'
import DateRangePicker from '@/components/DateRangePicker/index.vue'

// 注入权限检查函数
const checkPer = inject('checkPer')

// refs
const tableRef = ref(null)
const formRef = ref(null)
const menuRef = ref(null)

// 数据
const defaultProps = {
  children: 'children',
  label: 'label',
  isLeaf: 'leaf'
}
const dateScopes = ['全部', '本级', '自定义']
const level = ref(3)
const currentId = ref(0)
const menuLoading = ref(false)
const showButton = ref(false)
const menus = ref([])
const menuIds = ref([])
const depts = ref([])
const deptDatas = ref([])

// 使用 crud hook
const { crud,CRUD, query, form, rules } = useCrud({
  title: '角色',
  url: 'api/roles',
  sort: 'level,asc',
  crudMethod: { ...crudRoles },
  defaultForm: {
    id: null,
    name: null,
    depts: [],
    description: null,
    dataScope: '全部',
    level: 3
  },
  rules: {
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' }
    ]
  }
})

// 提供 crud 给子组件使用
provide('crud', crud)

// 计算属性
const dialogVisible = computed(() => crud.status.cu > 0)

// 权限配置
const permission = {
  add: ['admin', 'roles:add'],
  edit: ['admin', 'roles:edit'],
  del: ['admin', 'roles:del']
}

// 方法
const checkboxT = (row) => {
  return row.level >= level.value
}

const handleCurrentChange = (val) => {
  if (val) {
    menuRef.value.setCheckedKeys([])
    currentId.value = val.id
    menuIds.value = []
    val.menus.forEach(function(data) {
      menuIds.value.push(data.id)
    })
    showButton.value = true
  }
}

const getMenuDatas = (node, resolve) => {
  setTimeout(() => {
    getMenusTree(node.data.id ? node.data.id : 0).then(res => {
      resolve(res)
    })
  }, 100)
}

const menuChange = (menu) => {
  getChild(menu.id).then(childIds => {
    if (menuIds.value.includes(menu.id)) {
      childIds.forEach(id => {
        const index = menuIds.value.indexOf(id)
        if (index !== -1) {
          menuIds.value.splice(index, 1)
        }
      })
    } else {
      childIds.forEach(id => {
        if (!menuIds.value.includes(id)) {
          menuIds.value.push(id)
        }
      })
    }
    menuRef.value.setCheckedKeys(menuIds.value)
  })
}

const saveMenu = async () => {
  menuLoading.value = true
  const role = {
    id: currentId.value,
    menus: menuIds.value.map(id => ({ id }))
  }
  try {
    await crudRoles.editMenu(role)
    crud.notify('保存成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
    menuLoading.value = false
    update()
  } catch (err) {
    menuLoading.value = false
    console.error(err.response.data.message)
  }
}

const update = () => {
  crudRoles.get(currentId.value).then(res => {
    crud.data.forEach((item, index) => {
      if (res.id === item.id) {
        crud.data[index] = res
      }
    })
  })
}

const loadDepts = async (node, resolve) => {
  const params = { enabled: true, pid: node.data?.id || null }
  try {
    const res = await getDepts(params)
    const data = res.content.map(item => {
      if (item.hasChildren) {
        item.children = null
      }
      return item
    })
    resolve(data)
  } catch (error) {
    console.error(error)
    resolve([])
  }
}

const changeScope = async () => {
  if (form.dataScope === '自定义') {
    try {
      const res = await getDepts({ enabled: true })
      depts.value = res.content.map(dept => {
        if (dept.hasChildren) {
          dept.children = null
        }
        return dept
      })
    } catch (error) {
      console.error(error)
    }
  }
}

// CRUD 钩子
crud.afterRefresh = () => {
  menuRef.value?.setCheckedKeys([])
}

crud.beforeToAdd = (crud, form) => {
  deptDatas.value = []
  form.menus = null
}

crud.beforeToEdit = (crud, form) => {
  deptDatas.value = []
  if (form.dataScope === '自定义') {
    getSupDepts(form.depts)
  }
  form.depts.forEach(dept => {
    deptDatas.value.push(dept.id)
  })
  form.menus = null
}

crud.afterValidateCU = (crud) => {
  if (crud.form.dataScope === '自定义' && deptDatas.value.length === 0) {
    crud.$message({
      message: '自定义数据权限不能为空',
      type: 'warning'
    })
    return false
  }
  if (crud.form.dataScope === '自定义') {
    crud.form.depts = deptDatas.value.map(id => ({ id }))
  } else {
    crud.form.depts = []
  }
  return true
}

// 生命周期钩子
onMounted(() => {
  crudRoles.getLevel().then(data => {
    level.value = data.level
  })
  crud.refresh()
})
</script>

<style lang="scss" scoped>
.role-span {
  font-weight: bold;
  color: #303133;
  font-size: 15px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
