<template>
  <div 
    v-if="isExternalUri" 
    :style="styleExternalIcon" 
    class="svg-external-icon svg-icon" 
    v-bind="$attrs" 
  />
  <svg 
    v-else 
    :class="svgClass" 
    aria-hidden="true" 
    v-bind="$attrs"
  >
    <use :href="iconName" />
  </svg>
</template>

<script setup>
import { computed } from 'vue';
import { isExternal } from '@/utils/validate';

// 定义 props
const props = defineProps({
  iconClass: {
    type: String,
    required: true
  },
  className: {
    type: String,
    default: ''
  }
});

// 判断是否为外部图标
const isExternalUri = computed(() => isExternal(props.iconClass));

// 内部图标的路径
const iconName = computed(() => `#icon-${props.iconClass}`);

// SVG 类名
const svgClass = computed(() => {
  return props.className ? `svg-icon ${props.className}` : 'svg-icon';
});

// 外部图标的样式
const styleExternalIcon = computed(() => ({
  mask: `url(${props.iconClass}) no-repeat 50% 50%`,
  '-webkit-mask': `url(${props.iconClass}) no-repeat 50% 50%`
}));
</script>

<style scoped>
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.svg-external-icon {
  background-color: currentColor;
  mask-size: cover!important;
  display: inline-block;
}
</style>
