<template>
  <div class="property-container">
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-divider content-position="left">点位显示设置</el-divider>
      <el-form-item label="类型" prop="type">
        <el-select
          v-model="form.type"
          :teleported="false"
        >
          <el-option :label="'DEFAULT'" :value="'DEFAULT'"/>
          <el-option :label="'BOARD'" :value="'BOARD'"/>
          <el-option :label="'INDICATOR'" :value="'INDICATOR'"/>
          <el-option :label="'CONTROLLER'" :value="'CONTROLLER'"/>
        </el-select>
      </el-form-item>
      <el-form-item label="显示提示窗" prop="popoverPlace">
        <el-select
          v-model="form.popoverPlace"
          clearable
          :teleported="false"
        >
          <el-option :label="'top'" :value="'top'"/>
          <el-option :label="'left'" :value="'left'"/>
          <el-option :label="'right'" :value="'right'"/>
          <el-option :label="'bottom'" :value="'bottom'"/>
          <el-option :label="'top-start'" :value="'top-start'"/>
          <el-option :label="'top-end'" :value="'top-end'"/>
          <el-option :label="'left-start'" :value="'left-start'"/>
          <el-option :label="'left-end'" :value="'left-end'"/>
          <el-option :label="'right-start'" :value="'right-start'"/>
          <el-option :label="'right-end'" :value="'right-end'"/>
          <el-option :label="'bottom-start'" :value="'bottom-start'"/>
          <el-option :label="'bottom-end'" :value="'bottom-end'"/>
        </el-select>
      </el-form-item>
      <el-form-item label="控制指令" prop="cmdControls" v-if="form.type === 'CONTROLLER'" label-position="top" >
        <el-table
          ref="tableRef"
          :data="form.cmdControls"
          size="small"
          cell-class-name="position-table-cell"
          max-height="150"
          class="command-table"
        >
          <el-table-column prop="name" label="控件名" width="80">
            <template #default="scope">
              <el-input v-model="scope.row.name" class="input-pos" />
            </template>
          </el-table-column>
          <el-table-column prop="command" label="设备指令" >
            <template #default="scope">
              <el-select
                v-model="scope.row.command"
                value-key="id"
                filterable
                placeholder="请选择"
                style="width: 100%"
                popper-class="device-command-select-dropdown"
                @mousedown.stop
                @click.stop
              >
                <el-option v-for="item in props.deviceInstance.commands" :key="item.id" :label="item.name" :value="item" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="parameter" label="指令参数" >
            <template #default="scope">
              <el-input v-model="scope.row.parameter" class="input-pos" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="68px" align="center">
            <template #header>
              <span>操作</span>
              <el-button :icon="Plus" class="operation" @click="toAddCmd()" />
            </template>
            <template #default="scope">
              <div class="operation-container">
                <el-button :icon="Delete" class="operation" @click="deleteCommand(scope.row)" />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted, onBeforeUnmount } from 'vue'
import { Graph, Node } from '@antv/x6'
import { debounce } from '@/utils'
import { Edit, Delete, Plus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  },
  deviceInstance: {
    default: () => ({}),
    type: Object
  }
})

// Emits
const emit = defineEmits(["update:modelValue"]);


// 响应式数据
const defaultForm = { id: null, type: null, popoverPlace:null, cmdControls: [], javaClassName: null, configJavaClassName: null, layoutImage: null, layoutWidth: null, layoutHeight: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }
const form = reactive({ ...defaultForm })
const rules = reactive({
  name: [
    { required: true, message: '设备实例名称', trigger: 'blur' }
  ]
})
const currentSelectedPosIndex = ref(-1)
const initing = ref(false)
const formRef = ref(null)
const tableRef = ref(null)

// 计算属性
const transformNodeToFormData = () => {

  const curNode = props.curNode
  if (!curNode) {
    return
  }
  
  form.type = curNode.data.type;
  form.popoverPlace = curNode.data.popoverPlace;
  form.cmdControls = curNode.data.cmdControls || [];
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  if (!curNode) {
    return
  }

  curNode.data.type = form.type;
  curNode.data.popoverPlace = form.popoverPlace;
  curNode.data.cmdControls = form.cmdControls;
  
}

const toAddCmd = () => {
  form.cmdControls.push({ name: null, command: null, parameter: null })
}

const deleteCommand = (row) => {
  const index = form.cmdControls.indexOf(row)
  if (index > -1) {
    form.cmdControls.splice(index, 1)
  }
}



// 监听器
watch(
  () => form,
  (newVal) => {
    const curNode = props.curNode
    if (!curNode) {
      return
    }

    transformFormDataToNode()
  },
  {deep:true}
)

watch(()=>props.curNode, (newVal, oldVal) => {
  console.log('IndicatorProperty props.curNode changed..props.curNode='+props.curNode)
  if (newVal !== oldVal) {
    transformNodeToFormData()
  }
})

watch(()=>props, (newVal, oldVal) => {
  console.log('IndicatorProperty props changed..props.curNode='+props.curNode)
})

// 生命周期
onMounted(() => {
  console.log('IndicatorProperty created..props.curNode='+props.curNode?.id)
  transformNodeToFormData()
})

onBeforeUnmount(() => {
  console.log('IndicatorProperty unmounted..props.curNode='+props.curNode?.id)
})
</script>

<style lang="scss">
.property-container {
  width: 100%;
}
.circle-button {
  width: 40px;
  height: 40px;
}

.el-table .position-table-cell .cell {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell input {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell .input-pos input {
  width:56px;
}
.el-table .position-table-cell .cell .input-name {
  padding-right: 4px;
}

.operation {
  width: 18px;
  height: 18px;
  padding: 2px 2px;
  margin-left: 4px;
  margin-bottom: 4px;
}
.operation-container {
  display: flex;
}

/* 设备指令下拉选项样式 */
.device-command-select-dropdown {
  z-index: 99999 !important;
//  position: absolute !important;
}

/* 确保表格和表格单元格允许溢出 */
.command-table {
  overflow: visible !important;
}


.command-table .el-table__body-wrapper,
.command-table .el-table__header-wrapper {
  overflow: visible !important;
}

.el-table .position-table-cell {
  overflow: visible !important;
  position: relative !important;
}

.el-table .position-table-cell .cell {
  overflow: visible !important;
  position: relative !important;
}

/* 确保弹窗容器允许溢出  */
.el-popover {
  overflow: visible !important;
}

.el-tabs__content {
  overflow: visible !important;
}

.el-tab-pane {
  overflow: visible !important;
}

</style>
