<template>
  <div class="variables-editor-example">
    <h3>VariablesEditor 组件使用示例</h3>
    
    <!-- 示例1: 通过API加载变量定义 -->
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>示例1: 通过API加载变量定义</span>
        </div>
      </template>
      <div class="example-content">
        <el-form-item label="Action ID:">
          <el-input v-model="actionId" placeholder="请输入Action ID" style="width: 200px;" />
        </el-form-item>
        
        <VariablesEditor
          v-model="variables1"
          :action-id="actionId"
          @change="handleVariables1Change"
        />
        
        <div class="result">
          <strong>当前值:</strong>
          <pre>{{ variables1 || '无' }}</pre>
        </div>
      </div>
    </el-card>
    
    <!-- 示例2: 使用自定义变量数组 -->
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>示例2: 使用自定义变量数组</span>
        </div>
      </template>
      <div class="example-content">
        <VariablesEditor
          v-model="variables2"
          :variables="customVariables"
          label-width="25%"
          @change="handleVariables2Change"
        />
        
        <div class="result">
          <strong>当前值:</strong>
          <pre>{{ variables2 || '无' }}</pre>
        </div>
      </div>
    </el-card>
    
    <!-- 示例3: 带调试信息 -->
    <el-card class="example-card">
      <template #header>
        <div class="card-header">
          <span>示例3: 带调试信息</span>
        </div>
      </template>
      <div class="example-content">
        <VariablesEditor
          v-model="variables3"
          :variables="debugVariables"
          :show-debug-info="true"
          @change="handleVariables3Change"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import VariablesEditor from './VariablesEditor.vue'

// 响应式数据
const actionId = ref('')
const variables1 = ref(null)
const variables2 = ref('{"param1":"value1","param2":"value2"}')
const variables3 = ref(null)

// 自定义变量数组
const customVariables = reactive([
  {
    key: 'temperature',
    name: '温度',
    description: '设备工作温度',
    defaultValue: '25',
    value: ''
  },
  {
    key: 'pressure',
    name: '压力',
    description: '系统压力值',
    defaultValue: '1.0',
    value: ''
  },
  {
    key: 'speed',
    name: '速度',
    description: '运行速度',
    defaultValue: '100',
    value: ''
  }
])

const debugVariables = reactive([
  {
    key: 'debug_mode',
    name: '调试模式',
    description: '是否启用调试模式',
    defaultValue: 'false',
    value: 'true'
  },
  {
    key: 'log_level',
    name: '日志级别',
    description: '系统日志级别',
    defaultValue: 'INFO',
    value: 'DEBUG'
  }
])

// 事件处理
const handleVariables1Change = (variables, variablesObj) => {
  console.log('Variables1 changed:', variables, variablesObj)
}

const handleVariables2Change = (variables, variablesObj) => {
  console.log('Variables2 changed:', variables, variablesObj)
}

const handleVariables3Change = (variables, variablesObj) => {
  console.log('Variables3 changed:', variables, variablesObj)
}
</script>

<style lang="scss" scoped>
.variables-editor-example {
  padding: 20px;
  
  .example-card {
    margin-bottom: 20px;
    
    .card-header {
      font-weight: bold;
    }
    
    .example-content {
      .result {
        margin-top: 20px;
        padding: 12px;
        background: #f5f7fa;
        border-radius: 4px;
        
        pre {
          margin: 8px 0 0 0;
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }
}
</style>
