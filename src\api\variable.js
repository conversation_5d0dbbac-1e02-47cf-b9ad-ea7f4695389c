import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/variable',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/variable/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/variable',
    method: 'put',
    data
  })
}

export function loadFromProcedureConfig(params) {
  return request({
    url: 'api/variable/loadFromProcedureConfig',
    method: 'get',
    params
  })
}

export default { add, edit, del, loadFromProcedureConfig }
