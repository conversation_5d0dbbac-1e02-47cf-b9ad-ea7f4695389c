<template>
  <div id="app">
    <el-config-provider :locale="locale" >
      <router-view />
    </el-config-provider>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import elEn from 'element-plus/es/locale/lang/en';
import elZh from 'element-plus/es/locale/lang/zh-cn';

import {getLanguage} from '@/lang'

const locale = computed(() => {
  if (getLanguage() === 'zh-CN'){
    return elZh;
  }else{
    return elEn;
  }
});

</script>
