import request from '@/utils/request'

export function get(params) {
  return request({
    url: 'api/depotInfo',
    method: 'get',
    params
  })
}

export function getByArea(params) {
  return request({
    url: 'api/depotInfo/byArea',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/depotInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/depotInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/depotInfo',
    method: 'put',
    data
  })
}

export default { add, edit, del, get, getByArea }
