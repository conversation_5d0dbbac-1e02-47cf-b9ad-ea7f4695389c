import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/taskPrepare',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/taskPrepare/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/taskPrepare',
    method: 'put',
    data
  })
}

export function readRfTag(data) {
  return request({
    url: 'api/taskPrepare/readRfTag',
    method: 'post',
    data
  })
}
export function queryTaskPrepareForConveyor(params) {
  return request({
    url: 'api/taskPrepare/queryTaskPrepareForConveyor',
    method: 'get',
    params
  })
}

export function querySampleEntryStepStatus(params) {
  return request({
    url: 'api/taskPrepare/querySampleEntryStepStatus',
    method: 'get',
    params
  })
}

export function getCurrentTaskPrepareView(params) {
  return request({
    url: 'api/taskPrepare/getCurrentTaskPrepareView',
    method: 'get',
    params
  })
}
export function submitTagReadingStep(data) {
  return request({
    url: 'api/taskPrepare/submitTagReadingStep',
    method: 'post',
    data
  })
}
export function submitEntryConveyorAcceptingStep(data) {
  return request({
    url: 'api/taskPrepare/submitEntryConveyorAcceptingStep',
    method: 'post',
    data
  })
}

export function cancelCurrentTaskPrepareView(params) {
  return request({
    url: 'api/taskPrepare/cancelCurrentTaskPrepareView',
    method: 'get',
    params
  })
}

export function batchSaveTaskPrepares(data) {
  return request({
    url: 'api/taskPrepare/batchSaveTaskPrepares',
    method: 'post',
    data
  })
}

export function submitBatchTasks(data) {
  return request({
    url: 'api/taskPrepare/submitBatchTasks',
    method: 'post',
    data
  })
}

export function queryTagsToEdit(params) {
  return request({
    url: 'api/taskPrepare/queryTagsToEdit',
    method: 'get',
    params
  })
}

export function queryTaskPrepareByTag(params) {
  return request({
    url: 'api/taskPrepare/queryTaskPrepareByTag',
    method: 'get',
    params
  })
}

export function findLatestTaskPrepare(params) {
  return request({
    url: 'api/taskPrepare/findLatestTaskPrepare',
    method: 'get',
    params
  })
}





export default { add, edit, del, readRfTag, querySampleEntryStepStatus,
  queryTaskPrepareForConveyor,
  getCurrentTaskPrepareView,
  submitTagReadingStep,
  submitEntryConveyorAcceptingStep,
  cancelCurrentTaskPrepareView,
  submitBatchTasks,
  queryTagsToEdit,
  queryTaskPrepareByTag,
  batchSaveTaskPrepares,
  findLatestTaskPrepare }
