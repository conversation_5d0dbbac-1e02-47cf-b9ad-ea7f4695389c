<template>
    <el-dialog
      align-center
      :model-value="modelValue"
      :title="title"
      width="800px"
      :close-on-click-modal="false"
      @close="$emit('update:modelValue', false)"
      class="formula-editor-dialog"
    >
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px" class="formula-form">
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="类型" >
            <el-option v-for="item in dict.data.script_types" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="脚本名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="脚本描述">
          <el-input v-model="form.description" />
        </el-form-item>
        <el-form-item label="脚本内容">
          <LesFormulaEditor :cur-form="form" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="footer-inner-container">
          <el-button text @click="handleCancel">取消</el-button>
          <el-button :loading="loading" :icon="Finished" type="primary" @click="handleSubmit">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup>
  import { ref, defineProps, defineEmits, reactive, onMounted, watch } from 'vue'
  import { useDict } from '@/hooks/useDict'
  import LesFormulaEditor from './LesFormulaEditor.vue'
  import { Finished } from '@element-plus/icons-vue'
  import crudFormula from '@/api/formula'
  import { ElMessage } from 'element-plus'
  
  // 定义props
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '脚本编辑'
    },
    editId: {
      type: [String, Number],
      default: null
    },
    editName: {
      type: [String, Number],
      default: null
    }
  })
  
  // 定义事件
  const emit = defineEmits(['update:modelValue', 'success'])
  
  // 获取字典数据
  const { dict } = useDict(['script_types'])
  
  // 表单引用
  const formRef = ref(null)
  
  // 加载状态
  const loading = ref(false)
  
  // 默认表单数据
  const defaultForm = {
    id: null,
    type: 'FORMULA',
    name: null,
    description: null,
    content: null,
    deleteFlag: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  }
  
  // 表单数据
  const form = reactive({...defaultForm})
  
  // 表单验证规则
  const rules = {
    type: [
      { required: true, message: '请选择脚本类型', trigger: 'change' }
    ],
    name: [
      { required: true, message: '请输入脚本名称', trigger: 'blur' }
    ]
  }
  
  // 重置表单
  const resetForm = (row = {}) => {
    Object.keys(form).forEach(key => {
      form[key] = row[key] !== undefined ? row[key] : defaultForm[key]
    })
  }
  
  // 加载数据
  const loadData = async (id) => {
    if (!id) return
    
    try {
      loading.value = true
      const response = await crudFormula.queryForSelection({ id })
      if (response && response.length > 0) {
        resetForm(response[0])
      }
    } catch (error) {
      console.error('加载脚本数据失败:', error)
      ElMessage.error('加载脚本数据失败')
    } finally {
      loading.value = false
    }
  }
    // 加载数据
const loadDataByName = async (name) => {
    if (!name) return
    
    try {
      loading.value = true
      const response = await crudFormula.queryByName({ name })
      if (response) {
        resetForm(response)
      }
    } catch (error) {
      console.error('加载脚本数据失败:', error)
      ElMessage.error('加载脚本数据失败')
    } finally {
      loading.value = false
    }
  }
  
  // 提交表单
  const handleSubmit = async () => {
    try {
      const valid = await formRef.value.validate()
      if (!valid) return
      
      loading.value = true
      const method = form.id ? crudFormula.edit : crudFormula.add
      await method(form)
      
      ElMessage.success(form.id ? '编辑成功' : '新增成功')
      emit('success', form)
      emit('update:modelValue', false)
      resetForm()
    } catch (error) {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    } finally {
      loading.value = false
    }
  }
  
  // 取消操作
  const handleCancel = () => {
    resetForm()
    emit('update:modelValue', false)
  }
  
  // 监听editId变化，加载数据
  watch(() => props.editId, (newVal) => {
    if (newVal) {
      loadData(newVal)
    } else {
      resetForm()
    }
  }, { immediate: true })
    
  // 监听editName变化，加载数据
  watch(() => props.editName, (newVal) => {
    if (newVal) {
        loadDataByName(newVal)
    } else {
      resetForm()
    }
  }, { immediate: true })
  
  // 暴露formRef给父组件
  defineExpose({
    formRef,
    resetForm
  })
  </script>
  
  <style lang="scss" scoped>
  .formula-editor-dialog {
    background: linear-gradient(rgb(33 85 129) 7%, rgb(200 223 246) 7%, rgb(255 255 255) 100%);
    :deep(.el-dialog__body) {
      padding-right: 20px !important;
    } 
  }
  .formula-form {
   // margin-bottom: 0!important;
    padding-right: 20px !important;
    :deep(.el-form-item--small ) {
      margin-bottom: 18px !important;
    }
  }
  .footer-inner-container {
    padding-right: 20px !important;
  }
  </style>