{"name": "admin-web-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "test": "vite --mode test", "prod": "vite --mode production", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@formulajs/formulajs": "^4.4.5", "axios": "^1.4.0", "clipboard": "2.0.4", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "connect": "3.6.6", "core-js": "^2.6.12", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.9.3", "file-saver": "1.3.8", "fuse.js": "3.4.4", "js-beautify": "^1.10.2", "js-cookie": "2.2.0", "jsencrypt": "^3.0.0-rc.1", "jszip": "^3.7.1", "mavon-editor": "^2.9.1", "moment": "^2.30.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "2.4.0", "qrcode-vue3": "^1.7.1", "qs": "^6.10.1", "screenfull": "4.2.0", "sortablejs": "1.8.4", "vue": "^3.5.13", "vue-echarts": "^6.0.0", "vue-highlightjs": "^1.3.3", "vue-i18n": "^11.0.1", "vue-image-crop-upload": "^2.5.0", "vue-json-pretty": "^2.4.0", "vue-router": "^4.5.0", "vue3-print-nb": "^0.1.4", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/tsconfig": "^0.7.0", "fast-glob": "^3.3.3", "sass": "^1.79.0", "typescript": "~5.6.2", "vite": "^6.0.5", "vite-plugin-css-export": "^3.0.2", "vite-plugin-lang-jsx": "^1.5.3", "vite-plugin-sass-dts": "^1.3.30", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.0"}}