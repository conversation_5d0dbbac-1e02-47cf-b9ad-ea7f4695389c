<template>
  <div ref="graphPanelRef" class="content">
    <!--流程图工具栏-->
    <div v-if="!bTaskStarted" class="panel">
    <div class="toolbar" >
      <!-- <tool-bar v-if="isReady" /> -->
      <el-button-group class="toolbar-group">

        <el-tooltip class="item" effect="dark" content="放大" placement="top">
          <el-button
            size="small"
            :icon="ZoomIn"
            @click="graph.zoom(0.2)"
          />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="缩小" placement="top">
          <el-button
            size="small"
            :icon="ZoomOut"
            @click="graph.zoom(-0.2)"
          />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="调整到合适大小" placement="top">
          <el-button
            size="small"
            :icon="Aim"
            @click="zoomToFit()"
          />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="重置所有设备点位状态" placement="top">
          <el-button
            size="small"
            :icon="Coordinate"
            @click="resetAllPositionsHandler()"
          />
        </el-tooltip>
        <el-popover
          placement="bottom-end"
          width="150"
          trigger="click"
        >
          <el-button
            slot="reference"
            size="small"
            :icon="Grid"
          >
            <i
              class="fa fa-caret-down"
              aria-hidden="true"
            />
          </el-button>

        </el-popover>

      </el-button-group>
    </div>
    </div>
    <!--流程图画板-->
    <div id="main_board_container" />
  </div>
  <!--流程图画板-->
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, provide, nextTick } from 'vue'
import { ElNotification } from 'element-plus'
import { Graph, FunctionExt, Shape } from '@antv/x6'
import { stroke } from '@antv/x6/lib/registry/highlighter/stroke'
import { add, edit, getLatest, query, resetAllPositions } from '@/api/deviceLayout'
import { register } from '@antv/x6-vue-shape'
import DeviceNode from '@/views/devicelayout/DeviceNode.vue'
import { useStore } from 'vuex'
import useWebSocket from '@/hooks/useWebSocket'
import {
  ZoomIn,
  ZoomOut,
  Aim,
  Coordinate,
  Grid,
  Connection
} from '@element-plus/icons-vue';

// 注册自定义节点
register({
  shape: 'device-node',
  width: 212,
  height: 48,
  component: DeviceNode,
  onUnmount: ({node})=>{
      console.log('DeviceNode. unmount BEGIN ...: '+ node?.id)
  }
})

// 响应式状态
const graphPanelRef = ref(null)
const modules = ref([])
const permission = ref({
  add: ['admin', 'areaInfo:add'],
  edit: ['admin', 'areaInfo:edit'],
  del: ['admin', 'areaInfo:del']
})
const graph = ref(null)
const selectedNode = ref(null)
const nodeLabel = ref('')
const updateNodeLabel = ref('')
const isConnecting = ref(false)
const sourceNode = ref(null)
const tempEdge = ref(null)
const dataStore = ref(null)
const ws = ref(null)
const websocketState = ref('')
const resizeObserver = ref(null)

// 获取 Vuex store
const store = useStore()

// 提供 store 给自定义节点组件
provide('store', store)
// 任务是否
const bTaskStarted = computed(() => {
  if (!store) {
    return false
  }
  const ret = store.state.task.task.started
  
  return ret
})
// 初始化图表
const initGraph = () => {
  const container = document.getElementById('main_board_container')
  console.log('graph.container=' + container)
  graph.value = new Graph({
    container: container, // 画布容器
    width: container.offsetWidth, // 画布宽
    height: container.offsetHeight, // 画布高
    autoResize: true,
    background: false, // 背景（透明）
    snapline: false, // 对齐线
    interacting: false,
    panning: {
      enabled: true
    },
    mousewheel: {
      enabled: true, // 支持滚动放大缩小
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    }
  })

  // 将 store 附加到 graph 对象，使自定义节点可以访问
  graph.value.store = store

  getLatest(null).then(res => {
    console.log('getLatest.res=' + res)
    if (res == null) {
      dataStore.value = {}
    } else {
      dataStore.value = res
      graph.value.dataStore = res
      graph.value.fromJSON(JSON.parse(dataStore.value.layoutSvg))
      graph.value.getNodes().forEach(node => {
        const dataVisible = node.getData()?.layoutVisible
        if (dataVisible === false) {
          node.hide()
        }
        
      })

      zoomToFit()
      listenGraphEvent()
    }
  })
}

// 缩放以适应内容
const zoomToFit = () => {
  graph.value.zoomToFit({
    padding:{top: 48, left:34,right:24,bottom:24},
    minScale: 0.5,
    maxScale: 4
  })

  graph.value.centerContent()
}

const listenGraphEvent = () => {
  graph.value.on('scale', ({ sx, sy, ox, oy }) => {
    repositionIndicatores()
  })
graph.value.on('resize', ({ width, height }) => {
  repositionIndicatores()
})
graph.value.on('translate', ({ tx, ty }) => {
  repositionIndicatores()
})
}

const repositionIndicatores = () => {
  console.log('repositionIndicatores....')
  const nodes = graph.value.getNodes()
  nodes.forEach(node => {
    node.setData({
      ...node.getData(),
      changeState: 'RESCALED'
    })
  })
}

// 监听容器大小变化
const observeResize = () => {
  const element = graphPanelRef.value
  resizeObserver.value = new ResizeObserver(entries => {
    for (const entry of entries) {
      // 处理尺寸变化的逻辑
      nextTick(()=>{
        console.log('Graph Container resized:', entry.contentRect)
        graph.value.resize(entry.contentRect.width, entry.contentRect.height)
        zoomToFit()
      })
    }
  })
  resizeObserver.value.observe(element)
}

// WebSocket 相关方法
const {} = useWebSocket({
  url: ( id ) => `${import.meta.env.VITE_APP_WS_API}/webSocket/positionStateChange_${id}`,
  onMessage: ( message ) => {
    onTaskResponse(message)
  }
})

// 任务返回响应
const onTaskResponse = (data) => {
  const dto = JSON.parse(data.msg)

  console.log('Received PositionChange:' + JSON.stringify(dto))
  if (dataStore.value?.deviceInstanceList === undefined || dataStore.value.deviceInstanceList.length === 0) {
    return null
  }

  // For DeviceInstance connection status was changed.
  if (dto.operation && dto.operation === 'DEV_INST_STATUS_CHANGE') {
    for (let i in dataStore.value.deviceInstanceList) {
      const ins = dataStore.value.deviceInstanceList[i]
      if (ins.id === dto.id) {
        ins.initialized = dto.initialized
        ins.status = dto.status
        ins.message = dto.message
        const node = graph.value.getCellById(ins.layoutNodeId)
        if (node) {
          node.setData({ ...node.getData(), changeState: 'DEV_INST_STATUS_CHANGE' })
        }
        break
      }
    }
    if (dto.type === 'FRAME' ) {
      
      if (dto.status === 'LOCKED' || dto.status === 'UNLOCKED') {
        const ctrlStatus = JSON.parse(dto.message)
        ctrlStatus.doorStatus = ctrlStatus.status
        store.dispatch('setCtrlStatus', ctrlStatus)
      }else{
        store.dispatch('setCtrlStatusToDefault')
      }
    }else if (dto.type === 'ROBOT') {
      const motion = {}
      motion.status = dto.status
      motion.name = dto.name
      if (dto.status === 'FAILED'){
        dto.errorMsg = dto.message
        store.dispatch('updateMotionInfo', motion)
        return
      }
      const msg = JSON.parse(dto.message)
      Object.assign(motion, msg.data)
      
      store.dispatch('updateMotionInfo', motion)
    }
    return
  }

  // For Position status was changed.
  for (let i in dataStore.value.deviceInstanceList) {
    const ins = dataStore.value.deviceInstanceList[i]
    if (ins.positions) {
      for (let posIdx in ins.positions) {
        const pos = ins.positions[posIdx]
        if (pos.id === dto.id) {
          Object.assign(pos, dto)
          //ins.positions.splice(posIdx, 1, dto)
          const node = graph.value.getCellById(ins.layoutNodeId)
          if (node) {
            node.setData({ ...node.getData(), ...{posIndex:posIdx}, changeState: 'POSITION_CHANGE' })
          }
          break
        }
      }
    }
  }
}


const getDeviceInstanceByNodeId = (nodeId) => {
  if (dataStore.value.deviceInstanceList === undefined || dataStore.value.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in dataStore.value.deviceInstanceList) {
    const ins = dataStore.value.deviceInstanceList[i]
    if (ins.layoutNodeId === nodeId) {
      return ins
    }
  }
  return null
}

const resetAllPositionsHandler = async () => {
  try {
    const res = await resetAllPositions(dataStore.value)
    const updatedDevInsts = res
    for (const devInst of updatedDevInsts) {
      const nodeId = devInst.layoutNodeId
      const storeDevInst = getDeviceInstanceByNodeId(nodeId)
      if (storeDevInst) {
        storeDevInst.positions = devInst.positions
        const node = graph.value.getCellById(nodeId)
        if (node) {
          node.setData({ ... node.getData(), changeState: 'POSITION_CHANGE' })
        }
      }
    }
    ElNotification({
      title: '重置点位成功',
      type: 'success',
      duration: 2500
    })
    console.log('重置点位成功')
  } catch (error) {
    console.error('Reset positions failed:', error)
  }
}
// 生命周期钩子
onMounted(() => {
  initGraph()
  observeResize()
})

const clearDeviceNodes = () => {
  for (const node of graph.value.getNodes()){
    node.setData({ ... node.getData(), changeState: 'NODE_UNMOUNT' })
  } 
}

onBeforeUnmount(() => {
  console.log('MainBoard before unmounted.')
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
  clearDeviceNodes()
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.flow {
  width: 100%;
  height: 100vh;
}

.content {
  width: 100%;
  height: calc(100% - 4px);// calc(100% - 130px);
  display: block;
}

#main_board_container {
  width: 100%;
  height: 100%;
  position: relative;
}

#stencil {
  width: 290px;
  height: 100%;
  position: relative;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}
.panel {
    width: 228px;
    height: 40px;
    position:absolute;
    z-index: 900;
    margin-left: 40px;
  }

  .panel .toolbar {
    width: 100%;
    height: 38px;
    padding-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
   // background-color: #f7f9fb80;
  //  border-bottom: 1px solid rgba(0, 0, 0, 0.08);

  }
  .toolbar-group {
    border-radius: 20px;
    background-color: #FFFFFF;
    padding: 0px 20px;
  }
  .panel #container {
    width: 100%;
    height: calc(100% - 38px);
    background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
    background-size: cover;
  }

.config {
  width: 290px;
  height: 100%;
  padding: 0 10px;
  border-left: 1px solid rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.node_icon {
  color: darkcyan;
}
</style>
