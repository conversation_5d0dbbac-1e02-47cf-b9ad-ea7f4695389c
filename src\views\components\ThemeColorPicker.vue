<template>
  <div class="theme-color-picker">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="纯色" name="solid">
        <el-color-picker
          v-model="currentColor"
          :predefine="colorPresets"
          show-alpha
          popper-class="theme-picker-dropdown"
          @change="handleColorChange"
        />
      </el-tab-pane>
      <el-tab-pane label="渐变" name="gradient">
        <div class="gradient-controls">
          <div class="gradient-type">
            <el-radio-group v-model="gradientType" @change="updateGradient">
              <el-radio label="linear">线性渐变</el-radio>
              <el-radio label="radial">径向渐变</el-radio>
            </el-radio-group>
          </div>
          <template v-if="gradientType === 'linear'">
            <el-slider
              v-model="gradientAngle"
              :min="0"
              :max="360"
              @change="updateGradient"
            />
          </template>
          <div class="gradient-stops">
            <div v-for="(stop, index) in gradientStops" :key="index" class="gradient-stop">
              <el-color-picker
                v-model="stop.color"
                show-alpha
                @change="updateGradient"
              />
              <el-slider
                v-model="stop.position"
                :min="0"
                :max="100"
                @change="updateGradient"
              />
              <el-button
                v-if="gradientStops.length > 2"
                @click="removeGradientStop(index)"
                type="danger"
                size="small"
                circle
                :icon="Delete"
              />
            </div>
            <el-button
              v-if="gradientStops.length < 5"
              @click="addGradientStop"
              type="primary"
              size="small"
            >添加颜色</el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div class="color-preview" :style="previewStyle"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from "vue";
import { defineProps, defineEmits } from "vue";
import { Delete } from "@element-plus/icons-vue";

const props = defineProps({
  modelValue: String,
});

const emit = defineEmits(["update:modelValue"]);

// 颜色预设
const colorPresets = [
  "#4080FF",
  "#ff4500",
  "#ff8c00",
  "#90ee90",
  "#00ced1",
  "#1e90ff",
  "#c71585",
  "rgba(255, 69, 0, 0.68)",
  "rgb(255, 120, 0)",
  "hsva(120, 40, 94)",
];

const activeTab = ref('solid');
const currentColor = ref(props.modelValue);
const gradientType = ref('linear');
const gradientAngle = ref(90);
const gradientStops = ref([
  { color: '#4080FF', position: 0 },
  { color: '#ff4500', position: 100 }
]);

// 计算预览样式
const previewStyle = computed(() => {
  if (activeTab.value === 'transparent') {
    return {
      background: 'transparent'
    };
  } else if (activeTab.value === 'solid') {
    return {
      background: currentColor.value
    };
  } else {
    const stops = gradientStops.value
      .map(stop => `${stop.color} ${stop.position}%`)
      .join(', ');
    
    if (gradientType.value === 'linear') {
      return {
        background: `linear-gradient(${gradientAngle.value}deg, ${stops})`
      };
    } else {
      return {
        background: `radial-gradient(circle, ${stops})`
      };
    }
  }
});

// 方法
const handleColorChange = (value) => {
  emit("update:modelValue", value);
};

const updateGradient = () => {
  const gradientValue = previewStyle.value.background;
  emit("update:modelValue", gradientValue);
};

const addGradientStop = () => {
  if (gradientStops.value.length < 5) {
    gradientStops.value.push({
      color: '#ffffff',
      position: 50
    });
    updateGradient();
  }
};

const removeGradientStop = (index) => {
  gradientStops.value.splice(index, 1);
  updateGradient();
};

const setTransparent = () => {
  emit("update:modelValue", 'transparent');
};

// 监听父组件传入的值
watch(() => props.modelValue, (newValue) => {
  if (!newValue) return;
  
  if (newValue.startsWith('rgba') || newValue.startsWith('rgb') || newValue.startsWith('#')) {
    activeTab.value = 'solid';
    currentColor.value = newValue;
  } else if (newValue.startsWith('linear-gradient')) {
    activeTab.value = 'gradient';
    gradientType.value = 'linear';
    
    // 解析线性渐变
    const match = newValue.match(/linear-gradient\((\d+)deg,\s*(.+)\)/);
    if (match) {
      // 设置角度
      gradientAngle.value = parseInt(match[1]);
      
      // 解析颜色停止点，支持 rgba 格式
      const stopsStr = match[2];
      const stopMatches = stopsStr.match(/(?:rgba?\([^)]+\)|#[a-fA-F0-9]+)\s+\d+%/g);
      
      if (stopMatches) {
        const stops = stopMatches.map(stop => {
          const [color, position] = stop.match(/((?:rgba?\([^)]+\)|#[a-fA-F0-9]+))\s+(\d+)%/).slice(1);
          return {
            color: color,
            position: parseInt(position)
          };
        });
        gradientStops.value = stops;
      }
    }
  } else if (newValue.startsWith('radial-gradient')) {
    activeTab.value = 'gradient';
    gradientType.value = 'radial';
  } else {
    activeTab.value = 'solid';
    currentColor.value = newValue;
  }
});


</script>

<style lang="scss" scoped>
.theme-color-picker {
  width: 300px;
  
  .gradient-controls {
    padding: 10px;
    
    .gradient-type {
      margin-bottom: 15px;
    }
    
    .gradient-stops {
      .gradient-stop {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        
        .el-color-picker {
          margin-right: 10px;
        }
        
        .el-slider {
          flex: 1;
          margin-right: 10px;
        }
      }
    }
  }
  
  .color-preview {
    height: 140px;
    border-radius: 4px;
    margin-top: 10px;
    border: 1px solid #dcdfe6;
    background-image: linear-gradient(45deg, #ccc 25%, transparent 25%),
                    linear-gradient(-45deg, #ccc 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #ccc 75%),
                    linear-gradient(-45deg, transparent 75%, #ccc 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  }
}

:deep(.theme-picker-dropdown) {
  z-index: 99999 !important;
}

:deep(.el-tabs__content) {
  padding: 15px;
}
</style>
