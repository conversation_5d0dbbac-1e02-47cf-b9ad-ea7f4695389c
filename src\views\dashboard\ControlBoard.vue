<template>
  <div class="control-board-container">
    <div class="inner-container robot-inner-container">
      <RobotStatusBoard />
    </div>
    <div class="inner-container alert-inner-container">
      <AlertBoard />
    </div>
    <div class="status-inner-container">
      <StatusBoard />
    </div>

  </div>

</template>

<script>

import { mapGetters } from 'vuex'
import RobotStatusBoard from './RobotStatusBoard.vue'
import AlertBoard from './AlertBoard.vue'
import StatusBoard from './StatusBoard.vue'

export default {
  components: { RobotStatusBoard, AlertBoard, StatusBoard },
  data() {
    return {
      chart: null,
      robotSpeed: 50
    }
  },
  watch: {

  },
  computed: {
    ...mapGetters([
      'imagesUploadApi',
      'baseApi'
    ])
  },
  mounted() {

  },
  beforeDestroy() {

  },
  methods: {
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .flow {
   /* width: 100vw; */
    width: 100%;
    height: 100vh;
  }

  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  .control-board-container {
    width: 290px;
    height: 100%;
    padding-top: 10px;
    display: flex;
    flex-direction: column;

    .robot-inner-container {
      height:200px;
    }
    .alert-inner-container {
      flex: 1;
    }
    .status-inner-container {
      padding-left: 10px;
      padding-bottom: 10px;
    }
  }

  .robot-status-container {
    margin-bottom: 20px;
  }

  .robot-status-wrapper {
    display: flex;

    .img {
      width: 60px;
    }
    .left {
      width: 60px;
      display: flex;
      align-items: center;
    }
    .right .status {
      flex: 1;
      padding-left: 20px;
    }
    .status-value {
      padding-left: 10px;
      color: green;
    }
  }

  .axis-container {
    margin-left: 20px;
    width:200px;
    height:60px;
    background-size: 40px 40px; /* 背景图片适应按钮 */
    background-repeat: no-repeat;
      background-position: left; /* 背景图片居中 */
      background-image: url('http://localhost:8000/file/%E5%85%B6%E4%BB%96/%E5%9D%90%E6%A0%87_%E5%9D%90%E6%A0%87%E7%B3%BB-20240621125030697.svg');
      display: flex;
      flex-direction: column;
      .z-axis {
        padding-left: 26px;
      }
      .y-axis {
        padding-left: 50px;
        padding-top: 6px;
      }
      .x-axis {
        padding-left: 6px;
        padding-top: 10px;
      }
  }

  .inner-container {
    border: 1px solid #238DBD;
    box-sizing: border-box;
    background-color: #158CBE40; /* 半透明底色，蓝色 */
    border-radius: 8px; /* 圆角矩形 */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* 边框阴影 */
    padding: 10px; /* 内边距 */
    color: white; /* 文本颜色 */
    margin: 0px 0px 20px 10px;

    margin-bottom: 10px;
    .title {
      padding:10px;
    }
  }

  #alert-board {
    width:100%;
    height: 160px;
  }

</style>
