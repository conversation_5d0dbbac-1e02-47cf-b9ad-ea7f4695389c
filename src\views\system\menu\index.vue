<template>
  <div class="app-container">
    <!-- 工具栏 -->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item label="关键字" prop="blurry">
            <el-input
              v-model="query.blurry"
              clearable
              placeholder="模糊搜索"
              style="width: 200px"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="创建时间" >
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rrOperation />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <crudOperation :permission="permission" />

    <!-- 表单对话框 -->
    <el-dialog
      align-center
      append-to-body
      :close-on-click-modal="false"
      :before-close="crud.cancelCU"
      v-model="dialogVisible"
      :title="crud.status.title"
      width="580px"
    >
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        size="small"
        label-width="80px"
      >
        <!-- 表单内容保持不变，仅调整语法 -->
        <el-form-item label="菜单类型" prop="type">
          <el-radio-group v-model="form.type" style="width: 178px">
            <el-radio-button :value="0">目录</el-radio-button>
            <el-radio-button :value="1">菜单</el-radio-button>
            <el-radio-button :value="2">按钮</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-show="form.type.toString() !== '2'" label="菜单图标" prop="icon">
          <IconSelect v-model="form.icon" :width="'450px'" />
          <!--
          <el-popover
            placement="bottom-start"
            width="450"
            trigger="click"
            @show="$refs['iconSelect'].reset()"
          >
            <IconSelect ref="iconSelect" @selected="selected" />
            <el-input slot="reference" v-model="form.icon" style="width: 450px;" placeholder="点击选择图标" readonly>
              <svg-icon v-if="form.icon" slot="prefix" :icon-class="form.icon" class="el-input__icon" style="height: 32px;width: 16px;" />
              <i v-else slot="prefix" class="el-icon-search el-input__icon" />
            </el-input>
          </el-popover>
          -->
        </el-form-item>
        <el-form-item v-show="form.type.toString() !== '2'" label="外链菜单" prop="iFrame">
          <el-radio-group v-model="form.iFrame">
            <el-radio-button :value="true">是</el-radio-button>
            <el-radio-button :value="false">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="form.type.toString() === '1'" label="菜单缓存" prop="cache">
          <el-radio-group v-model="form.cache" >
            <el-radio-button :value="true">是</el-radio-button>
            <el-radio-button :value="false">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="form.type.toString() !== '2'" label="菜单可见" prop="hidden">
          <el-radio-group v-model="form.hidden" >
            <el-radio-button :value="false">是</el-radio-button>
            <el-radio-button :value="true">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.type.toString() !== '2'" label="菜单标题" prop="title">
          <el-input v-model="form.title" :style=" form.type.toString() === '0' ? 'width: 450px' : 'width: 178px'" placeholder="菜单标题" />
        </el-form-item>
        <el-form-item v-if="form.type.toString() === '2'" label="按钮名称" prop="title">
          <el-input v-model="form.title" placeholder="按钮名称" style="width: 178px;" />
        </el-form-item>
        <el-form-item v-show="form.type.toString() !== '0'" label="权限标识" prop="permission">
          <el-input v-model="form.permission" :disabled="form.iFrame.toString() === 'true'" placeholder="权限标识" style="width: 178px;" />
        </el-form-item>
        <el-form-item v-if="form.type.toString() !== '2'" label="路由地址" prop="path">
          <el-input v-model="form.path" placeholder="路由地址" style="width: 178px;" />
        </el-form-item>
        <el-form-item label="菜单排序" prop="menuSort">
          <el-input-number v-model.number="form.menuSort" :min="0" :max="999" controls-position="right" style="width: 178px;" />
        </el-form-item>
        <el-form-item v-show="form.iFrame.toString() !== 'true' && form.type.toString() === '1'" label="组件名称" prop="componentName">
          <el-input v-model="form.componentName" style="width: 178px;" placeholder="匹配组件内Name字段" />
        </el-form-item>
        <el-form-item v-show="form.iFrame.toString() !== 'true' && form.type.toString() === '1'" label="组件路径" prop="component">
          <el-input v-model="form.component" style="width: 178px;" placeholder="组件路径" />
        </el-form-item>

        <el-form-item label="上级类目" prop="pid">
          <el-tree-select
            v-model="form.pid"
            :data="menus"
            :load="loadMenus"
            style="width: 450px"
            placeholder="选择上级类目"
            
            value-key="id"
            node-key="id"
            check-strictly
            :props="treeProps"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">
          确认
        </el-button>
      </template>
    </el-dialog>

    <!-- 表格 -->
    <el-table
      ref="tableRef"
      v-loading="crud.loading"
      :data="crud.data"
      lazy
      :load="getMenus"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      row-key="id"
      @selection-change="crud.selectionChangeHandler"
    >
      <!-- 表格列定义 -->
      <el-table-column type="selection" width="55" />
      <el-table-column label="菜单标题" prop="title" width="225" show-overflow-tooltip />
      
      <!-- 图标列 -->
      <el-table-column prop="icon" label="图标" align="center" width="60">
        <template #default="{ row }">
          <svg-icon :icon-class="row.icon || ''" />
        </template>
      </el-table-column>

      <!-- 其他列定义 -->
      <el-table-column prop="menuSort" align="center" label="排序">
        <template  #default="{ row }">
          {{ row.menuSort }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="permission" label="权限标识" />
      <el-table-column :show-overflow-tooltip="true" prop="component" label="组件路径" />
      <el-table-column prop="iFrame" label="外链" width="75px">
        <template #default="{ row }">
          <span v-if="row.iFrame">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="cache" label="缓存" width="75px">
        <template #default="{ row }">
          <span v-if="row.cache">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="hidden" label="可见" width="75px">
        <template #default="{ row }">
          <span v-if="row.hidden">否</span>
          <span v-else>是</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建日期" width="135px" />

      <el-table-column v-if="hasPermission" label="操作" width="130" align="center" fixed="right">
        <template #default="{ row }">
          <udOperation
            :data="row"
            :permission="permission"
            msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, reactive, provide, inject, computed, onMounted } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useStore } from 'vuex'
import { getMenuSuperior, getMenusTree } from '@/api/system/menu'
import crudMenu from '@/api/system/menu'
import IconSelect from '@/components/IconSelect/index.vue'
import DateRangePicker from '@/components/DateRangePicker'

import rrOperation from '@/components/crud/RR.operation'
import crudOperation from '@/components/crud/CRUD.operation'
import udOperation from '@/components/crud/UD.operation'
import pagination from  '@/components/crud/Pagination'

const store = useStore()
const formRef = ref(null)
const tableRef = ref(null)
const menus = ref([])

// CRUD 配置
const { crud, query, form, rules } = useCrud({
  title: '菜单',
  url: 'api/menus',
  query: {
    blurry: null,
    createTime: null
  },
  formRef,
  tableRef,
  rules: {
    title: [
      { required: true, message: '请输入标题', trigger: 'blur' }
    ],
    path: [
      { required: true, message: '请输入地址', trigger: 'blur' }
    ]
  },
  defaultForm: {
    id: null,
    title: null,
    menuSort: 999,
    path: null,
    component: null,
    componentName: null,
    iFrame: false,
    roles: [],
    pid: 0,
    icon: null,
    cache: false,
    hidden: false,
    type: 0,
    permission: null,
    appName: 'CARRIOR_APP'
  },
  crudMethod: { ...crudMenu },
})

provide('crud', crud);

// 权限计算属性
const hasPermission = computed(() => {
  return checkPer(['admin', 'menu:edit', 'menu:del'])
})
debugger
// 权限管理
const  checkPer = inject('checkPer');

// 权限配置
const permission = {
  add: ['admin', 'menu:add'],
  edit: ['admin', 'menu:edit'],
  del: ['admin', 'menu:del']
}

// 树形配置
const treeProps = {
  value: 'id',        // 节点唯一标识
  label: 'label',      // 显示名称
  children: 'children', // 子节点字段
  isLeaf: (data) => {  // 判断是否为叶子节点
   // debugger
    return data.leaf || (!data.hasChildren)
  }
}
// 计算属性
const dialogVisible = computed(() => crud.status.cu > 0)

// 生命周期
onMounted(() => {
  initMenus()
  crud.refresh()
})

// 初始化菜单数据
const initMenus = async () => {
  try {
    const res = await getMenusTree(0)
    debugger
    menus.value = [{ id: 0, label: '顶级类目', children: processMenuData(res) }]
  } catch (error) {
    ElMessage.error('菜单加载失败')
  }
}

// 处理菜单数据
const processMenuData = (data) => {
  debugger
  return data.map(item => ({
    ...item,
    children: item.children ? processMenuData(item.children) : null
  }))
}

// 加载子菜单
const loadMenus = async (treeNode, resolve) => {
  debugger
  //if (treeNode.level === 0) return resolve([{ id: 0, label: '顶级类目', children: [] }])
  try {
    const res = await getMenusTree(treeNode.id)
    resolve(processMenuData(res))
  } catch (error) {
    ElMessage.error('子菜单加载失败')
    resolve([])
  }
}
const getMenus = (tree, treeNode, resolve) => {
  const params = { pid: tree.id }
  setTimeout(() => {
    crudMenu.getMenus(params).then(res => {
      resolve(res.content)
    })
  }, 100)
}

// 选中图标
const selected = (name) => {
  form.value.icon = name
}


</script>

<style scoped>
.app-container {
  padding: 20px;
}

.head-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

:deep(.el-tree-select) {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}
</style>