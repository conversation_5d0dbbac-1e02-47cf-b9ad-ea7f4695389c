<script lang="tsx">
import { defineComponent, h } from 'vue';

export default defineComponent({
  name: 'MenuItem',
  props: {
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    return () => {
      const vnodes = [];

      if (props.icon) {
        vnodes.push(<svg-icon icon-class={props.icon} />);
      }

      if (props.title) {
        vnodes.push(<span style="margin-left:4px">{props.title}</span>);
      }

      return vnodes;
    };
  },
});
</script>