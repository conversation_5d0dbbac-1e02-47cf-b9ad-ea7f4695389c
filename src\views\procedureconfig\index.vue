<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item label="流程名称" prop="name">
            <el-input
              v-model="query.name"
              clearable
              :placeholder="$t('流程名称')"
              style="width: 200px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="创建时间">
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rr-operation />
          </el-form-item>
        </el-form>
      </div>
    </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crud-operation :permission="permission" :crud="crud" >
        <template #right>
          <el-button
            v-permission="permission.add"
            size="small"
            plain
            :icon="Delete"
            @click="toRemoveNodes"
          >
            {{ $t('清理无效节点') }}
          </el-button>
        </template>
      </crud-operation>
      
      <!--表格渲染-->
      <el-table
        ref="tableRef"
        v-loading="crud.loading"
        :data="crud.data"
        size="small"
        style="width: 100%;"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="type" label="流程类型" >
          <template #default="{ row }">
          {{ getDictLabel('proc_types', row.type) }}
        </template>
        </el-table-column>
        <el-table-column prop="deriveProcNames" label="派生于流程" >
          <template #default="{ row }">
            <el-tag v-for="(item, index) in row.deriveProcNames" :key="index">
              {{ item }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updateTime" label="更新时间" />
        <el-table-column v-if="checkPer(['admin','procedure:edit','procedure:del'])" label="操作" width="200px" align="center">
          <template #default="{ row }">
            <div class="ud-operation-container">
              <div class="ud-operation-append">
                <el-button
                  v-permission="permission.edit"
                  size="small"
                  type="danger"
                  :icon="VideoPlay"
                  @click="onDebug(row)"
                />
              </div>
              <ud-operation
                :data="row"
                :permission="permission"
                :crud="crud"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    
  </div>
</template>

<script setup>
import { ref, provide, inject, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCrud } from '@/hooks/useCrud'
import crudProcedure from '@/api/procedure'
import CrudOperation from '@/components/crud/CRUD.Operation.vue'
import RrOperation from '@/components/crud/RR.Operation.vue'
import UdOperation from '@/components/crud/UD.Operation.vue'
import Pagination from '@/components/crud/Pagination.vue'
import DateRangePicker from '@/components/DateRangePicker'
import { useDict } from '@/hooks/useDict'

import {
  VideoPlay
} from '@element-plus/icons-vue'

const defaultForm = {
  id: null,
  name: null,
  description: null,
  entryMethodId: null,
  exitMethodId: null,
  dagJson: null,
  status: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const router = useRouter()
const checkPer = inject('checkPer')
const formRef = ref(null)
const tableRef = ref(null)

// 使用字典
const { dict, getDictLabel } = useDict(['proc_types'])

const permission = {
  add: ['admin', 'procedure:add'],
  edit: ['admin', 'procedure:edit'],
  del: ['admin', 'procedure:del']
}

const { crud, CRUD, query, form, rules } = useCrud({
  title: '流程管理',
  url: 'api/procedure',
  idField: 'id',
  sort: 'id,desc',
  crudMethod: { ...crudProcedure },
  defaultForm,
  permission
})

// 初始化CRUD钩子
crud.hooks[CRUD.HOOK.beforeToAdd] = () => {
  router.push('/config-list/procedure_config')
  return false
}

crud.hooks[CRUD.HOOK.beforeToEdit] = (ctx) => {
  router.push({
    path: '/config-list/procedure_config',
    query: { id: ctx.form.id, form: ctx.form, addOrEdit: 'edit' }
  })
  return false
}

const toRemoveNodes = () => {
  router.push({
    path: '/config-list/proc-node-clean'
  })
}

// 调试方法
const onDebug = (row) => {
  router.push({
    path: '/config-list/procedure_debug',
    query: { id: row.id, name: row.name }
  })
}

onMounted(() => {
  crud.toQuery()
})

// 初始化
provide('crud', crud)
</script>

<style scoped>
.ud-operation-container {
  display: flex;
  flex-direction: row;
}
.ud-operation-append {
  width:80px;
}
</style>
