<template>
  <el-dropdown trigger="click" @command="handleLanguageChange">
    <div class="lang-icon">
      <svg-icon icon-class="language" :size="size" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="item in langOptions"
          :key="item.value"
          :command="item.value"
        >
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup >
import { useI18n } from "vue-i18n";
import Cookies from 'js-cookie';

defineProps({
  size: {
    type: String,
    required: false,
  },
});

const langOptions = [
  { label: "中文", value: 'zh-CN' },
  { label: "English", value: 'en-US' },
];
const { locale, t } = useI18n();

function handleLanguageChange(lang) {
  locale.value = lang
  Cookies.set('language', lang);
  location.reload();
}
</script>

<style lang="scss" scoped> 
.lang-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #97a8be;
}

</style>