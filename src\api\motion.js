import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/device',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/device/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/device',
    method: 'put',
    data
  })
}

export function getCurrentMotionInfo(params) {
  return request({
    url: 'api/motion/getCurrentMotionInfo',
    method: 'get',
    params
  })
}

export function getMotionInfos() {
  return request({
    url: 'api/motion/getMotionInfos',
    method: 'get'
  })
}



export function changeSpeed(data) {
  return request({
    url: 'api/motion/changeSpeed',
    method: 'post',
    data
  })
}

export function openDoor(data) {
  return request({
    url: 'api/motion/openDoor',
    method: 'post',
    data
  })
}

export default { add, edit, del, getCurrentMotionInfo, getMotionInfos, changeSpeed, openDoor }
