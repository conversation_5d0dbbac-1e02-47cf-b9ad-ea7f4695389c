import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/result',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/result/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/result',
    method: 'put',
    data
  })
}

export function downloadReport(id) {
  return request({
    url: 'api/resultReport/downloadReport/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

export default { add, edit, del, downloadReport }
