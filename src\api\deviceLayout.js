import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/deviceLayout',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/deviceLayout/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/deviceLayout',
    method: 'put',
    data
  })
}

export function getLatest(params) {
  return request({
    url: 'api/deviceLayout/getLatest',
    method: 'get',
    params
  })
}

export function getLatestLayoutStyle(params) {
  return request({
    url: 'api/deviceLayout/getLatestLayoutStyle',
    method: 'get',
    params
  })
}

export function findAllDevicesInLatestLayout(params) {
  return request({
    url: 'api/deviceLayout/findAllDevicesInLatestLayout',
    method: 'get',
    params
  })
}

export function query(params) {
  return request({
    url: 'api/deviceLayout',
    method: 'get',
    params
  })
}

export function queryNoneProxyCmdForSection(params) {
  return request({
    url: 'api/deviceInstanceCmd/queryNoneProxyCmdForSection',
    method: 'get',
    params
  })
}

export function resetAllPositions(data) {
  return request({
    url: 'api/deviceLayout/resetAllPositions',
    method: 'put',
    data
  })
}

export default { add, edit, del, findAllDevicesInLatestLayout, queryNoneProxyCmdForSection, resetAllPositions, getLatestLayoutStyle }
