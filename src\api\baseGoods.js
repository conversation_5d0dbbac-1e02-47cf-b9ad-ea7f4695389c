import request from '@/utils/request'

export function getBaseGoods(params) {
  return request({
    url: 'api/baseGoods',
    method: 'get',
    params
  })
}

export function qryBaseGoodsByGoodsName(params) {
  return request({
    url: 'api/baseGoods/qryBaseGoodsByGoodsName',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/baseGoods',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/baseGoods/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/baseGoods',
    method: 'put',
    data
  })
}

export default { add, edit, del, getBaseGoods, qryBaseGoodsByGoodsName }
