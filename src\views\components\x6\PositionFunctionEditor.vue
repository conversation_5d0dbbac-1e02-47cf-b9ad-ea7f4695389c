<template>
  <span @click="dialogVisible=true">
    {{ content }}
    <el-dialog 
      :append-to-body="true"
      align-center
      v-model="dialogVisible"
      :close-on-click-modal="false"
      title="设备实例选择"
      width="600"
      height="400"
      @opened="initFormData"
    >
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="设备实例" prop="deviceInstanceName">
          <DeviceInstanceInput v-model:deviceInstance="form"  @blur="onDevInstanceBlur" />
        </el-form-item>
        <el-form-item label="点位名称">
          <el-select v-model="form.name" clearable size="small" placeholder="类型">
            <el-option v-for="item in form.posNames" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="点位状态">
          <el-select v-model="form.status" clearable size="small" placeholder="请选择">
            <el-option v-for="item in dict.data.position_status" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="绑定点位">
          <el-switch v-model="form.bindPos" />
        </el-form-item>
        <el-form-item v-if="form.bindPos" label="被绑定设备实例" prop="bindDeviceInstanceName">
          <DeviceInstanceInput v-model:deviceInstance="bindDeviceForm"  @blur="onBindDevInstanceBlur" />
        </el-form-item>
        <el-form-item v-if="form.bindPos" label="被绑定点位名称">
          <el-select v-model="bindDeviceForm.positionName" clearable size="small" placeholder="类型">
            <el-option v-for="item in form.bindPosNames" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.bindPos" label="被绑定点位状态">
          <el-select v-model="bindDeviceForm.positionStatus" clearable size="small" placeholder="请选择">
            <el-option v-for="item in dict.data.position_status" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="使用重定向点位">
          <el-switch v-model="form.useRedirectedPos" />
        </el-form-item>
        <el-form-item label="返回编码">
          <el-select v-model="form.returnCode" clearable size="small" placeholder="请选择">
            <el-option label="机器人点位编码" value="ROBOT_CODE" />
            <el-option label="业务编码" value="SERVICE_CODE" />
            <el-option label="重定向编码" value="REDIRECT_CODE" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="text" @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDialog">确认</el-button>
        </div>
      </template>
    </el-dialog>

  </span>
</template>

<script setup>
import { ref, reactive, watch, onMounted, inject } from 'vue'
import { Graph } from '@antv/x6'
import { Selection } from '@antv/x6-plugin-selection'
import { register } from '@antv/x6-vue-shape'
import { Search } from '@element-plus/icons-vue'
import { getLatest } from '@/api/deviceLayout'
import DeviceNode from '@/views/devicelayout/DeviceNode.vue'
import { useDict } from '@/hooks/useDict'

import DeviceInstanceInput from './DeviceInstanceInput.vue'

// 注册设备节点组件
register({
  shape: 'device-node',
  width: 212,
  height: 48,
  component: DeviceNode
})

const content = defineModel("content", {
  type: String,
  required: true,
  default: "",
});

// Props
const props = defineProps({
  curNode: {
    type: Object,
    default: () => ({})
  }
})


// 使用字典
const { dict } = useDict(['position_status'])


// 注入
const getDataStore = inject('getDataStore')

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  deviceInstanceName: null,
  deviceInstanceId: null,
  status: null,
  bindPos: false,
  bindDeviceInstanceId: null,
  bindDeviceInstanceName: null,
  bindPositionName: null,
  bindPositionStatus: null,
  useRedirectedPos: false,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

const bindDeviceForm = reactive({  deviceInstanceName: null,
  deviceInstanceId: null,
  positionName: null,
  positionStatus: null,
})


const emit = defineEmits(["blur"]);


const dialogVisible = ref(false)
const graph = ref(null)
const selectedNode = ref(null)
const layoutDataStore = reactive({})
const dataStore = reactive({})
const initing = ref(false)
const formRef = ref(null)
const mainBoardContainerRef = ref(null)


const initFormData = () => {

  // 解析 content 字符串
  if (content.value && content.value.startsWith('${GET_POSITION_CODE(')) {
    try {
      // 提取括号内的内容
      const contentStr = content.value
      const paramStr = contentStr.substring(contentStr.indexOf('(') + 1, contentStr.lastIndexOf(')'))
      
      // 分割参数
      const params = []
      let currentParam = ''
      let inQuote = false
      
      for (let i = 0; i < paramStr.length; i++) {
        const char = paramStr[i]
        
        if (char === ',' && !inQuote) {
          params.push(currentParam.trim())
          currentParam = ''
        } else if (char === "'" || char === '"') {
          inQuote = !inQuote
          if (!inQuote) {
            // 引号结束，不添加引号
          } else {
            // 引号开始，不添加引号
          }
        } else {
          currentParam += char
        }
      }
      
      // 添加最后一个参数
      if (currentParam) {
        params.push(currentParam.trim())
      }
      
      // 根据参数数量判断格式
      if (params.length >= 4) {
        // 设置表单值
        form.deviceInstanceId = params[0].replace(/['"]/g, '')
        form.deviceInstanceName = params[1].replace(/['"]/g, '')
        form.name = params[2].replace(/['"]/g, '')
        form.status = params[3].replace(/['"]/g, '')
        form.bindPos = false
        const statusArr = form.status.split('@')
        if (statusArr.length==2) {
          form.status = statusArr[0]
          form.bindPos = true
          const bindPosArr = statusArr[1].split('$')
          bindDeviceForm.deviceInstanceId = bindPosArr[0].replace(/['"]/g, '')
          bindDeviceForm.deviceInstanceName = bindPosArr[1].replace(/['"]/g, '')
          bindDeviceForm.positionName = bindPosArr[2].replace(/['"]/g, '')
          bindDeviceForm.positionStatus = bindPosArr[3].replace(/['"]/g, '')
        }

        if (params.length >= 5) {
          const options = JSON.parse( params[4].replace(/['"]/g, '') )
          form.useRedirectedPos = options.useRedirectedPos
          form.returnCode = options.returnCode
        }
      
      }
    } catch (error) {
      console.error('解析 content 字符串失败:', error)
    }
  }
}

const transformNodeToFormData = () => {

  dataStore.value = getDataStore()
  const cmdData = getStoreObjectByNodeId(props.curNode.id)
  if (cmdData?.parameter) {
    const paramObj = JSON.parse(cmdData.parameter)
    Object.assign(form, paramObj)
  }
}

const transformFormDataToNode = () => {
  const cmdData = getStoreObjectByNodeId(props.curNode.id)
  if (cmdData) {
    cmdData.parameter = JSON.stringify(form)
  }
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.value?.dagNodeId === nodeId) {
    return dataStore.value
  }
  
  for (const actionObj of dataStore.value?.actions || []) {
    if (actionObj.dagNodeId === nodeId) {
      return actionObj
    }
    for (const cmdObj of actionObj.commands || []) {
      if (cmdObj.dagNodeId === nodeId) {
        return cmdObj
      }
    }
  }
  return null
}

const getLayoutStoreObjectByNodeId = (nodeId) => {
  return layoutDataStore.deviceInstanceList?.find(
    devInsObj => devInsObj.layoutNodeId === nodeId
  )
}

const onDevInstanceSelect = (form) => {
 // dialogVisible.value = true
}

const onDevInstanceBlur = (devInst) => {
  const selectedPositions = devInst.positions
  const nameArr = [...new Set(selectedPositions.map(pos => pos.name))]

  form.posNames = nameArr
  if (nameArr.length > 0) {
    form.name = nameArr[0]
  }
  form.deviceInstanceName = devInst.name
  form.deviceInstanceId = devInst.id
}

const onBindDevInstanceBlur = (devInst) => {
  const selectedPositions = devInst.positions
  const nameArr = [...new Set(selectedPositions.map(pos => pos.name))]

  form.bindPosNames = nameArr
  if (nameArr.length > 0) {
    bindDeviceForm.positionName = nameArr[0]
  }
  bindDeviceForm.deviceInstanceName = devInst.name
  bindDeviceForm.deviceInstanceId = devInst.id
}

const submitDialog = () => {
  let contentTmp =  '${GET_POSITION_CODE('+form.deviceInstanceId+',\''+form.deviceInstanceName+'\',\''+form.name+'\',\''+form.status
  if (form.bindPos) {
    contentTmp += '@'+bindDeviceForm.deviceInstanceId+'$'+bindDeviceForm.deviceInstanceName+'$'+bindDeviceForm.positionName+'$'+bindDeviceForm.positionStatus
  }
  contentTmp += '\''
  const opt = {useRedirectedPos:form.useRedirectedPos, returnCode:form.returnCode}
  contentTmp += ',\'' + JSON.stringify(opt) + '\''
  
  /*
  if (form.useRedirectedPos) {
    contentTmp += ',\'{useRedirectedPos:true}\''
  }else if (form.returnCode) {
    contentTmp += ',\'{returnCode:\"'+form.returnCode+'\"}\''
  }
  */
  content.value = contentTmp + ')}'
  debugger
  dialogVisible.value = false
  emit("blur", content.value );
}

// 监听器
watch(() => dataStore.value, transformNodeToFormData, { deep: true })
watch(() => props.curNode, transformNodeToFormData)
watch(() => form, transformFormDataToNode, { deep: true })

// 生命周期钩子
onMounted(async () => {
  transformNodeToFormData()
  try {
    const res = await getLatest(null)
    Object.assign(layoutDataStore, res || {})
  } catch (error) {
    console.error('Failed to get latest layout:', error)
  }
})
</script>

<style lang="scss">
.dev-graph-container {
  width: 800px;
  height: 500px;
  
  #main_board_container {
    height: 100%;
    background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
    background-size: cover;
  }
}
</style>