<template>
  <div style="margin-bottom: 18px;width:100%;">

    <el-table
      ref="tableRef"
      :data="form.predicatesArr"
      size="small"
      cell-class-name="predicate-table-cell"
      max-height="450"
      class="predicate-table"
    >
      <el-table-column prop="operator" label="连接符" width="80">
        <template #default="scope">
          <el-select v-model="scope.row.joinOperator" placeholder="请选择" size="small">
            <el-option label="与" value="and" />
            <el-option label="或" value="or" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="检测指标" min-width="100">
        <template #default="scope">
          <el-select v-model="scope.row.name" placeholder="请选择" class="input-with-select">
            <el-option v-for="item in dict.data.to_check_rule_names" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="compareOperator" label="比较符" width="80">
        <template #default="scope">
          <el-select v-model="scope.row.compareOperator" placeholder="请选择" size="small">
            <el-option label="等于" value="eq" />
            <el-option label="不等" value="ne" />
            <el-option label="大于" value="gt" />
            <el-option label="大于等于" value="gte" />
            <el-option label="小于" value="lt" />
            <el-option label="小于等于" value="lte" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="targetValue" label="值" width="60">
        <template #default="scope">
          <el-input v-model="scope.row.targetValue" />
        </template>
      </el-table-column>
      <el-table-column v-if="checkPer(['admin', 'result:edit', 'result:del'])" prop="operation" fixed="right" label="操作" width="70">
        <template #header>
          <div>
            <span>操作</span>
            <el-button :icon="Plus" class="operation" @click="addCondition" />
          </div>
        </template>
        <template #default="scope">
          <div class="operation-container">
            <el-button :icon="Delete" class="operation" @click="deleteCondition(scope.$index)" />
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, inject, defineModel } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'

import { useDict } from '@/hooks/useDict'

// 定义v-model
const modelValue = defineModel({
  type: String,
  default: ''
})




// 权限检查
const checkPer = inject('checkPer')

// 使用字典
const { dict } = useDict(['failed_then_enum', 'to_check_rule_names'])

// 响应式数据
const defaultForm = { 
  id: null, 
  name: null, 
  description: null, 
  asyncMode: null, 
  variablesArr: [], 
  predicatesArr: [], 
  deleteFlag: null, 
  createBy: null, 
  createTime: null, 
  updateBy: null, 
  updateTime: null 
}

const form = reactive({ ...defaultForm })
const tableRef = ref(null)

// 监听器
watch(modelValue,() =>{
 parseModelValue()
})

const parseModelValue = () => {
  if (!modelValue.value) return

  form.predicatesArr = JSON.parse(modelValue.value)
}


watch(() => form.predicatesArr, (newVal, oldVal) => {
  modelValue.value = JSON.stringify(form.predicatesArr)
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  parseModelValue()
})

function addCondition() {
  const newData = { 
    joinOperator: 'and', 
    name: null, 
    sourceType: null, 
    sourceParameter: null, 
    compareOperator: 'eq', 
    targetValue: null 
  }
  form.predicatesArr.push(newData)
}

function deleteCondition(rowIndex) {
  form.predicatesArr.splice(rowIndex, 1)
}

</script>

<style lang="scss">
.operation {
  width: 18px;
  height: 18px;
  padding: 2px 2px;
  margin-left: 4px;
  margin-bottom: 4px;
}

.operation-container {
  display: flex;
}

.el-table .predicate-table-cell .cell {
  padding-left: 2px;
  padding-right: 2px;
}

.el-table .predicate-table-cell .cell input {
  padding-left: 2px;
  padding-right: 2px;
}

.el-table .predicate-table-cell .cell .input-pos input {
  width: 56px;
}

.el-table .predicate-table-cell .cell .input-name {
  padding-right: 4px;
}

.predicate-table :deep(.el-input__inner) {
  padding-left: 2px;
  padding-right: 2px;
}

.predicate-table :deep(.el-button) {
  padding-left: 2px;
  padding-right: 2px;
}

.predicate-table :deep(.el-input-group__append) {
  padding-right: 2px;
}
</style>
