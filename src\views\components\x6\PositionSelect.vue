<template>
  <div>
    <!--表单组件-->
    <el-dialog 
      v-model="dialogVisible"
      :close-on-click-modal="false"
      title="设备实例选择"
      width="840px"
      height="500px"
      @opened="openDialog"
    >
      <div class="dev-graph-container">
        <div id="main_board_container" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="text" @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPosSelection">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="设备实例" prop="deviceInstanceName">
        <el-input v-model="form.deviceInstanceName" placeholder="请输入内容" class="input-with-select">
          <template #append>
            <el-button :icon="Search" @click="dialogVisible = true" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="点位名称">
        <el-select v-model="form.name" clearable size="small" placeholder="类型">
          <el-option v-for="item in form.posNames" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, inject } from 'vue'
import { Graph } from '@antv/x6'
import { Selection } from '@antv/x6-plugin-selection'
import { register } from '@antv/x6-vue-shape'
import { Search } from '@element-plus/icons-vue'
import { getLatest } from '@/api/deviceLayout'
import DeviceNode from '@/views/devicelayout/DeviceNode.vue'

// 注册设备节点组件
register({
  shape: 'device-node',
  width: 212,
  height: 48,
  component: DeviceNode
})

// Props
const props = defineProps({
  curNode: {
    type: Object,
    default: () => ({})
  }
})

// 注入
const getDataStore = inject('getDataStore')

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  deviceInstanceName: null,
  deviceInstanceId: null,
  status: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

const dialogVisible = ref(false)
const graph = ref(null)
const selectedNode = ref(null)
const layoutDataStore = reactive({})
const dataStore = reactive({})
const initing = ref(false)
const formRef = ref(null)

// 方法
const initGraph = () => {
  if (initing.value) return
  initing.value = true

  const container = document.getElementById('main_board_container')
  console.log('container dimensions:', {
    width: container.offsetWidth,
    height: container.offsetHeight,
    clientHeight: container.clientHeight
  })

  graph.value = new Graph({
    container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    autoResize: true,
    background: false,
    snapline: false,
    interacting: false,
    connecting: {
      snap: true,
      allowBlank: false,
      allowMulti: true,
      allowLoop: true,
      highlight: true,
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#5F95FF',
              stroke: '#5F95FF'
            }
          }
        }
      },
      router: {
        name: 'orth'
      },
      connector: {
        name: 'rounded',
        args: {
          radius: 8
        }
      }
    },
    panning: {
      enabled: true
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    }
  })

  graph.value.use(new Selection({
    rubberband: true,
    showNodeSelectionBox: true,
    multiple: false
  }))

  graph.value.fromJSON(JSON.parse(layoutDataStore.layoutSvg))

  // 隐藏无点位设备实例
  graph.value.getNodes().forEach(node => {
    const storeData = getLayoutStoreObjectByNodeId(node.id)
    if (storeData?.positions?.length > 0) {
      node.show()
    } else {
      node.hide()
    }
  })

  graph.value.zoomToFit({ padding: 10, minScale: 0.8, maxScale: 4 })
  graph.value.centerContent()

  // 事件监听
  graph.value.on('cell:selected', ({ cell }) => {
    if (cell.isNode()) {
      selectedNode.value = cell
    }
  })

  graph.value.on('cell:unselected', () => {
    selectedNode.value = null
  })
}

const transformNodeToFormData = () => {
  dataStore.value = getDataStore()
  const cmdData = getStoreObjectByNodeId(props.curNode.id)
  if (cmdData?.parameter) {
    const paramObj = JSON.parse(cmdData.parameter)
    Object.assign(form, paramObj)
  }
}

const transformFormDataToNode = () => {
  const cmdData = getStoreObjectByNodeId(props.curNode.id)
  if (cmdData) {
    cmdData.parameter = JSON.stringify(form)
  }
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.value?.dagNodeId === nodeId) {
    return dataStore.value
  }
  
  for (const actionObj of dataStore.value?.actions || []) {
    if (actionObj.dagNodeId === nodeId) {
      return actionObj
    }
    for (const cmdObj of actionObj.commands || []) {
      if (cmdObj.dagNodeId === nodeId) {
        return cmdObj
      }
    }
  }
  return null
}

const getLayoutStoreObjectByNodeId = (nodeId) => {
  return layoutDataStore.deviceInstanceList?.find(
    devInsObj => devInsObj.layoutNodeId === nodeId
  )
}

const openDialog = () => {
  initGraph()
}

const submitPosSelection = () => {
  if (!selectedNode.value) return

  const devInst = getLayoutStoreObjectByNodeId(selectedNode.value.id)
  const selectedPositions = devInst.positions
  const nameArr = [...new Set(selectedPositions.map(pos => pos.name))]

  form.posNames = nameArr
  if (nameArr.length > 0) {
    form.name = nameArr[0]
  }
  form.deviceInstanceName = devInst.name
  form.deviceInstanceId = devInst.id

  dialogVisible.value = false
}

// 监听器
watch(() => dataStore.value, transformNodeToFormData, { deep: true })
watch(() => props.curNode, transformNodeToFormData)
watch(() => form, transformFormDataToNode, { deep: true })

// 生命周期钩子
onMounted(async () => {
  transformNodeToFormData()
  try {
    const res = await getLatest(null)
    Object.assign(layoutDataStore, res || {})
  } catch (error) {
    console.error('Failed to get latest layout:', error)
  }
})
</script>

<style lang="scss">
.dev-graph-container {
  width: 800px;
  height: 500px;
  
  #main_board_container {
    height: 100%;
    background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
    background-size: cover;
  }
}
</style>