<template>
  <div class="status-board-container">
    
    <div class="button-wrapper" v-for=" m in topMaterials">
      <el-tooltip placement="top">
        <template #content>
          {{ m.name }}<br>{{ m.remainValue }}/{{ m.totalValue }}<br>{{ m.status }}
        </template>
        <el-badge 
              :value=" Math.round(m.remainValue/m.totalValue*100)+'%'" 
              type="info"
              :offset="getBadgeOffset(m)"
              :badge-class="'status-badge status-badge-'+ m.status"
            >
            <PositionDetailConfigPopover :deviceInstanceId="m.deviceInstanceId" :name="m.name" placement="top">
            
            <template #reference>
        <div class="background-container">
          <el-image
            :src=" imageAccessUrl + m.image "
            fit="contain"
            class="img"
          />
        </div>
        </template>
            </PositionDetailConfigPopover>
        </el-badge>
      </el-tooltip>
      <div class="status-text"><span>{{ m.name }}</span></div>
    </div>
    
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import crudMaterial from '@/api/material'
import PositionDetailConfigPopover from '@/views/material/components/PositionDetailConfigPopover.vue'

const store = useStore()

const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

const topMaterials = reactive([])
// 添加定时器引用
const intervalId = ref(null)

const getBadgeOffset = (m) => {
  const value = Math.round(m.remainValue/m.totalValue*100) + '%'
  let offsetX = 0
  
  if (value.length >= 4) {
    offsetX = -36
  } else if (value.length >= 3) {
    offsetX = -29
  } else if (value.length >=2 ) {
    offsetX = -22
  } else {
    offsetX = -10
  }
  return [offsetX, 10];
}

const init = async () => {
  const res = await crudMaterial.queryTopByStatus({topCount:6})
  Object.assign(topMaterials, res)
  
}

const refreshData = async () => {
  const res = await crudMaterial.queryTopByStatus({topCount:6})
  
  for (let i = 0; i < res.length; i++) {
    if (topMaterials[i].id == res[i].id) {
      Object.assign(topMaterials[i], res[i])
    }
  }
  //Object.assign(topMaterials, res)
  
}

onMounted(()=>{
  init()
  // 设置定时器，每5秒获取一次数据
  intervalId.value = setInterval(() => {
    refreshData()
  }, 5000)

} )

onBeforeUnmount(() => {
  // 清除定时器
  if (intervalId.value) {
    clearInterval(intervalId.value)
    intervalId.value = null
  }
})

</script>

<style lang="scss" scoped>
    .status-board-container {
      display: flex;
      justify-content: flex-start; /* 水平居中 */
      align-items: center; /* 垂直居中 */
      flex-wrap: wrap; /* 换行布局 */
      gap: 14px; /* 元素之间的间距 */
      text-align: center; /* 文本居中对齐 */
    }

    .button-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center; /* 水平居中 */
      gap: 10px; /* 按钮和文本之间的间距 */
    }

    .background-container {
      width: 80px; /* 宽度 */
      height: 80px; /* 高度 */
      border-radius: 8px; /* 圆形 */
      background-size: cover; /* 背景图片适应按钮 */
      background-position: center; /* 背景图片居中 */
      background-color: #336c9b7d;
      border: 2px solid #59AED2; /* 边框颜色和样式 */
      display: flex;
      align-items: center; /* 内部内容垂直居中 */
      justify-content: center; /* 内部内容水平居中 */
      cursor: pointer; /* 鼠标移到按钮上变为手指 */
    }
    .background-container:hover {
      background-image:none;
    }

    .status-text {
      font-size: 14px;
      font-weight:200;
      color:#FFFFFF;
    }
    .img{
      width:60px;
      height:60px;
    }

    .el-select-dropdown {
    position: fixed !important;
    z-index: 9999 !important;
    top: auto !important;
    left: auto !important;
  }
  :deep(.status-badge) {
    border-radius: 0px 8px 0px 8px !important;
    border: 1px solid #59AED2; /* 边框颜色和样式 */
  }
  :deep(.status-badge-NORMAL) {
    background-color: #59aed274 !important; /* 添加半透明黑色背景 */
  }  
  :deep(.status-badge-WARN) {
    background-color: #d2905974 !important; /* 添加半透明黑色背景 */
  }  
  :deep(.status-badge-ERROR) {
    background-color: #d2595974 !important; /* 添加半透明黑色背景 */
  }    
</style>
