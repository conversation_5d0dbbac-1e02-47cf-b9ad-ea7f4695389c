# updateLineChart 函数重写文档

## 概述

`updateLineChart` 函数已重写，现在可以从API获取真实数据并支持同时显示多个序列。函数根据注释中的数据格式要求实现了完整的多序列折线图功能。

## 🔄 主要改进

### 1. 真实数据获取
- ✅ 从 `crudStatsResultFeature.queryStatsResultFeatureValues()` API 获取数据
- ✅ 合并全局筛选条件和图表级筛选条件
- ✅ 完整的错误处理和数据验证

### 2. 多序列支持
- ✅ 支持同时显示多个检测指标序列
- ✅ 序列名称来自 `serialsMap` 的 Key 值
- ✅ 自动颜色分配，每个序列使用不同颜色
- ✅ 响应式图例，支持序列显示/隐藏

### 3. 数据格式处理
- ✅ 严格按照注释要求的数据格式处理
- ✅ 时间轴数据格式：`['2023-01-01 00:12:11', '2023-01-02 01:11:34', ...]`
- ✅ 序列数据格式：`{ 'pH值': [1, 2, 3, ...], '溶解氧': [1, 2, 3, ...], ... }`

## 📊 数据格式

### API 输入参数
```javascript
const queryParams = {
  ...globalFilters,        // 全局筛选条件
  detectionItem: string    // 图表级筛选的检测物
}
```

### API 返回格式
```javascript
{
  xAxisData: [
    '2023-01-01 00:12:11',
    '2023-01-02 01:11:34',
    // ... 更多时间点
  ],
  serialsMap: {
    'pH值': [7.2, 7.1, 7.3, ...],
    '溶解氧': [8.5, 8.3, 8.7, ...],
    '浊度': [1.2, 1.5, 1.1, ...],
    '温度': [22.5, 23.1, 24.2, ...]
  }
}
```

## 🎨 功能特性

### 1. 多序列显示
```javascript
// 自动为每个序列分配颜色
const colors = [
  '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', 
  '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'
]

// 生成多个序列配置
seriesNames.forEach((seriesName, index) => {
  seriesData.push({
    name: seriesName,
    type: 'line',
    data: serialsMap[seriesName],
    lineStyle: { color: colors[index % colors.length] }
  })
})
```

### 2. 交互式功能
- **工具提示**: 鼠标悬停显示详细数值
- **图例控制**: 点击图例显示/隐藏序列
- **数据缩放**: 支持鼠标滚轮和拖拽缩放
- **十字准线**: 精确查看数据点

### 3. 时间轴优化
```javascript
// 时间格式化显示
axisLabel: {
  formatter: (value) => {
    // 只显示日期部分，避免标签过长
    if (value.includes(' ')) {
      return value.split(' ')[0]
    }
    return value
  },
  rotate: 45  // 旋转45度避免重叠
}
```

### 4. 状态处理

#### 空数据状态
```javascript
if (!xAxisData.length || !Object.keys(serialsMap).length) {
  // 显示"暂无数据"提示
  const emptyOption = {
    graphic: {
      type: 'text',
      text: '暂无数据',
      style: { fontSize: 16, fill: '#999' }
    }
  }
}
```

#### 错误状态
```javascript
catch (error) {
  // 显示"数据加载失败"提示
  const errorOption = {
    graphic: {
      type: 'text',
      text: '数据加载失败',
      style: { fontSize: 16, fill: '#f56c6c' }
    }
  }
}
```

## 🔧 配置选项

### ECharts 配置结构
```javascript
const option = {
  title: {
    text: '检测值趋势图',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross' }
  },
  legend: {
    top: 30,
    type: 'scroll',
    data: seriesNames
  },
  grid: {
    left: '3%', right: '4%',
    bottom: '8%', top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: xAxisData
  },
  yAxis: {
    type: 'value',
    scale: true,
    name: '检测值'
  },
  dataZoom: [
    { type: 'inside' },
    { height: 30, bottom: 20 }
  ],
  series: seriesData
}
```

## 📱 响应式设计

- **自适应布局**: 图表自动适应容器大小
- **滚动图例**: 序列过多时图例支持滚动
- **数据缩放**: 内置缩放控件，支持大数据量显示

## 🧪 测试用例

### 1. 正常数据测试
```javascript
const normalData = {
  xAxisData: ['2023-01-01 08:30:15', '2023-01-01 10:15:22'],
  serialsMap: {
    'pH值': [7.2, 7.1],
    '溶解氧': [8.5, 8.3]
  }
}
```

### 2. 空数据测试
```javascript
const emptyData = {
  xAxisData: [],
  serialsMap: {}
}
```

### 3. 错误数据测试
```javascript
const invalidData = null // 或格式错误的数据
```

## 🔄 调用方式

### 手动调用
```javascript
await updateLineChart()
```

### 筛选条件变化时自动调用
```javascript
// 在筛选控件的 @change 事件中调用
<el-select @change="updateLineChart">
```

### 全局刷新时调用
```javascript
const updateAllCharts = () => {
  updateLineChart()
  updateBarChart()
  updatePieChart()
  updateHeatmapChart()
}
```

## 📈 性能优化

1. **数据验证**: 在处理前验证数据格式
2. **错误处理**: 完整的 try-catch 错误捕获
3. **内存管理**: 使用 `setOption(option, true)` 清理旧配置
4. **异步处理**: 使用 async/await 避免阻塞

## 🔮 未来扩展

- [ ] 支持实时数据更新
- [ ] 添加数据导出功能
- [ ] 支持更多图表类型切换
- [ ] 添加数据预警阈值线
- [ ] 支持自定义颜色主题

## 📝 注意事项

1. **数据一致性**: 确保 `xAxisData` 和每个序列的数据长度一致
2. **API 依赖**: 需要确保 `crudStatsResultFeature.queryStatsResultFeatureValues` API 可用
3. **错误处理**: 建议在调用前检查图表实例是否已初始化
4. **性能考虑**: 大数据量时建议启用数据采样或分页加载
