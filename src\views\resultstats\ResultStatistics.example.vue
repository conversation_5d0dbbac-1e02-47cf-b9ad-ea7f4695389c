<template>
  <div class="statistics-example">
    <h2>检测结果统计示例</h2>
    
    <!-- 导航标签 -->
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="统计面板" name="statistics">
        <ResultStatistics />
      </el-tab-pane>
      
      <el-tab-pane label="使用说明" name="usage">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>ResultStatistics 组件使用说明</span>
            </div>
          </template>
          
          <div class="usage-content">
            <h3>功能特性</h3>
            <ul>
              <li>📊 <strong>多种图表类型</strong>：折线图、柱状图、饼图、热力图</li>
              <li>🔍 <strong>数据筛选</strong>：支持时间范围、检测方法、样品类型筛选</li>
              <li>🔍 <strong>图表放大</strong>：支持单个图表全屏显示</li>
              <li>📱 <strong>响应式布局</strong>：自适应不同屏幕尺寸</li>
              <li>⚡ <strong>实时更新</strong>：支持数据实时刷新</li>
            </ul>
            
            <h3>图表说明</h3>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="检测值趋势图">
                显示特定检测物的数值变化趋势，支持选择不同检测物（pH值、溶解氧、浊度、温度等）
              </el-descriptions-item>
              <el-descriptions-item label="检测方法统计">
                统计不同检测方法的使用频率和异常次数，支持切换统计类型
              </el-descriptions-item>
              <el-descriptions-item label="检测结论分布">
                以饼图形式展示检测结论的分布情况（正常、轻微异常、严重异常、需复检）
              </el-descriptions-item>
              <el-descriptions-item label="样品检测热力图">
                展示不同时间段的检测活动热度，支持按小时、按天、按周三种时间粒度
              </el-descriptions-item>
            </el-descriptions>
            
            <h3>使用方法</h3>
            <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;ResultStatistics /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import ResultStatistics from './ResultStatistics.vue'
&lt;/script&gt;</code></pre>
            
            <h3>自定义配置</h3>
            <p>组件支持以下自定义配置：</p>
            <ul>
              <li><strong>全局筛选</strong>：时间范围、检测方法、样品类型</li>
              <li><strong>图表筛选</strong>：每个图表都有独立的筛选控件</li>
              <li><strong>图表放大</strong>：点击放大图标可全屏显示单个图表</li>
              <li><strong>数据刷新</strong>：支持手动刷新所有图表数据</li>
            </ul>
            
            <h3>技术栈</h3>
            <el-tag type="primary" class="tech-tag">Vue 3</el-tag>
            <el-tag type="success" class="tech-tag">Element Plus</el-tag>
            <el-tag type="warning" class="tech-tag">ECharts</el-tag>
            <el-tag type="info" class="tech-tag">SCSS</el-tag>
          </div>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="API文档" name="api">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>API 文档</span>
            </div>
          </template>
          
          <div class="api-content">
            <h3>Props</h3>
            <el-table :data="propsData" border>
              <el-table-column prop="name" label="参数名" width="150" />
              <el-table-column prop="type" label="类型" width="100" />
              <el-table-column prop="default" label="默认值" width="100" />
              <el-table-column prop="description" label="说明" />
            </el-table>
            
            <h3>Events</h3>
            <el-table :data="eventsData" border>
              <el-table-column prop="name" label="事件名" width="150" />
              <el-table-column prop="params" label="参数" width="200" />
              <el-table-column prop="description" label="说明" />
            </el-table>
            
            <h3>Methods</h3>
            <el-table :data="methodsData" border>
              <el-table-column prop="name" label="方法名" width="150" />
              <el-table-column prop="params" label="参数" width="200" />
              <el-table-column prop="description" label="说明" />
            </el-table>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ResultStatistics from './index.vue'

const activeTab = ref('statistics')

// API文档数据
const propsData = ref([
  {
    name: 'apiUrl',
    type: 'String',
    default: "''",
    description: '数据接口地址，用于获取统计数据'
  },
  {
    name: 'autoRefresh',
    type: 'Boolean',
    default: 'false',
    description: '是否自动刷新数据'
  },
  {
    name: 'refreshInterval',
    type: 'Number',
    default: '30000',
    description: '自动刷新间隔时间（毫秒）'
  },
  {
    name: 'defaultFilters',
    type: 'Object',
    default: '{}',
    description: '默认筛选条件'
  }
])

const eventsData = ref([
  {
    name: 'filter-change',
    params: '(filters: Object)',
    description: '筛选条件变化时触发'
  },
  {
    name: 'chart-expand',
    params: '(chartType: String)',
    description: '图表放大时触发'
  },
  {
    name: 'data-refresh',
    params: '()',
    description: '数据刷新时触发'
  }
])

const methodsData = ref([
  {
    name: 'refreshData',
    params: '()',
    description: '手动刷新所有图表数据'
  },
  {
    name: 'expandChart',
    params: '(chartType: String)',
    description: '展开指定图表'
  },
  {
    name: 'resetFilters',
    params: '()',
    description: '重置所有筛选条件'
  }
])
</script>

<style lang="scss" scoped>
.statistics-example {
  padding: 20px;
  
  .card-header {
    font-weight: bold;
    color: #303133;
  }
  
  .usage-content {
    h3 {
      color: #409EFF;
      margin: 20px 0 10px 0;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 5px;
    }
    
    ul {
      margin: 10px 0;
      padding-left: 20px;
      
      li {
        margin: 8px 0;
        line-height: 1.6;
      }
    }
    
    pre {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      border-left: 4px solid #409EFF;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.4;
      }
    }
    
    .tech-tag {
      margin: 0 8px 8px 0;
    }
  }
  
  .api-content {
    h3 {
      color: #409EFF;
      margin: 20px 0 15px 0;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 5px;
    }
    
    .el-table {
      margin-bottom: 30px;
    }
  }
}
</style>
