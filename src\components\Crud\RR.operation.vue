<template>
  <span :class="['span-search', itemClass]">
    <!-- 搜索按钮 -->
    <el-button
      size="default"
      type="primary"
      :icon="Search"
      @click="crud.toQuery"
    >
      {{ $t('搜索') }}
    </el-button>

    <!-- 重置按钮 -->
    <el-button
      v-if="crud.optShow.reset"
      size="default"
      type="plain"
      :icon="RefreshLeft"
      @click="crud.resetQuery"
    >
      {{ $t('重置') }}
    </el-button>
  </span>
</template>

<script setup>
import { inject, computed } from 'vue';
import { Search, RefreshLeft } from '@element-plus/icons-vue'

defineOptions({
  name: "SearchReset",
  inheritAttrs: false,
});

// Props 定义
const props = defineProps({
    // 自定义样式类
    itemClass: {
      type: String,
      default: ''
    },
    crud: {
      type: Object,
      default: null
    }
  })

// 获取 crud 对象：优先使用 props 传入的，否则从 inject 获取
const crud = props.crud || inject('crud')
if (!crud) {
  throw new Error('`crud` 未提供，请确保在父组件中通过 provide 注入')
}

</script>

<style  rel="stylesheet/scss" lang="scss" scoped>
.span-search {
  padding-left: 20px;
  .el-button {
    margin-right: 6px;
  }
}
</style>