<template>
  <div class="alert-container">
    <div class="alert-title">
      <div class="title">
        <el-icon><Monitor /></el-icon>
        <span>告警信息</span>
      </div>
    </div>
    <div id="alert-board">
      <div class="alert-header-container">
        <div class="alert-header-column">
          <div class="alert-total-text">{{ stat.FATAL }}</div>
          <div class="alert-fatal-icon" />
          <div class="alert-total-label">严重</div>
        </div>
        <div class="alert-header-column">
          <div class="alert-total-text">{{ stat.ERROR }}</div>
          <div class="alert-error-icon" />
          <div class="alert-total-label">错误</div>
        </div>
        <div class="alert-header-column">
          <div class="alert-total-text">{{ stat.WARN }}</div>
          <div class="alert-warn-icon" />
          <div class="alert-total-label">警告</div>
        </div>
      </div>

      <el-scrollbar 
        ref="alertLogScrollBar" 
        class="log-scrollbar" 
        wrap-style="overflow-x:hidden;" 
        :bar-style="{ backgroundColor: '#409EFF', width: '8px' }"
      >
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          class="log-entry"
        >
          <span 
            class="log-task-tag" 
            :style="getTaskTagStyle(log.alertLevel)" 
          />
          <span 
            class="log-timestamp" 
            :style="getTextStyleByStatus(log.alertLevel)"
          >
            {{ getTruncatedTime(log.createTime) }}
          </span>
          <span 
            class="log-message" 
            :style="getTextStyleByStatus(log.alertLevel)"
          >
            {{ log.message }}
          </span>
          <!-- 节点操作菜单 -->
          <div class="more-action-container" >
            <el-dropdown trigger="hover" @command="onAlertCommand(log)">
                <div>
                  <i class="more-action" />
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>清除告警</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import moment from 'moment'
import crudAlert from '@/api/alert'
import { Monitor } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'

// 常量定义
const defaultStat = { FATAL: 0, ERROR: 0, WARN: 0 }

// 状态定义
const store = useStore()
const chart = ref(null)
const robotSpeed = ref(50)
const logs = ref([])
const stat = ref({...defaultStat})
const alertLogScrollBar = ref(null)

// 计算属性
const imagesUploadApi = computed(() => store.getters.imagesUploadApi)
const baseApi = computed(() => store.getters.baseApi)

// 方法定义
const initData = () => {
  crudAlert.findAlertStats().then(res => {
    stat.value = { ...defaultStat }
    if (res?.alertStatItemList) {
      for (const s of res.alertStatItemList) {
        stat.value[s.name] = s.value
      }
    }
    if (res?.alertLogList) {
      logs.value = res.alertLogList
    }

  //  setTimeout(initData, 3000)
  })
}

const getTruncatedTime = (tm) => {
  const strTm = moment(tm).format('YYYY-MM-DD HH:mm:ss')
  return strTm.substring(5)
}

const getTextStyleByStatus = (alertLevel) => {
  if (['FATAL', 'ERROR', 'WARN'].includes(alertLevel)) {
    return 'color: #000000'
  }
}

const getTaskTagStyle = (alertLevel) => {
  const styles = {
    FATAL: '#ce2f2f',
    ERROR: '#de6e69',
    WARN: 'rgb(241 202 105)'
  }
  return alertLevel ? `backgroundColor: ${styles[alertLevel]}` : ''
}

const onAlertCommand = async (log) => {
  log.fixType = 'MANUAL'
  await crudAlert.clearAlertLog(log)

  ElNotification.success({title:'告警已清除'})
  initData()
}

let timer = null
// 生命周期钩子
onMounted(() => {
  timer = setInterval( initData, 3000)
})

onBeforeUnmount(() => {
  if (timer){
    clearInterval(timer)
    timer = null
  }
})
</script>


<style rel="stylesheet/scss" lang="scss" scoped>

  .flow {
   /* width: 100vw; */
    width: 100%;
    height: 100vh;
  }

  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  .control-board-container {
    width: 290px;
    height: 100%;
    /*position: relative;*/
    z-index: 2000;
    border: 1px solid #238DBD;
    box-sizing: border-box;
            background-color: #158CBE40; /* 半透明底色，蓝色 */
            border-radius: 15px; /* 圆角矩形 */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* 边框阴影 */
            padding: 20px; /* 内边距 */
            color: white; /* 文本颜色 */
            margin: 60px 20px 20px 10px;
  }

  .alert-header-container {
    display: flex;
    margin-bottom: 4px;

    justify-content: space-evenly;
    .alert-header-column {

      .alert-total-text {
        margin-top: 10px;
        margin-bottom: 6px;
        text-align: center;
      }

      .alert-fatal-icon {
        width: 60px;
        height: 10px;
        background-color: #ce2f2f; /* 填充红色 */
        border-radius: 50%; /* 将矩形变为椭圆 */
        box-shadow: 0px 5px 8px rgba(0, 0, 0, 0.5); /* 添加阴影 */
      }

      .alert-error-icon {
        width: 60px;
        height: 10px;
        background-color: #de6e69; /* 填充红色 */
        border-radius: 50%; /* 将矩形变为椭圆 */
        box-shadow: 0px 5px 8px rgba(0, 0, 0, 0.5); /* 添加阴影 */
      }
      .alert-warn-icon {
        width: 60px;
        height: 10px;
        background-color: rgb(241 202 105); /* 填充红色 */
        border-radius: 50%; /* 将矩形变为椭圆 */
        box-shadow: 0px 5px 8px rgba(0, 0, 0, 0.5); /* 添加阴影 */
      }

      .alert-total-label {
        margin-top: 10px;
        text-align: center;
        font-size: small;
      }
    }
  }

  .inner-container {
    background-color: #127FAC42;
    margin-bottom: 10px;
    .title {
      padding:10px;
    }
  }

  .alert-title {
    color: white;
    
    .title {
      font-size: 13px;
      font-weight: bold;
      align-items: center;
      justify-content: start;
      display: flex;
    }
    .el-icon {
      margin-right: 4px;
    }
  }

  #alert-board {
    width:100%;
    height: calc(100% - 100px);
  }
  .alert-container {
    height:100%;
  }

.log-scrollbar {
  flex: 1;
  height: 100%;
/*  height: calc(100%-600px);

  min-height: 100px;
  max-height: calc(100%-600px); */
}


.log-task-tag {
  width:8px;
  height:18px;
  display: inline-block;
  background-color: chocolate;
  border-radius: 4px;
  margin-right: 4px;
}
.log-timestamp {
  color: #535559;
  margin-right: 10px;
}

.log-message {
  color: #303133;
  flex: 1;
}

.log-entry:hover {
  background-color: #f5f7fa;
}
.alert-container :deep( .log-entry ) {
  flex: 1;
  padding: 5px 0;
  border-bottom: 1px solid #5993bd;
  white-space: pre-wrap;
  transition: background-color 0.3s;
  font-size: 12px;
  display: flex;
  align-items: center;
}


.more-action-container {
    margin-left: 12px;
    width: 15px;
    height: 100%;
    text-align: center;
    cursor: pointer;
      
    .more-action {
      display: inline-block;
      width: 15px;
      height: 20px;
      background: none  no-repeat center center / 100% 100%;
      background-image: url('@/assets/images/more.svg');
    }
    .more-action:hover {
      background-color: #84cdf7;
    }

  }

</style>
