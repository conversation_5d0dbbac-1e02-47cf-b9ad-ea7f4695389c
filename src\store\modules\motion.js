import crudMotion from '@/api/motion'

const motion = {
  state: {
    motion: {},
    motions: []
  },

  mutations: {
    SET_MOTION: (state, motion) => {
      state.motion = motion
      for (const item  of state.motions){
        if (item.name === motion.name){
          Object.assign(item, motion)
        }
      }
    },
    SET_MOTIONS: (state, motions) => {
      state.motions = motions
    },
    
    SET_DOOR_STATUS: (state, doorStatus) => {
      state.motion.doorStatus = doorStatus
    }
  },

  actions: {

    // 获取当前运动信息
    getCurrentMotionInfo({ commit }) {
      return new Promise((resolve, reject) => {
        crudMotion.getCurrentMotionInfo(motion.state.motion).then(res => {
          res.xpos = parseFloat(res.xpos).toFixed(2)
          res.ypos = parseFloat(res.ypos).toFixed(2)
          res.zpos = parseFloat(res.zpos).toFixed(2)
          if (motion.state.motion.stepSize) {
            res.stepSize = motion.state.motion.stepSize
          }
          commit('SET_MOTION', res)
        //  console.log('getCurrentMotionInfo. success.'+res)
          resolve(res)
        }).catch(error => {
          console.log('getCurrentMotionInfo. error.'+ error)
          reject(error)
        })
      })
    },
    getMotionInfos({ commit }) {
      return new Promise((resolve, reject) => {
        crudMotion.getMotionInfos(motion.state.motion).then(res => {
          for (const r of res) {
            r.xpos = parseFloat(r.xpos).toFixed(2)
            r.ypos = parseFloat(r.ypos).toFixed(2)
            r.zpos = parseFloat(r.zpos).toFixed(2)
            if (motion.state.motion.stepSize) {
              r.stepSize = motion.state.motion.stepSize
            }
          }
          commit('SET_MOTIONS', res)
        //  console.log('getCurrentMotionInfo. success.'+res)
          resolve(res)
        }).catch(error => {
          console.log('getCurrentMotionInfo. error.'+ error)
          reject(error)
        })
      })
    },
    updateMotionInfo({ commit }, motion) {

      commit('SET_MOTION', motion)
    },
    openDoor({ commit }, newMotion) {
      return new Promise((resolve, reject) => {
        crudMotion.openDoor(newMotion).then(res => {
          commit('SET_MOTION', res)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    }

  }
}

export default motion
