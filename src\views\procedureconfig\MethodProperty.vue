<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="方法名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="方法类型" prop="type">
        <el-select v-model="form.method.type" clearable size="small" placeholder="请选择">
          <el-option v-for="item in dict.data.method_types" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-divider content-position="left">可选变量</el-divider>
      <VariablesEditor
        v-model="form.variables"
        :method-id="getMethodId()"
        @change="handleVariablesChange"
      />
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, watch, onMounted } from 'vue'
import { Node } from '@antv/x6'
import { useDict } from '@/hooks/useDict'
import VariablesEditor from './components/VariablesEditor.vue'

// Props 定义
const props = defineProps({
  curNode: {
    type: Object as () => Node,
    default: () => ({})
  }
})

// 注入
const getDataStore = inject('getDataStore')

// 使用字典
const { dict } = useDict(['method_types'])

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  method: { type: 'MAIN' },
  variables: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const formRef = ref(null)
const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '方法名称不能为空', trigger: 'blur' }
  ]
}

const initing = ref(false)
const dataStore = reactive({})

// 方法定义
const init = () => {
  initing.value = true
}

const transformNodeToFormData = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  form.name = curNode.getData().name
  form.description = curNode.getData().description
  if (curNode.getData().method) {
    form.method.type = curNode.getData().method.type
  }
  
  const methodData = getStoreObjectByNodeId(curNode.id)
  if (methodData) {
    form.variables = methodData.variables
  }
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  if (!Array.isArray(dataStore.methods)) {
    return null
  }
  
  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    if (!Array.isArray(methodObj.method?.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      if (!Array.isArray(stepObj.step?.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }
  return null
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  
  const curNodeData = curNode.getData()
  curNodeData.name = form.name
  curNodeData.description = form.description
  curNodeData.method.type = form.method.type
  
  const methodData = getStoreObjectByNodeId(curNode.id)
  if (methodData) {
    if (!methodData.method) {
      methodData.method = {}
    }
    methodData.method.name = form.name
    methodData.method.type = form.method.type
    methodData.method.description = form.description
    methodData.variables = form.variables
  }

  const changeState = 'changed from MethodProperty'
  curNode.setData({ ...curNodeData, changeState })
}
// 获取ActionId用于变量编辑器
const getMethodId = () => {
  const curNode = props.curNode
  const methodData = getStoreObjectByNodeId(curNode.id)
  return methodData?.method?.id || null
}

// 处理变量变化
const handleVariablesChange = (variables, variablesObj) => {
  console.log('Variables changed:', variables, variablesObj)
  form.variables = variables
}

// 监听器
watch(dataStore, () => {
  transformNodeToFormData()
}, { deep: true })

watch(() => props.curNode, () => {
  transformNodeToFormData()
})

watch(form, () => {
  transformFormDataToNode()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  transformNodeToFormData()
})
</script>

<style lang="scss" scoped>
</style>
