<template>
  <div class="header-board-container">
    <div class="left">
      <div class="left-bottom" />
    </div>
    <div class="title">
      <span>分析机器人可视化平台</span>
    </div>
    <div class="middle">
      <div style="height:40px" />
      <div class="middle-bottom" />
    </div>
    <div class="major_ctrl_board">
      <MajorCtrlBoard />
    </div>
    <div class="right">
      <div class="right-content">
        <el-image
          :src=" imageAccessUrl + '%E7%BC%A9%E6%94%BE-20240621012231165.svg' "
          fit="contain"
          class="img"
          @click="handleScreenFull"
        />
        <span id="time" class="time">2024-06-22 12:00:00</span>
      </div>
      <div class="right-bottom" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import MajorCtrlBoard from './MajorCtrlBoard.vue'
import { setInterval } from 'core-js'

export default {
  components: { MajorCtrlBoard },
  data() {
    return {
      chart: null
    }
  },
  watch: {

  },

  computed: {
    ...mapGetters([
      'imagesUploadApi',
      'imageAccessUrl',
      'baseApi'
    ])
  },
  mounted() {
    setInterval(this.autoUpdateDate)
  },
  beforeDestroy() {

  },
  methods: {
    handleScreenFull() {
      this.$emit('screenfull', true)
    },
    autoUpdateDate() {
      var time = new Date()
		  var year = time.getFullYear() // 获取年份
		  var month = time.getMonth() + 1 // 获取月份
		  var day = time.getDate() // 获取日期
		  var hour = checkTime(time.getHours()) // 获取时
		  var minite = checkTime(time.getMinutes()) // 获取分
		  var second = checkTime(time.getSeconds()) // 获取秒
		  /** **当时、分、秒、小于10时，则添加0****/
		  function checkTime(i) {
		 	 if (i < 10) return '0' + i
		 	 return i
	  	 }
		 var box = document.getElementById('time')
      if (box) {
        box.innerHTML = year + '-' + month + '-' + day + ' ' + hour + ':' + minite + ':' + second
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .flow {
   /* width: 100vw; */
    width: 100%;
    height: 100vh;
  }

  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  .header-board-container {
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    padding: 0px; /* 内边距 */
    color: white; /* 文本颜色 */
    margin: 0px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .left {
      width:40px;
      display: flex;
      .left-bottom {
        margin-top: 40px;
        height: 4px;
        width:40px;
        display: flex;
        background: radial-gradient(ellipse at bottom right, #FFFFFF,#327DA7);
      }
    }
    .title {

      font-size: 28px;
      font-style: italic;
      margin: 20px 20px 0px 20px;
      color: white;
    }
    .right {
      width: 440px;
      display: flex;
      flex-direction: column;

      .right-bottom {
        width: 100%;
        height: 4px;
        background-color: #327DA7;
      //  background: radial-gradient(circle at top left, #FFFFFF,#327DA7 25%);
      }
    }
  }

  .right-content {
    display: flex;
    justify-content: right;
    align-items: center;
    padding-bottom: 10px;
    .img {
      width:32px;
      height: 32px;

    }
    .img :hover {
      cursor: pointer;
      background-color: antiquewhite;
    }
    .time {
      padding-right: 40px;
      padding-left: 20px;
      width: 220px;
    }
  }

  .middle {
    flex: 1;
      display: flex;
      flex-direction: column;

      .middle-bottom {
        width: 100%;
        height: 4px;
        background: radial-gradient(circle at top left, #FFFFFF,#327DA7 25%);
      }
  }

  .major_ctrl_board {
    margin:  40px 10px  0px 10px;

  }

</style>
