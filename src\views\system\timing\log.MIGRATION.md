# 定时任务日志组件 Vue3 迁移文档

## 概述

本文档记录了 `log.vue` 组件从 Vue2 + Element UI 迁移到 Vue3 + Element Plus 的详细过程。

## 主要变更

### 1. 模板语法更新

#### Vue2 → Vue3 语法变更
```vue
<!-- Vue2 -->
<el-dialog :visible.sync="dialog">
<template slot-scope="scope">
<el-button size="mini" type="text">

<!-- Vue3 -->
<el-dialog v-model="dialogVisible">
<template #default="{ row }">
<el-button size="small" type="primary" link>
```

#### 图标使用方式
```vue
<!-- Vue2 -->
<el-button icon="el-icon-search">

<!-- Vue3 -->
<el-button :icon="Search">
```

### 2. 脚本部分重构

#### 从 Options API 到 Composition API
```javascript
// Vue2 - Options API + Mixin
export default {
  mixins: [crud],
  data() {
    return {
      title: '任务日志',
      errorInfo: '',
      errorDialog: false
    }
  },
  methods: {
    doInit() {
      this.$nextTick(() => {
        this.init()
      })
    }
  }
}

// Vue3 - Composition API + useCrud Hook
<script setup>
import { ref, reactive, nextTick } from 'vue'
import { useCrud } from '@/hooks/useCrud'

const errorInfo = ref('')
const errorDialog = ref(false)

const crud = useCrud({
  title: '任务日志',
  url: 'api/jobs/logs',
  query: {
    jobName: '',
    createTime: null,
    isSuccess: ''
  }
})

const doInit = () => {
  nextTick(() => {
    crud.refresh()
  })
}
</script>
```

### 3. 数据管理方式

#### 从 Mixin 到 Composable Hook
- **Vue2**: 使用 `crud` mixin 提供数据管理功能
- **Vue3**: 使用 `useCrud` hook 提供相同功能，更加模块化和类型安全

#### 状态管理对比
```javascript
// Vue2 - Mixin 方式
this.data          // 表格数据
this.loading       // 加载状态
this.query         // 查询参数
this.page          // 当前页码
this.size          // 每页大小
this.total         // 总数据量

// Vue3 - Hook 方式
crud.data          // 表格数据
crud.loading       // 加载状态
crud.query         // 查询参数
crud.page.page     // 当前页码
crud.page.size     // 每页大小
crud.page.total    // 总数据量
```

### 4. 事件处理

#### Props 和 Events
```javascript
// Vue2 - 通过 sync 修饰符
<el-dialog :visible.sync="dialog">

// Vue3 - 通过 v-model
<el-dialog v-model="dialogVisible">

// Props 定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Events 定义
const emit = defineEmits(['update:modelValue'])
```

### 5. 样式更新

#### 深度选择器语法
```scss
/* Vue2 */
::v-deep .el-dialog__body {
  padding: 0 20px 10px 20px !important;
}

/* Vue3 */
:deep(.el-dialog__body) {
  padding: 0 20px 10px 20px !important;
}
```

## 功能特性

### 保持的功能
- ✅ 任务日志查询和筛选
- ✅ 分页显示
- ✅ 异常详情查看
- ✅ 数据导出功能
- ✅ 响应式布局

### 新增特性
- ✅ Vue3 Composition API 支持
- ✅ Element Plus 组件库
- ✅ 更好的 TypeScript 支持
- ✅ 现代化的 Hook 模式
- ✅ 更清晰的组件结构

## 使用方式

### 基本使用
```vue
<template>
  <div>
    <el-button @click="showLog">查看日志</el-button>
    <TimingLog v-model="logVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TimingLog from './log.vue'

const logVisible = ref(false)

const showLog = () => {
  logVisible.value = true
}
</script>
```

### 通过 ref 调用方法
```vue
<template>
  <TimingLog ref="logRef" v-model="logVisible" />
</template>

<script setup>
import { ref } from 'vue'

const logRef = ref(null)

const initLog = () => {
  logRef.value?.doInit()
}
</script>
```

## API 文档

### Props
| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | Boolean | false | 控制弹窗显示/隐藏，支持v-model |

### Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (visible: Boolean) | 弹窗显示状态变化时触发 |

### Methods
| 方法名 | 参数 | 说明 |
|--------|------|------|
| doInit | () | 初始化并加载日志数据 |

## 依赖要求

### 必需依赖
- Vue 3.x
- Element Plus 2.x
- @element-plus/icons-vue

### 内部依赖
- `@/hooks/useCrud` - 数据管理 Hook
- `@/api/data` - API 接口
- `@/utils/index` - 工具函数
- `@/components/DateRangePicker` - 日期范围选择器

## 迁移检查清单

- [x] 模板语法更新 (`:visible.sync` → `v-model`)
- [x] 插槽语法更新 (`slot-scope` → `#default`)
- [x] 图标引入方式更新
- [x] 组件大小属性更新 (`mini` → `small`)
- [x] 按钮类型更新 (`text` → `link`)
- [x] 脚本重构为 Composition API
- [x] Mixin 替换为 Hook
- [x] Props/Emits 定义
- [x] 响应式数据声明
- [x] 方法重新定义
- [x] 样式深度选择器更新
- [x] 功能测试验证

## 注意事项

1. **分页参数**: Vue3 版本中页码从1开始，而不是0
2. **事件处理**: 使用新的事件监听方式
3. **响应式**: 确保所有数据都正确声明为响应式
4. **类型安全**: 建议配合 TypeScript 使用以获得更好的开发体验

## 测试建议

1. 测试弹窗打开/关闭功能
2. 测试搜索和筛选功能
3. 测试分页功能
4. 测试数据导出功能
5. 测试异常详情查看功能
6. 测试响应式布局

## 总结

本次迁移成功将组件从 Vue2 升级到 Vue3，保持了所有原有功能的同时，采用了更现代化的开发模式，提高了代码的可维护性和类型安全性。
