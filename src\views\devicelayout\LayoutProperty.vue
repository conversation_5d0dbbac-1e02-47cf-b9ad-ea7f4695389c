<template>
  <div class="property-container">
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-divider content-position="left">基本信息</el-divider>
      <el-form-item label="设备实例名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="设备描述">
        <el-input v-model="form.description" />
      </el-form-item>
      <el-form-item label="设备类型">
        <el-select v-model="form.type" clearable size="small" placeholder="类型" >
          <el-option v-for="item in dict.data.device_types" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备名">
        <el-select v-model="form.device" value-key="id" clearable size="small" placeholder="设备名" >
          <el-option v-for="item in deviceOptions" :key="item.id" :label="item.name" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备图像">
        <!-- 修改为使用 v-model:device 绑定 form -->
        <DeviceImageEditor v-model:device="form" :device-instance="getCurDeviceInstance()" />
      </el-form-item>

      <el-form-item label="节点可视" prop="layoutVisible">
        <el-select v-model="form.layoutVisible" filterable placeholder="请选择" style="width: 60px">
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
      </el-form-item>

      <el-form-item label="zIndex" prop="zIndex">
        <el-input v-model="form.zIndex" />
      </el-form-item>

      <el-divider content-position="left">点位设置</el-divider>
      <el-table 
        ref="tableRef" 
        :data="form.positions" 
        size="small" 
        cell-class-name="position-table-cell" 
        max-height="450"
        row-key="devicePosIndex"
        @cell-click="handleCellClick"
      >
        <el-table-column prop="devicePosIndex" label="序号" width="50">
          <template #default="scope">
            <div class="drag-handle" @mousedown="handleRowMouseDown( scope.row, scope.column, $event)">
            <el-button 
                  :circle = "scope.row.shape==='circle'?true:false" 
                  @click="onPosSelect(scope.row.devicePosIndex)"
                  @blur.capture="onPosBlur(scope.row.devicePosIndex)"
                  :class="{ 'button-selected': scope.row.devicePosIndex === currentSelectedPosIndex }"
                >{{ scope.row.devicePosIndex }}</el-button>
            </div>    
          </template>
        </el-table-column>
        <el-table-column prop="name" label="点位名称">
          <template #default="scope">
            <el-select
              v-if="scope.row.nameShowEditor===true"
              v-model="scope.row.name"
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              placeholder="选择点位名称"
              @change="handleInputBlur(scope.row, 'name')"
              @blur="handleInputBlur(scope.row, 'name')"
            >
              <el-option
                v-for="item in posNameOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span v-else>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="objectName" label="放置物">
          <template #default="scope">
           
            <el-select
              v-if="scope.row.objectNameShowEditor===true"
              v-model="scope.row.objectName"
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              placeholder="选择物品名称"
              @change="handleInputBlur(scope.row, 'objectName')"
              @blur="handleInputBlur(scope.row, 'objectName')"
            >
              <el-option
                v-for="item in objectNameOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
             <span v-else>{{ scope.row.objectName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="robots" label="机器人点位">
          <template #default="scope">
            
            <el-select v-if="scope.row.robotsShowEditor===true" 
            v-model="scope.row.robots" placeholder="设置机器人点位" 
            multiple
            clearable
            value-key="robotDevInstanceId" 
            @change="handleInputBlur(scope.row, 'robots')"
            @blur="handleInputBlur(scope.row, 'robots')"
            >
            <template #label="{ label, value }">
              <span style="font-weight: bold">{{ value.robotPosCode }}</span>
              <span> @{{ label }} </span>
            </template>
              <el-option
                v-for="item in scope.row.robotPosOptions"
                :key="item.robotDevInstanceId"
                :label="item.robotDevInstanceName"
                :value="item"
              >
                
                <el-input v-model="item.robotPosCode"  class="input-name"  style="width: 40px;" @click.stop @mousedown.stop
                    @mouseup.stop
                    @input="updateRobotPosCode(item, scope.row)" />
                <span > @{{ item.robotDevInstanceName }}</span>
              </el-option>
            </el-select>
            <div v-else><el-tag v-for="item in  scope.row.robots">{{ getRobotPosTag(item, scope.row.robotPosOptions) }}</el-tag></div>
            
          </template>
        </el-table-column>
        <el-table-column prop="serviceCode" label="服务码" width="60">
          <template #default="scope">
            <el-input v-model="scope.row.serviceCode" class="input-pos"  width="56" />
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="60">
          <template #default="scope">
            <el-select v-model="scope.row.type" size="small" >
              <el-option value="DEFAULT" label="DEFAULT" />
              <el-option value="BOARD" label="BOARD" />
              <el-option value="INDICATOR" label="INDICATOR" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="visible" label="可视" width="40">
          <template #default="scope">
            <el-switch v-model="scope.row.visible" active-value="Y" inactive-value="N" class="input-pos" />
          </template>
        </el-table-column>
        
        <el-table-column prop="holdLevel" label="访问级别" >
          <template #default="scope">
            <el-select v-model="scope.row.holdLevel" size="small" >
              <el-option value="TASK" label="TASK" />
              <el-option value="METHOD" label="METHOD" />
              <el-option value="STEP" label="STEP" />
              <el-option value="ACTION" label="ACTION" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="initStatus" label="初始状态">
          <template #default="scope">
            
            <el-select 
            v-if="scope.row.initStatusShowEditor===true"
            v-model="scope.row.initStatus" 
            clearable size="small" 
            placeholder="请选择"
            @change="handleInputBlur(scope.row, 'initStatus')"
            @blur="handleInputBlur(scope.row, 'initStatus')"
            >
              <el-option 
                v-for="item in dict.data.position_status" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
            <span v-else>{{ scope.row.initStatus }}</span>
            
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted, provide  } from 'vue'
import { Node } from '@antv/x6'
import { useDict } from '@/hooks/useDict'
import RobotAxisAdjustor from '../components/RobotAxisAdjustor.vue'
import { debounce } from '@/utils'
import DeviceImageEditor from '../devicemgt/DeviceImageEditor.vue'
import { queryAllDevices } from '@/api/device'

// Props
const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  }
})

// Emits
const emit = defineEmits(['update-device-instance'])

// 响应式数据
const defaultForm = { id: null, name: null, description: null, device:{}, positions: [], positionConfig: null, type: null, configTemplate: null, javaClassName: null, configJavaClassName: null, 
  layoutImage: null, layoutWidth: null, layoutHeight: null, layoutVisible:null, imageVisible:null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }
const form = reactive({ ...defaultForm })
const rules = reactive({
  name: [
    { required: true, message: '设备实例名称', trigger: 'blur' }
  ]
})
const currentSelectedPosIndex = ref(-1)
const initing = ref(false)
const formRef = ref(null)
const tableRef = ref(null)

const deviceOptions = ref(null)

// 拖拽相关状态
const isDragging = ref(false)
const dragStartIndex = ref(-1)
const dragOverIndex = ref(-1)


const tmpRobotPosCode = ref('')

const robotPosOptions = computed(() => {
  const robotPosOptions = []

  const curNode = props.curNode
  const { graph } = curNode.model || {}

  if (!graph) {
    return robotPosOptions
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return robotPosOptions
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.type === 'ROBOT') {
      robotPosOptions.push({
        robotDevInstanceId: ins.id,
        robotDevInstanceName: ins.name,
        robotPosCode: ''
      })
    }
  }
  
  return robotPosOptions
})

const posNameOptions = computed(() => {
  const posNameOptions = []
  const curNode = props.curNode
  const { graph } = curNode.model || {}
  if (!graph) {
    return posNameOptions
  }
  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return posNameOptions
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.positions && Array.isArray(ins.positions) && ins.positions.length > 0) {
      for (var j in ins.positions) {
        const pos = ins.positions[j]  
        if (posNameOptions.find(o => o.value === pos.name)) {
          continue
        }
        posNameOptions.push({
          value: pos.name,
          label: pos.name
        })
      }
    }
  }
  for (const pos of form.positions) {
    if (posNameOptions.find(o => o.value === pos.name)) {
      continue
    }
    posNameOptions.push({
      value: pos.name,
      label: pos.name
    })
  }
  return posNameOptions
})


const objectNameOptions = computed(() => {
  const objectNameOptions = []
  const curNode = props.curNode
  const { graph } = curNode.model || {}
  if (!graph) {
    return objectNameOptions
  }
  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return objectNameOptions
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.positions && Array.isArray(ins.positions) && ins.positions.length > 0) {
      for (var j in ins.positions) {
        const pos = ins.positions[j]
        if (objectNameOptions.find(o => o.value === pos.objectName)) {
          continue
        }
        if (pos.objectName){
          objectNameOptions.push({
            value: pos.objectName,
            label: pos.objectName
          })
        }
      }
    }
  }

  for (const pos of form.positions) {
    if (objectNameOptions.find(o => o.value === pos.objectName)) {
      continue
    }
    if (pos.objectName){
      objectNameOptions.push({
        value: pos.objectName,
        label: pos.objectName
      })
    }
  }

  return objectNameOptions
})


// 字典数据（需要根据实际项目调整字典获取方式）
const { dict } = useDict(['device_types', 'position_status'])

// 方法
const getCurDeviceInstance = () => {
  const curNode = props.curNode
  const { graph } = curNode.model || {}

  if (!graph) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === curNode.id) {
      return ins
    }
  }
  return null
}

// 更新机器人点位编号
const updateRobotPosCode = (item, row) => {
  
  // 查找当前行中是否已经选择了这个机器人
  if (row.robots && Array.isArray(row.robots)) {
    const existingRobot = row.robots.find(r => r.robotDevInstanceId === item.robotDevInstanceId);
    if (existingRobot) {
      // 更新已选择机器人的点位编号
      existingRobot.robotPosCode = item.robotPosCode;
    }else{
  //    row.robots.push(item);
    }
  }
}

const getRobotPosTag = (item, robotPosOptions) =>  {
  const option = robotPosOptions.find(opt => opt.robotDevInstanceId === item.robotDevInstanceId);
  if (option) {
    return option.robotPosCode + '@' + option.robotDevInstanceName;
  }
  return '';
}

const transformNodeToFormData = () => {
  const curNode = props.curNode
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }

  form.name = deviceInst.name
  form.description = deviceInst.description
  form.device = deviceInst.device
  form.type = deviceInst.type || deviceInst.device.type
  if (deviceInst.layoutImage) {
    form.layoutImage = deviceInst.layoutImage
  }else if (deviceInst.device.layoutImage) {
    form.layoutImage = deviceInst.device.layoutImage
  }
  if (deviceInst.layoutWidth) {
    form.layoutWidth = deviceInst.layoutWidth
  }else if (deviceInst.device.layoutWidth) {
    form.layoutWidth = deviceInst.device.layoutWidth
  }
  if (deviceInst.layoutHeight) {
    form.layoutHeight = deviceInst.layoutHeight
  }else if (deviceInst.device.layoutHeight) {
    form.layoutHeight = deviceInst.device.layoutHeight
  }

  form.layoutVisible = deviceInst.layoutVisible
  form.zIndex = curNode.prop('zIndex')

  //form.layoutVisible = curNode.getData().layoutVisible
  form.imageVisible = curNode.getData().imageVisible
  
  form.positionConfig = deviceInst.positionConfig
  resetFormPositions(deviceInst)
  
}
const resetFormPositions = (deviceInst) => {
  const devPosConfig = form.positionConfig
  if (devPosConfig) {
    const posArr = JSON.parse(devPosConfig)
    if (!deviceInst.positions) {
      deviceInst.positions = []
    }
    for (let idx = 0; idx < posArr.length; idx++) {
      if (deviceInst.positions.length === idx) {
        const newPos = { name: deviceInst.name + '点位', devicePosIndex: idx, shape: posArr[idx].shape,  type: posArr[idx].type, visible:'Y', boardNodeId:posArr[idx].boardNodeId,nodeId:posArr[idx].nodeId, mapPos: posArr[idx] }
        deviceInst.positions.push(newPos)
      } else {
        let tmpType = deviceInst.positions[idx].type
        if (tmpType!=='INDICATOR') {
          tmpType =  posArr[idx].type
        }
        let tmpVisible = deviceInst.positions[idx].visible
        if (!tmpVisible) {
          tmpVisible =  'Y'
        }
        deviceInst.positions[idx] = { ...deviceInst.positions[idx], devicePosIndex: idx, type:tmpType, visible: tmpVisible,shape: posArr[idx].shape, boardNodeId:posArr[idx].boardNodeId,nodeId:posArr[idx].nodeId, mapPos: posArr[idx] }
      }
    }
    const needToDelCount = deviceInst.positions.length - posArr.length
    if (needToDelCount > 0) {
      deviceInst.positions.splice(posArr.length, needToDelCount)
    }

    form.positions.splice(0, form.positions.length)
    for (const pos of deviceInst.positions) {
      form.positions.push(pos)
    }
  }else{
    form.positions = []
  }
  for (const pos of form.positions) {
    pos.robotPosOptions = JSON.parse(JSON.stringify(robotPosOptions.value))
    if (pos.robots && pos.robots.length > 0) {
      for (let i = 0; i < pos.robots.length; i++) {
        const robot = pos.robots[i]
        const option = pos.robotPosOptions.find(opt => opt.robotDevInstanceId === robot.robotDevInstanceId)
        if (option) {
          option.robotPosCode = robot.robotPosCode
        }
      }
    }
  }
  
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }
  
  // 
  deviceInst.name = form.name
  deviceInst.description = form.description
  deviceInst.type = form.type
  deviceInst.layoutImage = form.layoutImage
  deviceInst.layoutWidth = form.layoutWidth
  deviceInst.layoutHeight = form.layoutHeight
  deviceInst.layoutVisible = form.layoutVisible
  // 
  for (const pos of form.positions) {
    if (!pos.status || pos.status === 'NONE') {
      pos.status = pos.initStatus
    }
  }
  deviceInst.positions = form.positions
debugger
  if ( form.positionConfig ) {
    debugger
    deviceInst.positionConfig = bindServiceCodes(form.positionConfig, form.positions)
  }
 
  const changeState = 'CHANGED_FROM_PROPERTY'
  curNode.setData({ ...curNode.getData(), changeState, name:deviceInst.name, layoutImage: deviceInst.layoutImage })
  emit('update-device-instance', deviceInst)
}

const bindServiceCodes = (posCfg, positions) => {
  const posCfgObj = JSON.parse(posCfg)
  for (const posIdx in posCfgObj) {
    const pos = posCfgObj[posIdx]
    const serviceCode = positions[posIdx]?.serviceCode
    if (serviceCode) {
      const codeArr = serviceCode.split('-')
      pos.serviceCode = codeArr[0]
      if (codeArr.length > 1) {
        pos.serviceCode2 = codeArr[1]
      }
    }
  }
  return JSON.stringify(posCfgObj)
}

// 创建防抖的更新函数
const debouncedTransformFormDataToNode = debounce(() => {
  transformFormDataToNode()
}, 300)

const onPosSelect = (devicePosIndex) => {
  const curNode = props.curNode
  console.log('onPosSelect devicePosIndex=' + devicePosIndex)
  if (currentSelectedPosIndex.value >= 0) {
    const state = { posIndex: currentSelectedPosIndex.value, state: 'SELECTED' }
    const changeState = 'POSITION_CHANGE'
    curNode.setData({ ...curNode.getData(), ...state, changeState })
  }
  
  const state = { posIndex: devicePosIndex, selectState: 'SELECTED' }
  const changeState = 'POSITION_CHANGE'
  curNode.setData({ ...curNode.getData(), ...state, changeState })
  currentSelectedPosIndex.value = devicePosIndex
}

const onPosBlur = (devicePosIndex) => {
  const curNode = props.curNode
  console.log('onPosBlur devicePosIndex=' + devicePosIndex)
  
  if (currentSelectedPosIndex.value >= 0) {
    const state = { posIndex: currentSelectedPosIndex.value, selectState: 'UNSELECTED' }
    const changeState = 'POSITION_CHANGE'
    curNode.setData({ ...curNode.getData(), ...state, changeState })
  }
  currentSelectedPosIndex.value = -1
}

// 方法定义
const handleInputBlur = (row, columnName) => {
  row[`${columnName}ShowEditor`] = false
}

const handleCellClick = (row, column) => {
  row[`${column.property}ShowEditor`] = true
}
// 获取行的类名，用于拖拽时的样式
const getRowClassName = ({ row, rowIndex }) => {
  console.log('getRowClassName rowIndex=' + rowIndex+' isDragging.value=' + isDragging.value + ' dragStartIndex.value=' + dragStartIndex.value)
  if (rowIndex === dragOverIndex.value && isDragging.value) {
    return 'drag-over-row'
  }
  return ''
}

// 处理鼠标按下事件
const handleRowMouseDown = (row, column, event) => {
  
  console.log('handleRowMouseDown, rowIndex=' + row.devicePosIndex + ', event.button=' + event.button)
  // 只有在按下左键时才开始拖拽
  if (event.button === 0) {
    isDragging.value = true
    dragStartIndex.value = row.devicePosIndex
    console.log('handleRowMouseDown rowIndex=' + row.devicePosIndex )
    // 添加鼠标移动和松开的全局事件监听
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleGlobalMouseUp)
  }
}

// 处理鼠标移动事件
const handleMouseMove = (event) => {
  if (!isDragging.value) return
  
  // 获取鼠标所在的行
  const tableEl = tableRef.value.$el
  const rows = tableEl.querySelectorAll('.el-table__body tr')
  
  // 计算鼠标位置相对于每一行的位置，找出最近的行
  let closestRow = null
  let minDistance = Infinity
  
  rows.forEach((row, index) => {
    const rect = row.getBoundingClientRect()
    const rowMiddle = rect.top + rect.height / 2
    const distance = Math.abs(event.clientY - rowMiddle)
    
    if (distance < minDistance) {
      minDistance = distance
      closestRow = index
    }
  })
  
  if (closestRow !== null && closestRow < form.positions.length) {
    dragOverIndex.value = form.positions[closestRow].devicePosIndex
    console.log('handleMouseMove dragOverIndex.value=' + dragOverIndex.value)
  }
}

// 处理全局鼠标松开事件
const handleGlobalMouseUp = () => {
  if (isDragging.value && dragStartIndex.value !== -1 && dragOverIndex.value !== -1 && dragStartIndex.value !== dragOverIndex.value) {
    // 执行复制操作
    copyPositionRow(dragStartIndex.value, dragOverIndex.value)
  }
  
  // 重置拖拽状态
  isDragging.value = false
  dragStartIndex.value = -1
  dragOverIndex.value = -1
  
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleGlobalMouseUp)
}

// 处理表格内的鼠标松开事件
const handleRowMouseUp = (row) => {
  if (isDragging.value && dragStartIndex.value !== -1) {
    dragOverIndex.value = row.devicePosIndex
  }
}

// 复制行数据
const copyPositionRow = (fromIndex, toIndex) => {
  if (fromIndex === toIndex) return
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) return
  
  // 找到源行和目标行
  const sourceRow = form.positions.find(p => p.devicePosIndex === fromIndex)
  for (let idx=fromIndex+1; idx<=toIndex; idx++) {
    const targetRow = form.positions.find(p => p.devicePosIndex === idx)
    if (!targetRow) continue
    targetRow.name = sourceRow.name
    targetRow.type = sourceRow.type
    targetRow.visible = sourceRow.visible
    targetRow.objectName = sourceRow.objectName
    targetRow.serviceCode = sourceRow.serviceCode
    targetRow.holdLevel = sourceRow.holdLevel
    // 更新
    targetRow.robots = JSON.parse(JSON.stringify(sourceRow.robots))
    for (const rb of targetRow.robots) {
      rb.id = null
    }
    // 如果有机器人点位选项，需要重新设置
    if (targetRow.robotPosOptions) {
      targetRow.robotPosOptions = JSON.parse(JSON.stringify(robotPosOptions.value))
      if (targetRow.robots && targetRow.robots.length > 0) {
        for (let i = 0; i < targetRow.robots.length; i++) {
          const robot = targetRow.robots[i]
          const option = targetRow.robotPosOptions.find(opt => opt.robotDevInstanceId === robot.robotDevInstanceId)
          if (option) {
            option.robotPosCode = robot.robotPosCode
          }
        }
      }
    }

    // 更新初始状态
    targetRow.initStatus = sourceRow.initStatus
      
  }
  
  // 更新节点数据
  transformFormDataToNode()
}

// 监听器
watch(() => props.curNode, (newVal, oldVal) => {
  transformNodeToFormData()
  console.log(`curNode changed from ${oldVal} to ${newVal}`)
})

watch(
  () => form,
  (newVal) => {
    
    debouncedTransformFormDataToNode()
    console.log('form.name changed from ' + form.name + ' to ' + newVal)
    
  },
  {
    deep: false
  }
)
watch(
  () => [form.name, form.description, form.type, form.layoutWidth, form.layoutHeight,form.layoutVisible],
  ([newWidth, newHeight, newVisible]) => {
    const curNode = props.curNode
    const deviceInst = getCurDeviceInstance()
    if (!deviceInst) {
      return
    }
    deviceInst.layoutWidth = form.layoutWidth
    deviceInst.layoutHeight = form.layoutHeight
    deviceInst.layoutVisible = form.layoutVisible
    deviceInst.name = form.name
    deviceInst.description = form.description
    deviceInst.type = form.type

    const changeState = 'CHANGED_FROM_PROPERTY'
    curNode.setData({...curNode.getData(), name: deviceInst.name, changeState, layoutImage: deviceInst.layoutImage })

  }
)
watch( () => form.positionConfig, (newVal, oldVal) => {
  const curNode = props.curNode
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }
  debugger
  deviceInst.positionConfig = form.positionConfig
  resetFormPositions(deviceInst)
  const changeState = 'POSITION_CHANGE'
  curNode.setData({...curNode.getData(), changeState, layoutImage: deviceInst.layoutImage })
})

watch( () => form.positions, (newVal, oldVal) => {

  const bindedposCfg = bindServiceCodes (form.positionConfig, form.positions) 
  if (bindedposCfg === form.positionConfig) {
    return
  }
  const curNode = props.curNode
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }
  form.positionConfig = bindedposCfg
  deviceInst.positionConfig = form.positionConfig
 
},{deep:true})


watch(() => form.layoutImage, (newVal, oldVal) => {
  const curNode = props.curNode
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }
  deviceInst.layoutImage = form.layoutImage
  const changeState = 'CHANGED_FROM_PROPERTY'
  curNode.setData({...curNode.getData(), changeState, layoutImage: deviceInst.layoutImage })
})
watch(() => form.zIndex, (newVal, oldVal) => {
  const curNode = props.curNode
  curNode.prop('zIndex',form.zIndex);
})
watch(() => form.imageVisible, (newVal, oldVal) => {
  const curNode = props.curNode
  curNode.getData().imageVisible = form.imageVisible;
})

watch(() => form.device, (newVal, oldVal) => {
  const curNode = props.curNode
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) {
    return
  }
  deviceInst.device = form.device
})



const initDeviceOptions = async () => {
  
  const res = await queryAllDevices(null)
  deviceOptions.value = res
}

// 生命周期
onMounted(() => {
  console.log('LayoutProperty created..')
  transformNodeToFormData()
  const curNode = props.curNode

  const {width, height} = curNode.size()

  curNode.on('change:size', () => {
    console.log('curNode size changed')
    
    //transformNodeToFormData()
    const deviceInst = getCurDeviceInstance()
    if (!deviceInst) {
      return
    }
    const {width, height} = curNode.size()
    if (form.layoutWidth !== width){
      form.layoutWidth = width
    }
    if (form.layoutHeight!== height){
      form.layoutHeight = height
    }
  })

  // 设置deviceOptions = dataStore.allDevices
  initDeviceOptions()
})


provide('getCurNode', () => {
  return props.curNode
})
</script>

<style lang="scss">
.property-container {
  width: 100%;
}
.circle-button {
  width: 40px;
  height: 40px;
}

.button-selected {
  background-color: var(--el-button-hover-bg-color);
  border-color: var(--el-button-hover-border-color);
  color: var(--el-button-hover-text-color);
  outline: none;
}

.el-table .position-table-cell .cell {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell input {
  padding-left: 2px;
  padding-right: 2px;
}
.el-table .position-table-cell .cell .input-pos input {
  width:56px;
}
.el-table .position-table-cell .cell .input-name {
  padding-right: 4px;
}

/* 拖拽相关样式 */
.el-table .drag-over-row {
  background-color: #ecf5ff;
  border-bottom: 2px dashed #409eff;
}

.drag-handle {
  cursor: move;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.el-table .el-table__row {
  transition: background-color 0.2s;
}
</style>
