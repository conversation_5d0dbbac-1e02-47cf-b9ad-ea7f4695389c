import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/deviceMock',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/deviceMock/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/deviceMock',
    method: 'put',
    data
  })
}
export function queryAllDeviceMocks(params) {
  return request({
    url: 'api/deviceMock/queryAllDeviceMocks',
    method: 'get',
    params
  })
}

export function start(data) {
  return request({
    url: 'api/deviceMock/start',
    method: 'put',
    data
  })
}

export function stop(data) {
  return request({
    url: 'api/deviceMock/stop',
    method: 'put',
    data
  })
}

export function inputScanner(data) {
  return request({
    url: 'api/deviceMock/inputScanner',
    method: 'post',
    data
  })
}

export default { add, edit, del, queryAllDeviceMocks, start, stop, inputScanner }
