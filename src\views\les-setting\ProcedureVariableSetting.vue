<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="120px">
        <el-tabs
          v-model="activeName"
          tab-position="left"
          class="demo-tabs"
        >
          <el-tab-pane v-for="procedure in form.procedures" :key="procedure.id" :label="procedure.name" :name="procedure.id" >
            <el-form-item v-for="item in procedure.variables" :key="item.name" :label="item.viewName" >
              <el-input v-model="item.value"  style="width: 40%"/>
              <span style="color: #C0C0C0;margin-left: 10px;">{{item.description}}</span>
            </el-form-item>
          </el-tab-pane>

        </el-tabs>
        
      </el-form>

      <div slot="footer" class="footer-inner-container">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted, computed } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { useDict } from '@/hooks/useDict'
import { useStore } from 'vuex'
import crudSetting from '@/api/setting'
import crudProcedureVariable from '@/api/procedureVariable'


const activeName = ref('0')

// refs
const formRef = ref(null)
const tableRef = ref(null)

const taskStarterMode = ref(null)

// 初始化表单
const defaultForm = {
  id: null,
  name: null,
  description: null,
  procedures: [],
  globalSettings: null,
  globalSettingsObj: {
    taskStarterMode: 'DEFAULT',
    autoSubmitTask: false,
    debugMode: false
  }
}

const rules = {
  name: [
    { required: true, message: '设备名称不能为空', trigger: 'blur' }
  ]
}

// 使用CRUD钩子
const { crud, CRUD, query, form } = useCrud({
  title: '系统设置',
  url: 'api/procedureVariable',
  crudMethod: { ...crudProcedureVariable },
  defaultForm,
  rules,
  formRef,
  tableRef
})

// 初始化
const initForm = async () => {
  
  const res = await crudProcedureVariable.queryAllRawProceduresWithVariables()
  if (res ) {
    form.procedures = res
    activeName.value = res[0].id
  }
}

onMounted(() => {
  initForm()
})


// Before Submit Validation
crud.hooks[CRUD.HOOK.afterValidateCU] = () => {
  
    crud.status.edit = CRUD.STATUS.PREPARED

  return true
}

crud.hooks[CRUD.HOOK.afterSubmit] = () => {
  initForm()
}

</script>


<style scoped>
.footer-inner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  :deep(.el-form-item--small) {
    margin-bottom: 0px;
  }
}
.footer-inner-container .el-button {
  margin: 0 5px;
}
</style>
