<template>
  <div style="margin-bottom: 18px;">
    <el-dialog
      align-center
      v-model="positionSelectDialogVisible"
      :close-on-click-modal="false"
      title="设备实例选择"
      width="840px"
      @opened="openDialog"
    >
      <PositionSelect :cur-data="curPredicateItem" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="text" @click="positionSelectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPosSelection">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <LockSelect :cur-data="curPredicateItem" v-model:dialogVisible="lockSelectDialogVisible" />

    <ScriptSelect v-if="curPredicateItem?.sourceType==='script-select'" :cur-data="curPredicateItem" v-model:dialogVisible="scriptSelectDialogVisible" />

    <el-table
      ref="tableRef"
      :data="form.predicatesArr"
      size="small"
      cell-class-name="predicate-table-cell"
      max-height="450"
      class="predicate-table"
    >
      <el-table-column prop="operator" label="连接符" width="80">
        <template #default="scope">
          <el-select v-model="scope.row.joinOperator" placeholder="请选择" size="small">
            <el-option label="与" value="and" />
            <el-option label="或" value="or" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" min-width="100">
        <template #default="scope">
          <el-input v-model="scope.row.name" :title="scope.row.name" placeholder="请选择" class="input-with-select">
            <template #append>
              <el-dropdown @command="handlePredicateNameCommand">
                <el-button v-if="scope.row.sourceType==='position-select'" :icon="Coordinate" />
                <el-button v-if="scope.row.sourceType==='lock-select'" :icon="Lock" />
                <el-button v-if="scope.row.sourceType==='script-select'" :icon="Tickets" />
                <el-button v-else :icon="Search" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="'position-select,' + scope.$index"><el-icon><Coordinate /></el-icon>选择点位</el-dropdown-item>
                    <el-dropdown-item :command="'lock-select,' + scope.$index"><el-icon><Lock /></el-icon>选择锁</el-dropdown-item>
                    <el-dropdown-item :command="'script-select,' + scope.$index"><el-icon><Tickets /></el-icon>选择脚本</el-dropdown-item>
                    <el-dropdown-item :command="'command,' + scope.$index"><el-icon><Search /></el-icon>选择指令</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-input>
        </template>
      </el-table-column>
      <el-table-column prop="compareOperator" label="比较符" width="80">
        <template #default="scope">
          <el-select v-model="scope.row.compareOperator" placeholder="请选择" size="small">
            <el-option label="等于" value="eq" />
            <el-option label="不等" value="ne" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="targetValue" label="值" width="60">
        <template #default="scope">
          <el-select v-if="scope.row.sourceType === 'position-select'" v-model="scope.row.targetValue" :title="scope.row.targetValue"
            clearable 
            filterable
            default-first-option
            size="small" placeholder="请选择">
            <el-option v-for="item in dict.data.position_status" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-input v-else v-model="scope.row.targetValue" class="input-pos" />
        </template>
      </el-table-column>
      <el-table-column prop="unmatchedThen" label="不满足时" width="80">
        <template #default="scope">
          <el-select v-model="scope.row.unmatchedThen" placeholder="请选择" size="small">
            <el-option label="等待" value="WAIT" />
            <el-option label="跳过" value="SKIP" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPer(['admin', 'result:edit', 'result:del'])" prop="operation" fixed="right" label="操作" width="70">
        <template #header>
          <div>
            <span>操作</span>
            <el-button :icon="Plus" class="operation" @click="addCondition" />
          </div>
        </template>
        <template #default="scope">
          <div class="operation-container">
            <el-button :icon="Delete" class="operation" @click="deleteCondition(scope.$index)" />
            <el-button :disabled="scope.$index === form.predicatesArr.length - 1" :icon="SortDown" class="operation" @click="sortConditionItemDown(scope.$index)" />
            <el-button :disabled="scope.$index === 0" :icon="SortUp" class="operation sort-up" @click="sortConditionItemUp(scope.$index)" />
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, inject, defineModel } from 'vue'
import { Node } from '@antv/x6'
import { Search, Plus, Delete, Lock, Coordinate, Tickets, SortUp, SortDown } from '@element-plus/icons-vue'
import PositionSelect from './PositionSelect.vue'
import LockSelect from './LockSelect.vue'
import ScriptSelect from './ScriptSelect.vue'
import { useDict } from '@/hooks/useDict'

// Props
const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  },
  content: {
    default: null,
    type: String
  }
})


// 权限检查
const checkPer = inject('checkPer')

// 使用字典
const { dict } = useDict(['failed_then_enum', 'position_status'])

// 响应式数据
const defaultForm = { 
  id: null, 
  name: null, 
  description: null, 
  asyncMode: null, 
  variablesArr: [], 
  predicatesArr: [], 
  deleteFlag: null, 
  createBy: null, 
  createTime: null, 
  updateBy: null, 
  updateTime: null 
}

const form = reactive({ ...defaultForm })
const tableRef = ref(null)
const initing = ref(false)
const positionSelectDialogVisible = ref(false)
const lockSelectDialogVisible = ref(false)
const scriptSelectDialogVisible = ref(false)
const curPredicateItem = ref({})
const curPredicateItemIndex = ref(0)
const dataStore = ref(null)

// 设备点位
const devPosition = ref([{ xpos: 0.0, ypos: 0.0, zpos: 0.0 }])

// 监听器
watch(() => [props.curNode, props.content], () => {
  transformNodeToFormData()
}, { deep: false })

watch(() => form.predicatesArr, (newVal, oldVal) => {
  //debugger
  //if (oldVal.length === 0) {
  //  return
  //}
  transferPredicatesArrToPredicates()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  transformNodeToFormData()
})

// 方法
function init() {
  initing.value = true
}

function openDialog() {
  // 对话框打开时的处理
}

function transformNodeToFormData() {
  const curData = getStoreObjectByNodeId()
  
  if (curData){
    form.predicatesArr = []
    if (curData.predicates) {
      form.predicatesArr = JSON.parse(curData.predicates)
    }
    return
  }

  if (props.content ) {
    form.predicatesArr = []
    form.predicatesArr = JSON.parse(props.content)
  }
  
}

function transferPredicatesArrToPredicates() {
  const curData = getStoreObjectByNodeId()
  if (!curData) return
  
  curData.predicates = JSON.stringify(form.predicatesArr)
}

function addCondition() {
  const newData = { 
    joinOperator: 'and', 
    name: null, 
    sourceType: null, 
    sourceParameter: null, 
    compareOperator: 'eq', 
    targetValue: null 
  }
  form.predicatesArr.push(newData)
}

function deleteCondition(rowIndex) {
  form.predicatesArr.splice(rowIndex, 1)
}

function sortConditionItemDown(rowIndex) {
  if (rowIndex === form.predicatesArr.length - 1) {
    return
  }
  const item = form.predicatesArr.splice(rowIndex, 1)[0]
  form.predicatesArr.splice(rowIndex + 1, 0, item)
}

function sortConditionItemUp(rowIndex) {
  if (rowIndex === 0) {
    return
  }
  const item = form.predicatesArr.splice(rowIndex, 1)[0]
  form.predicatesArr.splice(rowIndex - 1, 0, item)
}

function handlePredicateNameCommand(arg) {
  const argArr = arg.split(',')
  const cmd = argArr[0]
  const rowIdx = Number(argArr[1])
  const curData = form.predicatesArr[rowIdx]
  curData.sourceType = cmd
  curPredicateItem.value = curData
  curPredicateItemIndex.value = rowIdx

  if (cmd === 'position-select') {
    positionSelectDialogVisible.value = true
  }else if (cmd === 'lock-select') {
    // 处理锁选择
    lockSelectDialogVisible.value = true
  }else if (cmd === 'script-select') {
    // 处理锁选择
    scriptSelectDialogVisible.value = true
  }else if (cmd === 'command') {
    // 处理指令选择
  }
}

function submitPosSelection() {
  form.predicatesArr.splice(curPredicateItemIndex.value, 1, curPredicateItem.value)
  positionSelectDialogVisible.value = false
}

function getStoreObjectByNodeId() {
  const curNode = props.curNode
  const { graph } = curNode.model || {}
  
  if (!graph) {
    return null
  }

  dataStore.value = graph.dataStore
  const nodeId = curNode.id
  
  if (dataStore.value.dagNodeId === nodeId) {
    return dataStore.value
  }
  
  if (!Array.isArray(dataStore.value.methods)) {
    return null
  }
  
  for (const methodObj of dataStore.value.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    
    if (!Array.isArray(methodObj.method.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      
      if (!Array.isArray(stepObj.step.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }

  return null
}
</script>

<style lang="scss">
  .operation {
    width: 18px;
    height: 18px;
    padding: 2px 2px;
    margin-left: 4px;
    margin-bottom: 4px;
  }
.operation-container {
  display: flex;
  .operation {
    width: 18px;
    height: 18px;
    padding: 2px 2px;
    margin-left: 4px;
    margin-bottom: 4px;
  }
  .sort-up {
    margin-left: 0px;
  }

}

.el-table .predicate-table-cell .cell {
  padding-left: 2px;
  padding-right: 2px;
}

.el-table .predicate-table-cell .cell input {
  padding-left: 2px;
  padding-right: 2px;
}

.el-table .predicate-table-cell .cell .input-pos input {
//  width: 56px;
}

.el-table .predicate-table-cell .cell .input-name {
  padding-right: 4px;
}

.predicate-table :deep(.el-input__inner) {
  padding-left: 2px;
  padding-right: 2px;
}

.predicate-table :deep(.el-button) {
  padding-left: 2px;
  padding-right: 2px;
}

.predicate-table :deep(.el-input-group__append) {
  padding-right: 2px;
}
</style>
