<template>
  <div class="app-container" style="padding: 8px;">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item>
            <el-input v-model="query.blurry" clearable size="default" placeholder="输入内容模糊搜索" style="width: 200px;" class="filter-item" @keyup.enter="crud.toQuery" />
          </el-form-item>
          <el-form-item>
            <date-range-picker v-model="query.createTime" class="date-item" />
          </el-form-item>
          <el-form-item>
            <rrOperation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
      
    </div>
    <crudOperation :permission="permission" :crud="crud">
      <!-- 新增 -->
      <template #left>
        <el-button
          v-permission="['admin','storage:add']"
          class="filter-item"
          size="small"
          type="primary"
          :icon="Upload"
          @click="crud.toAdd"
        >上传
        </el-button>
      </template>
    </crudOperation>
    <!--表单组件-->
    <el-dialog 
      align-center 
      append-to-body 
      :close-on-click-modal="false" 
      :before-close="crud.cancelCU" 
      v-model="crud.dialogVisible" 
      :title="crud.status.add ? '文件上传' : '编辑文件'" 
      width="500px"
    >
      <el-form ref="formRef" :model="form" size="default" label-width="80px">
        <el-form-item label="文件名">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <!--   上传文件   -->
        <el-form-item v-if="crud.status.add" label="上传">
          <el-upload
            ref="uploadRef"
            :limit="1"
            :before-upload="beforeUpload"
            :auto-upload="false"
            :headers="headers"
            :on-success="handleSuccess"
            :on-error="handleError"
            :action="fileUploadApi + '?name=' + form.name"
          >
            <div class="eladmin-upload"><el-icon><Upload /></el-icon> 添加文件</div>
            <template #tip>
              <div class="el-upload__tip">可上传任意格式文件，且不超过100M</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" link @click="crud.cancelCU">取消</el-button>
          <el-button v-if="crud.status.add" :loading="loading" type="primary" @click="upload">确认</el-button>
          <el-button v-else :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </template>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="tableRef" v-loading="crud.loading" :data="crud.data" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="文件名">
        <template #default="scope">
          <el-popover
            :content="'file/' + scope.row.type + '/' + scope.row.realName"
            placement="top-start"
            title="路径"
            width="200"
            trigger="hover"
          >
            <template #reference>
              <a
                :href="baseApi + '/file/' + scope.row.type + '/' + scope.row.realName"
                class="el-link--primary"
                style="word-break:keep-all;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;color: #1890ff;font-size: 13px;"
                target="_blank"
              >
                {{ scope.row.name }}
              </a>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="path" label="预览图">
        <template #default="{row}">
          <el-image
            :src="baseApi + '/file/' + row.type + '/' + row.realName"
            :preview-src-list="[baseApi + '/file/' + row.type + '/' + row.realName]"
            fit="contain"
            lazy
            class="el-avatar"
          >
            <template #error>
              <el-icon><Document /></el-icon>
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="suffix" label="文件类型" />
      <el-table-column prop="type" label="类别" />
      <el-table-column prop="size" label="大小" />
      <el-table-column prop="operate" label="操作人" />
      <el-table-column prop="createTime" label="创建日期" />
      <el-table-column v-if="checkPer(['admin','storage:edit','storage:del'])" label="操作" width="130px" align="center" fixed="right">
        <template #default="{ row }">
          <udOperation
            :data="row"
            :permission="permission"
            :crud="crud"
            msg="确定删除该文件吗？"
          />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination :crud="crud" />
  </div>
</template>

<script setup>
import { ref, reactive, inject, computed } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Document } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'
import crudFile from '@/api/tools/localStorage'
import { useCrud } from '@/hooks/useCrud'
import DateRangePicker from '@/components/DateRangePicker'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import pagination from '@/components/crud/Pagination.vue'
import udOperation from '@/components/crud/UD.operation.vue'

// 注入 checkPer 函数
const checkPer = inject('checkPer')

// 获取 store
const store = useStore()

// refs
const formRef = ref(null)
const tableRef = ref(null)
const uploadRef = ref(null)

// 状态数据
const loading = ref(false)
const delAllLoading = ref(false)

// 初始化表单
const defaultForm = { id: null, name: '' }

// 使用 crud hook
const { crud, query, form } = useCrud({
  title: '文件',
  url: 'api/localStorage',
  crudMethod: { ...crudFile },
  defaultForm
})

// 权限配置
const permission = {
  edit: ['admin', 'storage:edit'],
  del: ['admin', 'storage:del']
}

// 计算属性
const baseApi = computed(() => store.getters.baseApi)
const fileUploadApi = computed(() => store.getters.fileUploadApi)
const headers = reactive({ 'Authorization': getToken() })

// 初始化设置
crud.optShow.add = false

// 上传文件
const upload = () => {
  uploadRef.value.submit()
}

// 上传前检查
const beforeUpload = (file) => {
  let isLt2M = true
  isLt2M = file.size / 1024 / 1024 < 100
  if (!isLt2M) {
    loading.value = false
    ElMessage.error('上传文件大小不能超过 100MB!')
  }
  form.name = file.name
  return isLt2M
}

// 上传成功处理
const handleSuccess = () => {
  crud.notify('上传成功', 'success')
  uploadRef.value.clearFiles()
  crud.status.add = 0
  crud.resetForm()
  crud.toQuery()
}

// 上传失败处理
const handleError = (e) => {
  const msg = JSON.parse(e.message)
  ElMessage({
    title: msg.message,
    type: 'error',
    duration: 2500
  })
  loading.value = false
}
</script>

<style scoped>
:deep(.el-image__error), :deep(.el-image__placeholder) {
  background: none;
}
:deep(.el-image-viewer__wrapper) {
  top: 55px;
}
.eladmin-upload {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 120px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.eladmin-upload:hover {
  border-color: #409EFF;
}
.eladmin-upload .el-icon {
  margin-right: 5px;
}
</style>
