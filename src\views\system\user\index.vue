<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 侧边部门数据 -->
      <el-col :xs="9" :sm="6" :md="5" :lg="4" :xl="4">
        <div class="head-container">
          <el-input
            v-model="deptName"
            clearable
            size="small"
            placeholder="输入部门名称搜索"
            prefix-icon="el-icon-search"
            class="filter-item"
            @input="getDeptDatas"
          />
        </div>
        <el-tree
          :data="deptDatas"
          :load="loadDeptData"
          :props="defaultProps"
          :expand-on-click-node="false"
          lazy
          @node-click="handleNodeClick"
        />
      </el-col>

      <!-- 用户数据 -->
      <el-col :xs="15" :sm="18" :md="19" :lg="20" :xl="20">
        <!-- 工具栏 -->
        <div class="head-container">
          <div v-if="crud.props.searchToggle">
            <!-- 搜索 -->
            <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
              <el-form-item label="关键字" prop="blurry">
                <el-input
                  v-model="query.blurry"
                  clearable
                  placeholder="输入名称或者邮箱搜索"
                  style="width: 200px"
                  @keyup.enter="crud.toQuery"
                />
              </el-form-item>
              <el-form-item label="状态" prop="enabled">
                <el-select
                  v-model="query.enabled"
                  clearable
                  placeholder="状态"
                  style="width: 90px"
                  @change="crud.toQuery"
                >
                  <el-option
                    v-for="item in enabledTypeOptions"
                    :key="item.key"
                    :label="item.display_name"
                    :value="item.key"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间" >
                <date-range-picker v-model="query.createTime" />
              </el-form-item>
              <el-form-item>
                <rr-operation />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <crud-operation :permission="permission" />

        <!-- 表单渲染 -->
        <el-dialog
          align-center
          :close-on-click-modal="false"
          :before-close="crud.cancelCU"
          :model-value="crud.status.cu > 0"
          :title="crud.status.title"
          width="570px"
        >
          <el-form
            ref="formRef"
            :inline="true"
            :model="form"
            :rules="rules"
            size="small"
            label-width="66px"
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" @keydown="keydown" />
            </el-form-item>
            <el-form-item label="电话" prop="phone">
              <el-input v-model.number="form.phone" />
            </el-form-item>
            <el-form-item label="昵称" prop="nickName">
              <el-input v-model="form.nickName" @keydown="keydown" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" />
            </el-form-item>
            <el-form-item label="部门" prop="dept.id">
              <el-tree-select
                v-model="form.dept.id"
                :data="depts"
                :load="loadDepts"
                :props="deptSelectProps"
                lazy
                value-key="id"
                node-key="id"
                check-strictly
                :render-after-expand="false"
                placeholder="选择部门"
                style="width: 178px"
                @change="onChangeDeptSelected"
              >
                <template #default="{ data }">
                  <span>{{ data.name }}</span>
                </template>
              </el-tree-select>
            </el-form-item>
            <el-form-item label="岗位" prop="jobs">
              <el-select
                v-model="jobDatas"
                style="width: 178px"
                multiple
                :teleported="false"
                placeholder="请选择"
                @remove-tag="deleteTag"
                @change="changeJob"
              >
                <el-option
                  v-for="item in jobs"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="性别">
              <el-radio-group v-model="form.gender" style="width: 178px">
                <el-radio value="男">男</el-radio>
                <el-radio value="女">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态">
              <el-radio-group v-model="form.enabled" :disabled="form.id === user.id">
                <el-radio
                  v-for="item in dict.data.user_status"
                  :key="item.id"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="角色" prop="roles">
              <el-select
                v-model="roleDatas"
                style="width: 437px"
                multiple
                :teleported="false"
                placeholder="请选择"
                @remove-tag="deleteTag"
                @change="changeRole"
              >
                <el-option
                  v-for="item in roles"
                  :key="item.id"
                  :disabled="level !== 1 && item.level <= level"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-form>
          
          <template #footer>
            <el-button type="text" @click="crud.cancelCU">取消</el-button>
            <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">
                确认
            </el-button>
          </template>
            
        </el-dialog>

        <!-- 表格渲染 -->
        <el-table
          ref="tableRef"
          v-loading="crud.loading"
          :data="crud.data"
          style="width: 100%"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column type="selection" width="55" :selectable="checkboxT" />
          <el-table-column prop="username" label="用户名" :show-overflow-tooltip="true" />
          <el-table-column prop="nickName" label="昵称" :show-overflow-tooltip="true" />
          <el-table-column prop="gender" label="性别" />
          <el-table-column prop="phone" label="电话" width="100" :show-overflow-tooltip="true" />
          <el-table-column prop="email" label="邮箱" width="135" :show-overflow-tooltip="true" />
          <el-table-column prop="dept" label="部门">
            <template #default="{ row }">
              <div>{{ row.dept?.name }}</div>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="enabled">
            <template #default="{ row }">
              <el-switch
                v-model="row.enabled"
                :disabled="user.id === row.id"
                active-color="#409EFF"
                inactive-color="#F56C6C"
                @change="changeEnabled(row, row.enabled)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建日期" width="135" :show-overflow-tooltip="true" />
          <el-table-column label="操作" width="115" align="center" fixed="right">
            <template #default="{ row }">
              <ud-operation
                :data="row"
                :permission="permission"
                :disabled-delete="row.id === user.id"
              />
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, inject, provide, computed, onMounted, onRenderTracked, onRenderTriggered } from 'vue';
import { useCrud } from '@/hooks/useCrud';
import { useDict } from '@/hooks/useDict';

import crudUser from '@/api/system/user'
import { getDepts, getDeptSuperior } from '@/api/system/dept';
import { getAll as getAllRoles, getLevel } from '@/api/system/role';
import { getAllJob } from '@/api/system/job';
import { isvalidPhone } from '@/utils/validate';

import rrOperation from '@/components/crud/RR.operation'
import crudOperation from '@/components/crud/CRUD.operation'
import udOperation from '@/components/crud/UD.operation'
import pagination from  '@/components/crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'
import { ElMessage, ElMessageBox } from 'element-plus';
import { useStore } from 'vuex';


onRenderTracked((event) => {
  debugger
})

onRenderTriggered((event) => {
  debugger
})

const { dict } = useDict(['user_status']);

const store = useStore();
const user = computed(() => store.getters.user);

// 数据
const formRef = ref(null)
const tableRef = ref(null)
const deptName = ref('');
const deptDatas = ref([]);
const depts = ref([]);
const jobs = ref([]);
const roles = ref([]);
const jobDatas = ref([]);
const roleDatas = ref([]);
const level = ref(3);
const enabledTypeOptions = [
  { key: 'true', display_name: '激活' },
  { key: 'false', display_name: '锁定' },
];
const deptSelectProps = {
  value: 'id',        // 节点唯一标识
  label: 'name',      // 显示名称
  children: 'children', // 子节点字段
  isLeaf: (data) => {  // 判断是否为叶子节点
    return data.leaf || (!data.hasChildren && !data.children?.length)
  }
}
// 权限管理
const  checkPer = inject('checkPer');
// 配置数据
const permission = {
  add: ['admin', 'user:add'],
  edit: ['admin', 'user:edit'],
  del: ['admin', 'user:del']
}

// 验证电话号码
const validPhone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入电话号码'))
  } else if (!isvalidPhone(value)) {
    callback(new Error('请输入正确的11位手机号码'))
  } else {
    callback()
  }
}
// 表单验证规则
const rules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    nickName: [
      { required: true, message: '请输入用户昵称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
    phone: [
      { required: true, trigger: 'blur', validator: validPhone }
    ]
  };

// 初始化 CRUD
const { crud, CRUD, form, query } = useCrud({
  title: '用户',
  url: 'api/users',
  formRef,
  tableRef,
  rules,
  query: {
    blurry: null,
    enable: null,
    createTime: null
  },
  defaultForm: {
    id: null,
    username: null,
    nickName: null,
    gender: '男',
    email: null,
    enabled: 'false',
    roles: [],
    jobs: [],
    dept: { id: null },
    deptId: 1,
    phone: null,
  },
  crudMethod: { ...crudUser }
  
});

provide('crud', crud);

// 方法
const loadDeptData = async (node, resolve) => {
  const params = { sort: 'id,desc' };
  if (node?.level !== 0) params.pid = node.data.id;
  const res = await getDepts(params);
  resolve(res.content);
};

const handleNodeClick = (data) => {
  query.deptId = data.pid === 0 ? null : data.id;
  crud.toQuery();
};

const changeEnabled = async (row, val) => {
  try {
    await ElMessageBox.confirm(
      `此操作将 ${val ? '激活' : '锁定'} ${row.username}, 是否继续？`,
      '提示',
      { type: 'warning' }
    );
    await crud.edit(row);
    ElMessage.success(`${val ? '激活' : '锁定'}成功`);
  } catch {
    row.enabled = !row.enabled;
  }
};

// 获取弹窗内角色数据
const getRoles = async () => {
  getAllRoles().then(res => {
        roles.value = res
      }).catch(() => { })
    };

    // 获取弹窗内岗位数据
 const    getJobs = async () => {
      getAllJob().then(res => {
        jobs.value = res.content
      }).catch(() => { })
    };

    // 获取权限级别
 const    getRoleLevel = async () => {
      getLevel().then(res => {
        level.value = res.level
      }).catch(() => { })
    };
// 部门数据处理
const loadDepts = async (node, resolve, reject) => {
  debugger
  try {
    let params = { enabled: true }
    if (node.level !== 0) {
      params.pid = node.data.id
    }
    debugger
    const res = await getDepts(params)
    const nodes = res.content.map(item => ({
      ...item,
      leaf: !item.hasChildren, // 根据后端返回的hasChildren设置leaf状态
      children: item.hasChildren ? [] : undefined
    }))

    // Element Plus需要手动设置节点数据
    if (node.level === 0) {
      depts.value = nodes
    } else {
      resolve(nodes)
    }
  } catch (error) {
    ElMessage.error('加载部门数据失败')
    console.error('部门加载错误:', error)
    resolve([])
  }
}

// 转换后的加载部门方法
const loadAllDepts = async () => {
  try {
    const res = await getDepts({ enabled: true })
    depts.value = res.content.map(obj => ({
      ...obj,
      children: obj.hasChildren ? null : undefined  // 保持树形结构兼容性
    }))
    debugger
  } catch (error) {
    console.error('加载部门数据失败:', error)
    // 可以添加ElMessage错误提示
    ElMessage.error('部门数据加载失败')
  }
}

const onChangeDeptSelected = (val) => {
  debugger
}

// 新增与编辑前做的操作
crud.hooks[CRUD.HOOK.afterToCU] =  (crud, form) => {

    getRoles()
    if (form.id == null) {
      loadAllDepts()
    } else {
      getSupDepts(form.dept.id)
    }
    getRoleLevel()
    getJobs()
  }

// 生命周期
onMounted(() => {
  getRoles();
  getJobs();
  getRoleLevel();
  crud.refresh();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.head-container {
  margin-bottom: 0px;
}
.filter-item {
  margin-right: 10px;
}
</style>