<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索表单 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item :label="$t('任务编号')" prop="taskNumber">
            <el-input
              v-model="query.taskNumber"
              clearable
              :placeholder="$t('任务编号')"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('任务名称')" prop="taskName">
            <el-input
              v-model="query.taskName"
              clearable
              :placeholder="$t('任务名称')"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('任务状态')" prop="status">
            <el-select
              v-model="query.status"
              clearable
              multiple
              :placeholder="$t('请选择')"
              style="width: 150px;"
            >
              <el-option
                v-for="item in dict.data.task_status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('创建时间')">
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rr-operation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crud-operation :crud="crud" :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="任务编号" prop="taskNumber">
            <el-input v-model="form.taskNumber" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="任务名称">
            <el-input v-model="form.taskName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="任务状态" prop="status">
            <el-input v-model="form.status" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="form.updateTime" style="width: 370px;" />
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="tableRef" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler" @expand-change="onExpandRow">
        <el-table-column type="selection" width="55" />
        <el-table-column type="expand" width="40">
          <template #default="props">
            <div v-for="mth in props.row.methodList" :key="mth.id">
              <div v-if="mth.type === 'MAIN'">
                <span class="row-method-name">{{ mth.name }}</span>
                <el-steps :active="getStepActive(mth)" finish-status="success" simple style="margin-bottom: 10px">
                  <el-step v-for="step in mth.steps" :key="step.id" :title="step.name" :status="getStepStatus(step)" />
                </el-steps>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="taskNumber" label="任务编号" />
        <el-table-column prop="taskName" label="任务名称" />
        <el-table-column prop="status" label="任务状态" />
        <el-table-column prop="createTime" :label="$t('创建时间')" />
        <el-table-column prop="updateTime" :label="$t('更新时间')" />
        <el-table-column v-if="checkPer(['admin','task:edit','task:del'])" :label="$t('操作')" width="150px" align="center">
          <template #default="scope">
            <el-button v-if="scope.row.status === 'SUCCESS'" type="success" :icon="Star" size="small" circle plain @click="toSetFavoriteScheduler(scope.row)" />

            <ud-operation
              :crud="crud"
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination :crud="crud" />
    
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useCrud } from '@/hooks/useCrud'
import { useDict } from '@/hooks/useDict'
import crudTask from '@/api/task'
import udOperation from '@/components/crud/UD.operation.vue'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import pagination from '@/components/crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'
import { Star, Delete } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'

// 依赖注入
const checkPer = inject('checkPer')
const store = useStore()

// CRUD配置
const { crud, CRUD, query, form } = useCrud({
  title: '任务管理',
  url: 'api/task',
  idField: 'id',
  sort: 'id,desc',
  crudMethod: { ...crudTask },
  defaultForm: {
    id: null,
    taskNumber: null,
    taskName: null,
    status: null,
    createTime: null,
    updateTime: null
  }
})

// 字典数据
const { dict } = useDict(['task_status'])


// 权限配置
const permission = {
  add: ['admin', 'task:add'],
  edit: ['admin', 'task:edit'],
  del: ['admin', 'task:del']
}

// 方法
const getStepActive = (mth) => {
  return mth.steps.findIndex(step => 
    ['RUNNING', 'IN_SCHEDULE_QUE'].includes(step.status)
  )
}

const getStepStatus = (step) => {
  const statusMap = {
    FAILED: 'error',
    SUCCESS: 'success',
    IN_SCHEDULE_QUE: 'wait',
    READY: 'wait'
  }
  return statusMap[step.status] || 'process'
}

const onExpandRow = (row) => {
  row.isExpanded = true
}

// 方法
const toSetFavoriteScheduler = async (data) => {
  try {
    await crudTask.setFavoriteScheduler(data)
    ElNotification.success({
      title: '设置评估调度时间成功！',
      duration: 2000
    })
  } catch (error) {
    ElNotification.error({
      title: '设置失败',
      message: error.message
    })
  }
}

// CRUD钩子
crud.hooks[CRUD.HOOK.beforeToAdd] = () => {
  store.dispatch('app/openTab', '/task/enter')
  return false
}

// 初始化字典
onMounted(async () => {
  dict.value = await store.dispatch('dict/load', 'task_status')
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.head-container {
  margin-bottom: 20px;
}
.row-method-name {
  font-weight: bold;
  position:absolute;
}
</style>
