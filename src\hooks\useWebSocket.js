import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

export default function useWebSocket(options) {
  // 配置项
  const {
    onMessage,          // 必需的消息处理器
    url,                // WebSocket服务地址
    reconnectInterval = 5000,  // 重连间隔
    autoReconnect = true,      // 是否自动重连
    maxReconnectAttempts = 5,  // 最大重试次数
    debug = false              // 调试模式
  } = options

  // 响应式状态
  const status = ref('DISCONNECTED') // 连接状态：CONNECTING/OPEN/CLOSED/DISCONNECTED
  const wsInstance = ref(null)       // WebSocket实例
  const reconnectAttempts = ref(0)   // 当前重连尝试次数
  const reconnectTimer = ref(null)   // 重连定时器

  // 生成唯一ID（默认实现）
  const generateUniqueId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
  }

  // 核心连接方法
  const connect = () => {
    if (status.value === 'CONNECTING') return

    status.value = 'CONNECTING'
    const finalUrl = typeof url === 'function' ? url(generateUniqueId()) : url
    
    try {
      wsInstance.value = new WebSocket(finalUrl)
      
      wsInstance.value.onopen = () => {
        if (debug) console.log('WebSocket connected:', finalUrl)
        status.value = 'OPEN'
        reconnectAttempts.value = 0
        //ElMessage.success('实时连接已建立')
      }

      wsInstance.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          onMessage?.(data)
        } catch (error) {
          console.error('WebSocket消息解析失败:', error)
        }
      }

      wsInstance.value.onclose = (event) => {
        status.value = 'CLOSED'
        if (debug) console.log('WebSocket closed:', event)
        
        if (autoReconnect && reconnectAttempts.value < maxReconnectAttempts) {
          scheduleReconnect()
        }
      }

      wsInstance.value.onerror = (error) => {
        console.error('WebSocket error:', error)
        ElMessage.error('实时连接异常')
        wsInstance.value?.close()
      }

    } catch (error) {
      console.error('WebSocket初始化失败:', error)
      status.value = 'DISCONNECTED'
    }
  }

  // 计划重连
  const scheduleReconnect = () => {
    if (reconnectTimer.value) clearTimeout(reconnectTimer.value)
    
    reconnectAttempts.value++
    reconnectTimer.value = setTimeout(() => {
      if (debug) console.log(`尝试重连 (${reconnectAttempts.value}/${maxReconnectAttempts})`)
      connect()
    }, reconnectInterval)
  }

  // 主动断开连接
  const disconnect = (code = 1000, reason = '正常关闭') => {
    if (wsInstance.value) {
      status.value = 'DISCONNECTED'
      wsInstance.value.close(code, reason)
      wsInstance.value = null
    }
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
  }

  // 发送消息
  const send = (data) => {
    if (status.value === 'OPEN' && wsInstance.value) {
      try {
        const payload = typeof data === 'string' ? data : JSON.stringify(data)
        wsInstance.value.send(payload)
        return true
      } catch (error) {
        console.error('WebSocket发送失败:', error)
        return false
      }
    }
    return false
  }

  // 组件挂载时自动连接
  onMounted(() => {
    connect()
  })

  // 组件卸载时自动清理
  onUnmounted(() => {
    disconnect()
  })

  return {
    status,          // 当前连接状态
    connect,         // 连接方法
    disconnect,      // 断开连接
    send,            // 发送消息
    wsInstance,      // WebSocket实例（高级操作）
    reconnectAttempts // 当前重连次数
  }
} 