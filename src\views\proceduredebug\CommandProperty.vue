<template>
  <div>
    <el-form ref="formRef" :model="form" size="small" label-width="80px">
      <el-form-item label="命令名称" prop="name">
        <el-input v-model="form.name" readonly/>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
          readonly
        />
      </el-form-item>
      <el-divider content-position="left">参数设置</el-divider>
      <RestfulReqEditor v-if="inputComponent ==='restful-req-map'" :cur-node="curNode"
       :form-parameters="form.parameters" :readonly="true"/>
      <div v-if="inputComponent === 'input' || inputComponent === 'position-input' || inputComponent === 'position-value-map'">
        <el-form-item v-for="item in form.parameters" :key="item.name" :label="item.name" prop="arg1">
          <el-input v-model="item.value" readonly />
        </el-form-item>
      </div>
      <el-divider content-position="left">选项</el-divider>
      <el-form-item label="执行失败动作" prop="arg1">
        <el-input v-model="form.failedThen" readonly/>
      </el-form-item>
      <el-form-item label="重试次数" prop="arg1">
        <el-input v-model="form.failedRetries" readonly/>
      </el-form-item>
      <el-form-item label="执行前点位状态检测" prop="positionCheckingStatus">
        <el-input v-model="form.positionCheckingStatus" readonly/>
      </el-form-item>
      <el-form-item label="执行前点位状态设置" prop="positionPreExecutionStatus">
        <el-input v-model="form.positionPreExecutionStatus" readonly/>
      </el-form-item>
      <el-form-item label="执行后执行脚本" prop="postExecution">
        <el-input v-model="form.postExecution" readonly/>
      </el-form-item>
      <el-form-item label="执行后延迟(ms)" prop="executedDelay">
        <el-input v-model="form.executedDelay" readonly/>
      </el-form-item>
      <el-form-item label="执行后点位状态设置" prop="positionExecutedStatus">
        <el-input v-model="form.positionExecutedStatus" readonly/>
      </el-form-item>
      <el-form-item label="备注提示" prop="comment">
        <el-input v-model="form.comment" readonly/>
      </el-form-item>
      <el-divider content-position="left">调试信息</el-divider>
      <el-form-item label="运行状态" prop="status">
        <el-tag :type="form.status=='FAILED'?'danger':'primary'">{{ form.status }}</el-tag>
      </el-form-item>
      <el-form-item label="运行信息" prop="message">
        <el-text>{{ form.message }}</el-text>
      </el-form-item>
     
      <el-divider content-position="left">节点运行日志</el-divider>
      <TaskLogViewer :data-filter="dataFilter"/>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-entry">
          <span class="log-task-tag" :style="getTaskTagStyle(log.taskId)" />
          <span class="log-timestamp" :style="getTextStyleByStatus(log.status)">{{ getTruncatedTime( log.updateTime ) }}</span>
          <span class="log-message" :style="getTextStyleByStatus(log.status)">{{ log.name + (log.message?(': '+log.message):'') }}</span>
          <span class="log-status" :style="getTextStyleByStatus(log.status)">{{ log.status }}</span>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, inject, computed } from 'vue'
import { Node } from '@antv/x6'
import { RefreshLeft, Close, DArrowRight } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'
import crudTask from '@/api/task'
import { useDict } from '@/hooks/useDict'

import RestfulReqEditor from '@/views/components/x6/RestfulReqEditor.vue'
import TaskLogViewer from '@/views/components/TaskLogViewer.vue'

// 定义props
const props = defineProps({
  curNode: {
    default: () => ({}),
    type: Node
  }
})

// 注入依赖
const getDataStore = inject('getDataStore')

// 字典数据
// 使用字典
const { dict } = useDict(['failed_then_enum', 'position_status', 'device_types'])

// 默认表单数据
const defaultForm = { 
  id: null, 
  name: null, 
  description: null, 
  commandId: null, 
  parameters: [],
  failedThen: '', 
  failedRetries: 0,
  positionCheckingStatus: null,
  positionPreExecutionStatus: null,
  postExecution: null,
  executedDelay: null,
  positionExecutedStatus: null,
  comment: null,
  status: '',
  message: '',
  deleteFlag: null, 
  createBy: null, 
  createTime: null, 
  updateBy: null, 
  updateTime: null 
}

// 响应式数据
const formRef = ref(null)
const form = reactive({ ...defaultForm })
const rules = reactive({
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
})
const dataStore = ref({})
const initing = ref(false)
const inputComponent = ref('input')
const logs = ref([])
const commandsOptions = ref([{ label: '系统等待', value: 1 }])
const dataFilter = reactive({
                    nodeType: 'COMMAND',
                    id: 0
                  })
// 计算属性
const taskId = computed(() => dataStore.value.id) 
// 监听数据变化
watch(dataStore, (newVal, oldVal) => {
  transformNodeToFormData()
  console.log(`dataStore changed from ${oldVal} to ${newVal}`)
}, { deep: true })

watch(() => props.curNode, (newVal, oldVal) => {
  transformNodeToFormData()
  console.log(`curNode changed from ${oldVal} to ${newVal}`)
}, { deep: false })

// 生命周期钩子
onMounted(() => {
  console.log('CommandProperty created..')
  transformNodeToFormData()
})

// 方法定义
function init() {
  initing.value = true
}

const getConfigTemplate = (command) => {
  if (!command) return null
  let parameterTemplate = null
  if (command.commandType === 'PROXY') {
    parameterTemplate = command.proxyInstanceCmd?.command?.parameterTemplate
  }else{
    parameterTemplate = command.command?.parameterTemplate
  }

  if (!parameterTemplate) return null
  
  return JSON.parse(parameterTemplate)
}

const getInstConfig = (storeCmdData, configTemplate) => {
  let instConfig = storeCmdData.parameter
  if (!instConfig) {
    instConfig = storeCmdData.command?.parameter
  }
    if (instConfig && configTemplate.children) {
      instConfig = JSON.parse(instConfig)
    }
    return instConfig
}
function transformNodeToFormData() {
  const curNode = props.curNode
  const storeCmdData = curNode.getData()
  if (!storeCmdData) return
  Object.assign(form, defaultForm)
  Object.assign(form, storeCmdData)
  
  form.parameters.length = 0
  const configTemplate = getConfigTemplate(storeCmdData.command)
  if (configTemplate) {
    let instConfig = getInstConfig( storeCmdData, configTemplate)

    if (configTemplate.inputComponent) {
      inputComponent.value = configTemplate.inputComponent
    }
    
    if (configTemplate.children) {
      configTemplate.children.forEach(item => {
        form.parameters.push({
          key: item.key,
          name: item.name,
          value: (!instConfig) ? '' : instConfig[item.key]
        })
      })
    } else {
      form.parameters.push({
        name: configTemplate.name,
        value: instConfig
      })
    }
  }
  
  dataFilter.id = storeCmdData.id
}

function doRetryCommand() {
  const data = props.curNode.getData()
  crudTask.doRetryCommand(data).then(res => {
    ElNotification({
      title: '执行成功',
      type: 'success',
      duration: 2000
    })
  })
}

function doNextCommand() {
  dataStore.value = getDataStore()
  const data = { taskId: dataStore.value.id }
  crudTask.doNextCommand(data).then(res => {
    ElNotification({
      title: '执行成功',
      type: 'success',
      duration: 2000
    })
  })
}

function doSkipCommand() {
  const data = props.curNode.getData()
  crudTask.doSkipCommand(data).then(res => {
    ElNotification({
      title: '执行成功',
      type: 'success',
      duration: 2000
    })
  })
}

function getTaskTagStyle(taskId) {
  // 实现任务标签样式逻辑
  return {}
}

function getTextStyleByStatus(status) {
  // 实现根据状态获取文本样式的逻辑
  return {}
}

function getTruncatedTime(time) {
  // 实现时间截断逻辑
  return time
}
</script>

<style lang="scss">
.log-container {
  max-height: 300px;
  overflow-y: auto;
  
  .log-entry {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    
    .log-task-tag {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 5px;
    }
    
    .log-timestamp {
      margin-right: 10px;
      font-size: 12px;
    }
    
    .log-message {
      flex: 1;
    }
    
    .log-status {
      margin-left: 10px;
      font-weight: bold;
    }
  }
}
</style>
