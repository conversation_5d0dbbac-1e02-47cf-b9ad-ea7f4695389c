import { reactive } from 'vue';
import { get as getDictDetail } from '@/api/system/dictDetail';

export default class Dict {
  constructor(dict) {
    this.dict = reactive(dict); // 确保 `dict` 是响应式对象
  }

  async init(names, completeCallback) {
    if (names === undefined || names === null) {
      throw new Error('need Dict names');
    }

    const ps = [];
    names.forEach(n => {
      // 初始化响应式属性
      if (!this.dict.dict[n]) this.dict.dict[n] = {};
      if (!this.dict.label[n]) this.dict.label[n] = {};
      if (!this.dict[n]) this.dict[n] = [];

      ps.push(
        getDictDetail(n).then(data => {
          this.dict[n].splice(0, 0, ...data.content); // 动态更新数组
          data.content.forEach(d => {
            this.dict.dict[n][d.value] = d; // 动态设置对象属性
            this.dict.label[n][d.value] = d.label;
          });
        })
      );
    });

    await Promise.all(ps);
    completeCallback();
  }
}