import { createRouter, createWebHashHistory  } from 'vue-router';
import Layout from '../layout/index.vue';

// 定义路由表
export const constantRouterMap = [
  {
    path: '/login',
    meta: { title: '登录', noCache: true },
    component: () => import('@/views/login.vue'), // 动态导入组件
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/features/404.vue'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/features/401.vue'),
    hidden: true,
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import('@/views/features/redirect.vue'),
      },
    ],
  },
  {
    path: '/refresh',
    name: 'Refresh',
    component: () => import('@/views/refresh.vue'),
    meta: { title: '刷新界面' },
    hidden: true,
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/home.vue'),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'index', affix: true, noCache: true },
      },
    ],
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'center',
        component: () => import('@/views/system/user/center.vue'),
        name: '个人中心',
        meta: { title: '个人中心' },
      },
    ],
  },
];

// 创建路由实例
const router = createRouter({
  mode: 'hash',
  history: createWebHashHistory(), // 使用 HASH 历史模式
  scrollBehavior: () => ({ y: 0 }), // 滚动行为
  routes: constantRouterMap, // 加载路由表
});

export default router;