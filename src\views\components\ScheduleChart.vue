<template>
    <div id="gantt-chart" style="width: 100%; "></div>
  </template>
  
  <script>
  import * as echarts from 'echarts';
  
  export default {
    name: 'ScheduleChart',
    data() {
      return {
        chart: null,
        tasks: [
          { name: 'Task 1', startDate: '2023-06-01', endDate: '2023-06-05', progress: 0.5 },
          { name: 'Task 2', startDate: '2023-06-03', endDate: '2023-06-07', progress: 0.2 },
          { name: 'Task 3', startDate: '2023-06-06', endDate: '2023-06-12', progress: 0.8 },
          // 更多任务
        ]
      };
    },
    mounted() {
      this.initChart();
    },
    methods: {
      initChart() {
        this.chart = echarts.init(document.getElementById('gantt-chart'));
  
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: (params) => {
              const task = params.data;
              return `${task[0]}<br/>Start: ${task[1]}<br/>End: ${task[2]}<br/>Progress: ${task[3] * 100}%`;
            }
          },
          xAxis: {
            type: 'time',
            min: '2023-06-01',
            max: '2023-06-15',
            axisLabel: {
              formatter: '{yyyy}-{MM}-{dd}'
            }
          },
          yAxis: {
            type: 'category',
            data: this.tasks.map(task => task.name),
            axisTick: { alignWithLabel: true }
          },
          series: [
            {
              type: 'custom',
              renderItem: (params, api) => {
                const categoryIndex = api.value(0);
                const start = api.coord([api.value(1), categoryIndex]);
                const end = api.coord([api.value(2), categoryIndex]);
                const width = end[0] - start[0];
  
                return {
                  type: 'rect',
                  shape: {
                    x: start[0],
                    y: start[1] - 10,
                    width: width,
                    height: 20
                  },
                  style: api.style({
                    fill: '#DDB30B'
                  })
                };
              },
              encode: {
                x: [1, 2],
                y: 0
              },
              data: this.tasks.map(task => [
                task.name,
                task.startDate,
                task.endDate,
                task.progress
              ])
            }
          ]
        };
  
        this.chart.setOption(option);
      }
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose();
      }
    }
  };
  </script>
  
  <style scoped>
  #gantt-chart {
    width: 100%;
    height: 400px;
  }
  </style>