<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.blurry"
          clearable
          size="small"
          placeholder="输入店铺名搜索"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <date-range-picker v-model="query.createTime" class="date-item" />
        <el-select
          v-model="query.enabled"
          clearable
          size="small"
          placeholder="状态"
          class="filter-item"
          style="width: 90px"
          @change="crud.toQuery"
        >
          <el-option
            v-for="item in enabledTypeOptions"
            :key="item.key"
            :label="item.display_name"
            :value="item.key"
          />
        </el-select>
        <rrOperation />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission">
        <el-button
          slot="left"
          class="filter-item"
          size="mini"
          type="primary"
          icon="el-icon-edit"
          :disabled="crud.selections.length === 0"
          @click="batchAudit(crud.selections)"
        >批量审核</el-button>
        <el-button
          slot="left"
          class="filter-item"
          size="mini"
          type="danger"
          icon="el-icon-delete"
          :disabled="crud.selections.length === 0"
          @click="batchReject(crud.selections)"
        >批量驳回</el-button>
      </crudOperation>
      <el-dialog :visible.sync="auditDetail" title="批量驳回">
        <el-form ref="shopform" :model="shopform">
          <el-form-item label="店铺驳回原因" prop="auditDetail">
            <el-input v-model="shopform.auditDetail" placeholder="驳回原因" style="width: 350px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" @click="saveReason(crud.selections)">确认</el-button>
        </div>
      </el-dialog>
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="rejectReason" :title="批量驳回" width="350px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="店铺驳回原因">
            <el-input v-model="form.rejectReason" placeholder="驳回原因" style="width: 350px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="shopId" label="店铺ID" />
        <el-table-column prop="areaId" label="店铺所属区域" />
        <el-table-column prop="shopName" label="店铺名称" />
        <el-table-column prop="address" label="地址" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="auditStatus" label="审核状态" />
        <el-table-column prop="createBy" label="创建人" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column v-if="checkPer(['admin','shopInfo:edit','shopInfo:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudShopInfo from '@/api/shopInfo'
import CRUD, { presenter, header, form, crud } from '@/components/crud/crud'
import rrOperation from '@/components/crud/RR.operation'
import crudOperation from '@/components/crud/crud.operation'
import udOperation from '@/components/crud/UD.operation'
import pagination from '@/components/crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'

const defaultForm = { shopId: null, areaId: null, tenantId: null, shopName: null, address: null, latitude: null, longitude: null, description: null, auditStatus: null, onlineStatus: null, createBy: null, createTime: null, updateBy: null, updateTime: null, auditDetail: null }
export default {
  name: 'ShopInfo',
  components: { pagination, crudOperation, udOperation, rrOperation, DateRangePicker },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '店铺管理', url: 'api/shopInfo', idField: 'shopId', sort: 'shopId,desc', crudMethod: { ...crudShopInfo }})
  },
  data() {
    return {
      shopform: {
        auditDetail: ''
      },
      permission: {
        add: ['admin', 'shopInfo:add'],
        edit: ['admin', 'shopInfo:edit'],
        del: ['admin', 'shopInfo:del']
      },
      rules: {
        areaId: [
          { required: true, message: '店铺所属区域ID不能为空', trigger: 'blur' }
        ],
        tenantId: [
          { required: true, message: '租户ID不能为空', trigger: 'blur' }
        ]
      },
      // 驳回原因显示弹框的标识
      auditDetail: false
    }
  },
  created() {
    this.crud.optShow = { add: false, edit: false, del: false, download: true }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    batchAudit(datas) {
      this.$confirm(`确认批量审核选中的${datas.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        datas.forEach(data => {
          // 已审核
          if (data.onlineStatus === '1') {
            return
          }
        })
        crudShopInfo.batchAudit(datas).then((res) => {
          this.crud.notify(res ? '批量审核成功' : '批量审核失败', res ? 'success' : 'error')
        }).catch(err => {
          datas.enabled = !datas.enabled
          console.log(err.datas.message)
        })
      }).catch(() => {
      })
    },
    batchReject(datas) {
      this.$confirm(`确认批量驳回选中的${datas.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.auditDetail = true
      }).catch(() => {
      })
    },
    saveReason(datas) {
      const shop = {
        auditDetail: this.shopform.auditDetail
      }
      const selections = []
      const shopApproveLog = {}
      if (datas.length > 0) {
        datas.forEach(data => {
          this.$set(shopApproveLog, 'auditDetail', shop.auditDetail)
          this.$set(shopApproveLog, 'shopId', data.shopId)
          this.$set(shopApproveLog, 'auditStatus', data.auditStatus)
          selections.push(shopApproveLog)
        })
      }
      crudShopInfo.batchSaveShopApprovelLog(selections).then((res) => {
        this.crud.notify(res ? '批量驳回原因成功' : '批量驳回原因失败', res ? 'success' : 'error')
      }).catch(err => {
        this.$message.error(err.selections.message)
      })
    }
  }
}

</script>

<style scoped>

</style>
