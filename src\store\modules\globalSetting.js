import crudSetting from '@/api/setting'

const globalSetting = {
  state: {
    globalSetting: {}
  },

  mutations: {
    SET_GLOBAL_SETTING: (state, setting) => {
      state.globalSetting = setting
    }
  },

  actions: {

    // 获取最近设置信息
    setGlobalSettingLatestOne({ commit }) {
      return new Promise((resolve, reject) => {
        crudSetting.queryLatestOne(globalSetting.state.globalSetting).then(res => {
          if (res && res.id) {
            const settingObj = JSON.parse(res.globalSettings)
            commit('SET_GLOBAL_SETTING', settingObj) 
          }
          
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    }

  }
}

export default globalSetting
