import request from '@/utils/request'

export function getShoppes(params) {
  return request({
    url: 'api/shopInfo',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/shopInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/shopInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/shopInfo',
    method: 'put',
    data
  })
}

export function batchAudit(data) {
  return request({
    url: 'api/shopInfo/batchAuditShopInfo',
    method: 'post',
    data
  })
}

export function batchSaveShopApprovelLog(data) {
  return request({
    url: '/api/shopApproveLog/batchSaveShopApprovelLog',
    method: 'post',
    data
  })
}

export default { add, edit, del, getShoppes, batchAudit, batchSaveShopApprovelLog }
