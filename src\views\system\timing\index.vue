<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item label="任务名称" prop="jobName">
            <el-input
              v-model="query.jobName"
              clearable
              size="default"
              placeholder="输入任务名称搜索"
              style="width: 200px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rrOperation />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <crudOperation :permission="permission">
      <!-- 任务日志 -->
      <template #right>
        <el-button
          v-if="checkPer(['admin','timing:edit'])"
          class="filter-item"
          size="small"
          type="info"
          @click="doLog"
        >
          <el-icon><Tickets /></el-icon>
          日志
        </el-button>
      </template>
    </crudOperation>
    <Log ref="logRef" v-model="logVisible" />
    
    <!--表单组件-->
    <el-dialog
      align-center
      v-model="dialogVisible"
      :title="crud.status.title"
      width="730px"
      append-to-body
      :close-on-click-modal="false"
      @close="crud.cancelCU"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        inline
        size="default"
        label-width="100px"
      >
        <el-form-item label="任务名称" prop="jobName">
          <el-input v-model="form.jobName" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="form.description" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="Bean名称" prop="beanName">
          <el-input v-model="form.beanName" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="执行方法" prop="methodName">
          <el-input v-model="form.methodName" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="Cron表达式" prop="cronExpression">
          <el-input v-model="form.cronExpression" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="子任务ID">
          <el-input
            v-model="form.subTask"
            placeholder="多个用逗号隔开，按顺序执行"
            style="width: 220px;"
          />
        </el-form-item>
        <el-form-item label="任务负责人" prop="personInCharge">
          <el-input v-model="form.personInCharge" style="width: 220px;" />
        </el-form-item>
        <el-form-item label="告警邮箱" prop="email">
          <el-input
            v-model="form.email"
            placeholder="多个邮箱用逗号隔开"
            style="width: 220px;"
          />
        </el-form-item>
        <el-form-item label="失败后暂停">
          <el-radio-group v-model="form.pauseAfterFailure" style="width: 220px">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="任务状态">
          <el-radio-group v-model="form.isPause" style="width: 220px">
            <el-radio :label="false">启用</el-radio>
            <el-radio :label="true">暂停</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="参数内容">
          <el-input
            v-model="form.params"
            type="textarea"
            :rows="4"
            style="width: 556px;"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </template>
    </el-dialog>

    <!--表格渲染-->
    <el-table
      ref="tableRef"
      v-loading="crud.loading"
      :data="crud.data"
      style="width: 100%;"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column :selectable="checkboxT" type="selection" width="55" />
      <el-table-column :show-overflow-tooltip="true" prop="id" label="任务ID" />
      <el-table-column :show-overflow-tooltip="true" prop="jobName" label="任务名称" />
      <el-table-column :show-overflow-tooltip="true" prop="beanName" label="Bean名称" />
      <el-table-column :show-overflow-tooltip="true" prop="methodName" label="执行方法" />
      <el-table-column :show-overflow-tooltip="true" prop="params" label="参数" />
      <el-table-column :show-overflow-tooltip="true" prop="cronExpression" label="cron表达式" />
      <el-table-column :show-overflow-tooltip="true" prop="isPause" width="90px" label="状态">
        <template #default="scope">
          <el-tag :type="scope.row.isPause ? 'warning' : 'success'">
            {{ scope.row.isPause ? '已暂停' : '运行中' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="description" width="150px" label="描述" />
      <el-table-column :show-overflow-tooltip="true" prop="createTime" width="136px" label="创建日期" />
      <el-table-column
        v-if="checkPer(['admin','timing:edit','timing:del'])"
        label="操作"
        width="170px"
        align="center"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="checkPer(['admin','timing:edit'])"
            size="small"
            type="primary"
            link
            @click="crud.toEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="checkPer(['admin','timing:edit'])"
            size="small"
            type="primary"
            link
            @click="execute(scope.row.id)"
          >
            执行
          </el-button>
          <el-button
            v-if="checkPer(['admin','timing:edit'])"
            size="small"
            type="primary"
            link
            @click="updateStatus(scope.row.id, scope.row.isPause ? '恢复' : '暂停')"
          >
            {{ scope.row.isPause ? '恢复' : '暂停' }}
          </el-button>
          <el-popconfirm
            v-if="checkPer(['admin','timing:del'])"
            :title="'确定停止并删除该任务吗？'"
            @confirm="delMethod(scope.row.id)"
          >
            <template #reference>
              <el-button
                size="small"
                type="primary"
                link
                :loading="delLoading"
              >
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script setup>
import { ref, computed, provide, inject, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Tickets } from '@element-plus/icons-vue'
import { useCrud } from '@/hooks/useCrud'
import crudJob from '@/api/system/timing'
import Log from './log.vue'
import pagination from '@/components/Crud/Pagination.vue'
import crudOperation from '@/components/Crud/crud.operation.vue'
import rrOperation from '@/components/Crud/RR.operation.vue'
import DateRangePicker from '@/components/DateRangePicker/index.vue'

// 注入权限检查函数
const checkPer = inject('checkPer')

// refs
const tableRef = ref(null)
const formRef = ref(null)
const logRef = ref(null)

const logVisible = ref(false)

// 数据
const delLoading = ref(false)

// 使用 crud hook
const { crud, CRUD, query, form, rules } = useCrud({
  title: '定时任务',
  url: 'api/jobs',
  crudMethod: { ...crudJob },
  defaultForm: {
    id: null,
    jobName: null,
    subTask: null,
    beanName: null,
    methodName: null,
    params: null,
    cronExpression: null,
    pauseAfterFailure: true,
    isPause: false,
    personInCharge: null,
    email: null,
    description: null
  },
  formRef,
  tableRef,
  rules: {
    jobName: [
      { required: true, message: '请输入任务名称', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '请输入任务描述', trigger: 'blur' }
    ],
    beanName: [
      { required: true, message: '请输入Bean名称', trigger: 'blur' }
    ],
    methodName: [
      { required: true, message: '请输入方法名称', trigger: 'blur' }
    ],
    cronExpression: [
      { required: true, message: '请输入Cron表达式', trigger: 'blur' }
    ],
    personInCharge: [
      { required: true, message: '请输入负责人名称', trigger: 'blur' }
    ]
  }
})

// 提供 crud 给子组件使用
provide('crud', crud)

// 计算属性
const dialogVisible = computed(() => crud.status.cu > 0)

// 权限配置
const permission = {
  add: ['admin', 'timing:add'],
  edit: ['admin', 'timing:edit'],
  del: ['admin', 'timing:del']
}

// 方法
const checkboxT = (row) => {
  return row.id !== 1
}

const execute = async (id) => {
  try {
    await crudJob.execution(id)
    crud.notify('执行成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
  } catch (err) {
    console.error(err.response.data.message)
  }
}

const updateStatus = async (id, status) => {
  if (status === '恢复') {
    updateParams(id)
  }
  try {
    await crudJob.updateIsPause(id)
    crud.toQuery()
    crud.notify(status + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
  } catch (err) {
    console.error(err.response.data.message)
  }
}

const updateParams = (id) => {
  console.log(id)
}

const delMethod = async (id) => {
  delLoading.value = true
  try {
    await crudJob.del([id])
    delLoading.value = false
    crud.dleChangePage(1)
    crud.delSuccessNotify()
    crud.toQuery()
  } catch (err) {
    delLoading.value = false
    console.error(err)
  }
}

const doLog = () => {
  logVisible.value = true
 // logRef.value.dialog = true
 // logRef.value.doInit()
}

// 生命周期钩子
onMounted(() => {
  crud.refresh()
})
</script>

<style lang="scss" scoped>
.filter-item {
  margin-right: 10px;
}

.date-item {
  margin-right: 10px;
}
</style>
