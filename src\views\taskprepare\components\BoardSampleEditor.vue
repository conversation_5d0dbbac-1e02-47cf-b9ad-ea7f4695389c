<template>
  <div class="main-form-container">

    <div class="main-form-body">
      <el-form ref="formRef" :model="form" :rules="rules" size="small"  label-width="80px"  :inline="true">
        <el-form-item label="板卡编号" prop="tag">
          <TagInput v-model="form.tag" />
        </el-form-item>
        <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
        <crud-operation :permission="permission" :crud="crud" >
          <template #left>
            <el-button
              v-permission="permission.add"
              size="small"
              type="primary"
              plain
              :icon="Plus"
              @click="addTaskPrepare"
            >
              {{ $t('新增行') }}
            </el-button>
          </template>
        </crud-operation>
        <el-table
          ref="tableRef"
          v-loading="taskPrepareLoading"
          :data="crud.data"
          size="small"
          highlight-current-row
          @current-change="handleCurrentChange"
          @selection-change="crud.selectionChangeHandler"
          style="height:240px"
        >
          <el-table-column type="selection" width="55" :selectable="isRowSelectable" />
          <el-table-column prop="posIndex" label="板位" width="60" >
            <template #default="scope">
              <el-popover
                placement="left"
                :width="200"
                :height="100"
                trigger="click"
                @show="showPopoverBoard(scope.row)"
                @hide="hidePopoverBoard(scope.row)"
              >
              <SampleBoard :board="popoverBoard" @update:selectedPositions="(selectedPositions)=>{scope.row.posIndex=selectedPositions[0]}" />
              <template #reference>
                <el-button circle >{{ scope.row.posIndex != null ? scope.row.posIndex+1 : null }}</el-button>
              </template>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="sampleName" label="样本名称">
            <template #default="scope">
              <el-select
                v-model="scope.row.sample"
                value-key="id"
                size="small"
                allow-create
                filterable
                reserve-keyword
                default-first-option
                placeholder="请选择"
              >
                <el-option
                  v-for="item in sampleOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="schedureName" label="流程名称">
            <template #default="scope">
              <el-select
                v-model="scope.row.procedure"
                value-key="id"
                size="small"
                filterable
                default-first-option
                placeholder="请选择"
              >
                <el-option
                  v-for="item in procedureOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                />
              </el-select>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="80" />
          <el-table-column v-if="checkPer(['admin','sample:edit','sample:del'])" :label="$t('操作')" width="80px" align="center">
            <template #default="scope">
              <el-popover
                v-model:visible="scope.row.deletePopVisible"
                placement="top"
                width="180"
                trigger="manual"
                @show="onPopoverShow(scope.row)"
                @hide="onPopoverHide(scope.row)"
              >
                <template #default>
                  <p>{{ '请确认删除该条记录？' }}</p>
                  <div style="text-align: right; margin: 0">
                    <el-button size="small" text @click="doCancel(scope.row)">取消</el-button>
                    <el-button
                      :loading="crud.dataStatus[crud.getDataId(scope.row)]?.delete === 2"
                      type="primary"
                      size="small"
                      @click="crud.doDelete(scope.row)"
                    >
                      确定
                    </el-button>
                  </div>
                </template>
                <template #reference>
                  <el-button
                    :icon="Delete"
                    class="operation"
                    type="danger"
                    size="small"
                    @click="toDelete(scope.row)"
                  />
                </template>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>

    <div class="main-form-footer">
      <div class="btn-container">
       
        <el-button
          size="default"
          type="primary"
          :icon="VideoPlay"
          :disabled="inputDisabled"
          :loading="batchSubmitLoading"
          @click="handleAnalyseStart"
        >
          {{ $t('提交任务') }}
        </el-button>
        
        <el-button
          size="default"
          type="primary"
          text
          @click="saveDraft"
        >
          {{ $t('保存草稿') }}
        </el-button>
        <el-button size="default" text @click="cancelAnalyse">取 消</el-button>
        <el-switch v-model="form.debugMode" inline-prompt
          active-text="调试"
          inactive-text="调试" />
      </div>
      <div class="message-container">
        <span :class="['span-message', messageClass]"><el-icon class="question-icon"><InfoFilled /></el-icon>点击“提交任务”按钮，请先确保进样托盘点位对应样品正确，并把托盘放置到指定位置。</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, version, defineModel,defineEmits } from 'vue'
import { useStore } from 'vuex'
import { debounce } from '@/utils'
import { useCrud } from '@/hooks/useCrud'
import crudTaskPrepare from '@/api/taskPrepare'
import { queryProceduresForSelection } from '@/api/procedure'
import { querySamplesForSelection } from '@/api/sample'
import { ElMessage, ElNotification as Notification } from 'element-plus'
import { Delete, VideoPlay, InfoFilled, Plus } from '@element-plus/icons-vue'
import pagination from '@/components/crud/Pagination'
import CrudOperation from '@/components/crud/CRUD.Operation.vue'

import TagInput from './TagInput.vue'
import SampleBoard from '@/views/samplemgr/components/SampleBoard.vue'

// 接收 props
const props = defineProps({
  board: {
    type: Object,
    default: null
  }
})
const emit = defineEmits(['onTaskSubmitted', 'onBoardUpdated'])


// 组件引用
const store = useStore()
const tableRef = ref(null)
const formRef = ref(null)

// 响应式状态
const sampleEditorVisible = ref(false)
const procedureOptions = ref([{ label: '新建流程', value: 0 }])
const sampleOptions = ref([])
const message = ref(' ')
const messageClass = ref('')
const inputDisabled = ref(false)
const batchSubmitLoading = ref(false)
const taskPrepareView = ref({})
const currentDeletingRow = ref({})
const taskPrepareLoading = ref(false)

const sampleEditorSelected = ref('printer');

const permission = {
  add: ['admin', 'procedure:add'],
  edit: ['admin', 'procedure:edit'],
  del: ['admin', 'procedure:del']
}
// 表单验证规则
const rules = {
  tag: [
    { required: true, message: 'TAG不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}
// CRUD配置
const { crud, CRUD, form } = useCrud({
  title: '任务启动准备',
  url: 'api/taskPrepare/queryTaskPrepareByTag',
  query: {
    rfTag: ()=> { return props.board.tag}
  },
  idField: 'id',
  sort: 'posIndex,asc',
  queryOnPresenterCreated: true,
  crudMethod: { ...crudTaskPrepare },
  optShow: { add: false, edit: true, del: true, download: false },
  formRef,
  tableRef
})

const popoverBoard = reactive({ 
  tag: '', 
  width:170, 
  height:80,
  rows:2,
  columns:4, 
  positions: [] 
})
const showPopoverBoard = (row) => {
  if (!row) return
  popoverBoard.positions = props.board.positions
  popoverBoard.tag = row.rfTag
  popoverBoard.selectedPositions = [row.posIndex]
}
const hidePopoverBoard = (row) => {
  if (!row) return
  if (popoverBoard.selectedPositions?.length === 0) return
 // row.posIndex = popoverBoard.selectedPositions[0]
}

// 计算属性
const backgroundStyle = computed(() => ({
  backgroundImage: `url(${require('@/assets/icons/svg/status-btn-bg.svg')})`
}))

const buttonIconStyle = computed(() => ({
  backgroundImage: `url(${store.getters.imageAccessUrl}%E5%90%AF%E5%8A%A8%E5%88%86%E6%9E%90-20240620031632806.svg)`
}))

// 生命周期
onMounted(async () => {
  sampleOptions.value = await querySamplesForSelection({ name: null })
  procedureOptions.value = await queryProceduresForSelection()

  form.tag = props.board.tag

  crud.refresh()
})

// 方法
const isRowSelectable = (row) => !row.inQueue

const handleAnalyseStart = () => {
   doSubmitFactory()
}

const doSubmitFactory = async () => {
debugger
  const valid = await formRef.value?.validate();
  if (!valid) return;
  
  crud.data.forEach(tp => {
    tp.rfTag = form.tag
    if (tp.sample && typeof tp.sample === 'string') {
      tp.sample = {
        id: null,
        name: tp.sample,
        number: tp.sample
      }
    }
  });

  batchSubmitLoading.value = true
  inputDisabled.value = true
  taskPrepareView.value.mode = 'BOARD'
  taskPrepareView.value.taskPrepareDtos = crud.data
  taskPrepareView.value.debugMode = form.debugMode
  debugger
  try {
    await crudTaskPrepare.submitBatchTasks(taskPrepareView.value)
    crud.refresh()
    Notification({
      title: '任务提交成功',
      type:'success',
      duration: 2500
    })
    emit('onTaskSubmitted')
    console.log(new Date() + ': TaskPrepare was submitted done.')

  } finally {
    batchSubmitLoading.value = false
    inputDisabled.value = false
  }
}

const saveDraft = async () => {
  
  crud.data.forEach(tp => {
    tp.rfTag = form.tag
    if (tp.sample && typeof tp.sample === 'string') {
      tp.sample = {
        id: null,
        name: tp.sample,
        number: tp.sample
      }
    }
  });

  inputDisabled.value = true
  taskPrepareView.value.taskPrepareDtos = crud.data
  
  try {
    const res = await crudTaskPrepare.batchSaveTaskPrepares(crud.data)
    sampleOptions.value = await querySamplesForSelection({ name: null })
    crud.refresh()
    Notification({
      title: '保存草稿成功',
      type:'success',
      duration: 2500
    })
    console.log(new Date() + ': TaskPrepare was saved done.')
    // getCurrentTaskPrepareView()
  } finally {
    inputDisabled.value = false
  }
}

const onSampleEditSuccess = (form) => {
  sampleEditorVisible.value = false;
  crud.refresh();
};

const addTaskPrepare = async (taskprepares) => {
  if (crud.data.length >= props.board.rows * props.board.columns) {

    Notification({
          title: '当前板卡已经满了',
          type:'error',
          duration: 2500
        })
    return
  }
  if (crud.data.length > 0){
    const newLine = { ... crud.data[crud.data.length-1]}
    newLine.posIndex++
    newLine.id = null
    crud.data.push(newLine)
    tableRef.value.setCurrentRow(newLine)
    return
  }
  const defaultProc = procedureOptions.value[0] || { id: null, name: null }
  const newLine = { procedure: defaultProc, rfTag: null, posIndex: 0, status: 'DRAFT', selected: true }
  const latestTp = await crudTaskPrepare.findLatestTaskPrepare()
  if (latestTp && latestTp.id){
    Object.assign(newLine, latestTp)
    newLine.id = null
    newLine.status = 'DRAFT'
    newLine.posIndex = 0
  }
  
  crud.data.push(newLine)
  tableRef.value.setCurrentRow(newLine)
}

const handleCurrentChange = (currentRow, oldCurrentRow) => {
  // currentRow.selected = true
  // oldCurrentRow.selected = false
}

const deleteTaskPrepare = (curTaskPrepare) => {
  crud.toDelete(curTaskPrepare)
}

const cancelAnalyse = () => {
  message.value = ''
  inputDisabled.value = false
  taskPrepareView.value = {}
  crudTaskPrepare.cancelCurrentTaskPrepareView({ id: taskPrepareView.value.id }).then(res => {
    ElMessage.success('撤销任务成功')
    console.log(new Date() + ': TaskPrepare was cancelled done.')
    getCurrentTaskPrepareView()
  }).catch((error) => {
    console.log('doSubmitTrial error=' + error)
    ElMessage.error('撤销任务失败')
  })
}

const onSampleSelect = (keyword) => {
  sampleOptions.value = []
  sampleLoading.value = true
  debounce(() => {
    querySamplesForSelection({ name: keyword }).then(res => {
      sampleLoading.value = false
      sampleOptions.value = res
    })
  }, 200)()
}

// 获取任务准备状态标签类型
const getTaskPrepareStatusTagType = (taskPrepare) => {
  switch (taskPrepare.status) {
    case 'PENDING':
      return 'warning';
    case 'COMPLETED':
      return 'success';
    default:
      return 'info';
  }
};
const getCurrentTaskPrepareView = async (param) => {
  try {
    taskPrepareView.value = await crudTaskPrepare.getCurrentTaskPrepareView({ mode: 'FACTORY' })
    updateTaskPrepare()
    let finishedCount = 0
    for (const tp of taskPrepareView.value.taskPrepareDtos) {
      if (tp.status === 'PUSHED' || tp.status === 'SUCCESS' || tp.status === 'FAILED') {
        finishedCount++
      }
    }

    if (finishedCount !== crud.finishedCount) {
      crud.finishedCount = finishedCount
      crud.refresh()
    }

    if (taskPrepareView.value.status === 'DRAFT') {
      message.value = ''
    } else {
      message.value = '进样完成进度：' + finishedCount + '/' + taskPrepareView.value.taskPrepareDtos.length
    }

    if (finishedCount === taskPrepareView.value.taskPrepareDtos.length) {
      batchSubmitLoading.value = false
      inputDisabled.value = false
    } else {
      inputDisabled.value = true
      setTimeout(() => {
        getCurrentTaskPrepareView(param)
      }, 500)
    }
  } catch (error) {
    console.log('getCurrentTaskPrepareView error=' + error)
    ElMessage.error('获取任务准备视图失败')
  }
}

const updateTaskPrepare = () => {
  if (taskPrepareView.value.taskPrepareDtos && taskPrepareView.value.taskPrepareDtos.length > 0) {
    for (const respTpDto of taskPrepareView.value.taskPrepareDtos) {
      respTpDto.inQueue = true
      for (var i in crud.data) {
        const row = crud.data[i]
        if (row.id === respTpDto.id && (row.status !== respTpDto.status || !row.inQueue)) {
          crud.data.splice(i, 1, respTpDto)
        }
      }
    }
  }
}

// BEGIN: 处理删除操作
const doCancel = (row) => {
  row.deletePopVisible = false
  //crud.cancelDelete(this.data)
}

const toDelete = (row) => {
  currentDeletingRow.value = row
  for (let i = 0; i < crud.data.length; i++) {
    if (row.id === crud.data[i].id) {
      crud.data[i] = { ...crud.data[i], deletePopVisible: true }
      break
    }
  }
}

const onPopoverShow = (row) => {
  currentDeletingRow.value = row
  setTimeout(() => {
    document.addEventListener('click', handleDocumentClick)
  }, 0)
}

const onPopoverHide = (row) => {
  currentDeletingRow.value = row
  document.removeEventListener('click', handleDocumentClick)
}

const handleDocumentClick = (event) => {
  // currentDeletingRow.value.deletePopVisible = false
}
// END: 处理删除操作


// 添加watch来监听对话框关闭
watch(() => sampleEditorVisible.value, (newVal) => {
  console.log('Vue version:', version)
  
  if (typeof defineModel === 'undefined') {
    console.warn('当前 Vue 版本不支持 defineModel，需要 Vue 3.4+ 版本')
  }

})

watch(() => [form.tag,crud.data], (newVal) => {
  debugger
  //board.value.tag = form.tag
  emit('onBoardUpdated',{oldTag: props.board.tag, newTag:form.tag, taskPrepareDtos:crud.data})
}, {deep:true})

</script>

<style lang="scss" scoped>
.main-form-body {
  text-align: left;
  :deep(.el-form-item__label) {
    font-weight: bold;
  }
}
.message-container {
  width: 100%;
  padding-left: 80px;
}
.main-form-footer {
  margin-top: 10px;
  padding-left: 0px;
  display: flex;
  .btn-container {
    display: flex;
  }
}
.span-message {
  line-height: 14px;
  font-size: 10px;
  font-weight: normal;
}
.span-message-FAILED {
  color: red;
}
.span-message-SUCCESS {
  color: green;
}
.span-message-RUNNING {
  color: #0580B4;
}

  .span-message-RUNNING {
    position: relative;
    padding: 10px 20px;
    font-size: 24px;
    color: #fff;
    text-align: center;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
  }

  /* 动态边框效果 */
  .span-message-RUNNING::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    padding: 2px; /* 边框宽度 */
    background: linear-gradient(90deg,
      #ff0000, #00ff00, #0000ff, #ff0000); /* 渐变色条 */
    background-size: 300% 100%;
    animation: flowing 3s linear infinite;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude; /* 仅保留边框部分 */
  }

  @keyframes flowing {
    from {
      background-position: 0% 50%;
    }
    to {
      background-position: 100% 50%;
    }
  }

.operation {
  width:18px;
  padding: 2px 2px;
  margin-left: 4px;
}
.flowing-border {
    position: relative;
    padding: 4px 4px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
  }

  /* 动态边框效果 */
  .flowing-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    padding: 2px; /* 边框宽度 */
    background: linear-gradient(90deg,
      #ff0000, #00ff00, #0000ff, #ff0000); /* 渐变色条 */
    background-size: 300% 100%;
    animation: flowing 3s linear infinite;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude; /* 仅保留边框部分 */
  }

  @keyframes flowing {
    from {
      background-position: 0% 50%;
    }
    to {
      background-position: 100% 50%;
    }
  }
</style>
