import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/alertLog',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/alertLog/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/alertLog',
    method: 'put',
    data
  })
}

export function clearAlertLog(data) {
  return request({
    url: 'api/alert/clearAlertLog',
    method: 'put',
    data
  })
}

export function findAlertStats(params) {
  return request({
    url: 'api/alert/findAlertStats',
    method: 'get',
    params
  })
}

export default { add, edit, del, findAlertStats, clearAlertLog }
