<template>
  <div>
    <el-dialog
      align-center
      v-model="positionSelectDialogVisible"
      :close-on-click-modal="false"
      title="设备实例选择"
      width="840px"
      height="500px"
      @opened="openDialog"
    >
      <PositionSelect :cur-data="curPredicateItem" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="text" @click="positionSelectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPosSelection">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <el-table ref="tableRef" :data="form.predicatesArr" size="small" cell-class-name="predicate-table-cell" max-height="450" class="predicate-table">
      <el-table-column prop="operator" label="连接符" width="80">
        <template #default="{ row }">
          <el-select v-model="row.joinOperator" placeholder="请选择" size="small">
            <el-option label="与" value="and" />
            <el-option label="或" value="or" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" min-width="100">
        <template #default="{ row, $index }">
          <el-input v-model="row.name" placeholder="请选择" class="input-with-select">
            <template #append>
              <el-dropdown @command="handlePredicateNameCommand">
                <el-button :icon="Search" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`position-select,${$index}`">选择点位</el-dropdown-item>
                    <el-dropdown-item :command="`command,${$index}`">选择指令</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-input>
        </template>
      </el-table-column>
      <el-table-column prop="compareOperator" label="比较符" width="80">
        <template #default="{ row }">
          <el-select v-model="row.compareOperator" placeholder="请选择" size="small">
            <el-option label="等于" value="eq" />
            <el-option label="不等" value="ne" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="targetValue" label="值" width="60">
        <template #default="{ row }">
          <el-input v-model="row.targetValue" class="input-pos" />
        </template>
      </el-table-column>
      <el-table-column v-if="checkPer(['admin','result:edit','result:del'])" prop="zpos" label="操作" width="80">
        <template #header>
          <span>操作</span>
          <el-button :icon="Plus" class="operation" @click="addCondition" />
        </template>
        <template #default="{ $index }">
          <div class="operation-container">
            <el-button :icon="Delete" class="operation" @click="deleteCondition($index)" />
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { Node } from '@antv/x6'
import { Search, Plus, Delete } from '@element-plus/icons-vue'
import PositionSelect from './PositionSelect.vue'

// 使用 defineModel 替代 props
const curData = defineModel('curData', {
  default: () => ({})
})

// 响应式状态
const defaultForm = {
  predicatesArr: []
}

const tableRef = ref(null)
const form = reactive({ ...defaultForm })
const positionSelectDialogVisible = ref(false)
const curPredicateItem = ref({})
const curPredicateItemIndex = ref(0)
const devPosition = ref([{ xpos: 0.0, ypos: 0.0, zpos: 0.0 }])

// 方法定义
const openDialog = () => {
  // 实现对话框打开逻辑
}


const transformNodeToFormData = () => {
  form.predicatesArr = []
  if (curData.value.predicates) {
    form.predicatesArr = JSON.parse(curData.value.predicates)
  }
}

const transferPredicatesArrToPredicates = () => {
    curData.value.predicates = JSON.stringify(form.predicatesArr)
}

const addCondition = () => {
  form.predicatesArr.push({
    joinOperator: 'and',
    name: null,
    sourceType: null,
    sourceParameter: null,
    compareOperator: 'eq',
    targetValue: null
  })
}

const deleteCondition = (rowIndex: number) => {
  form.predicatesArr.splice(rowIndex, 1)
}

const handlePredicateNameCommand = (command: string) => {
  const [cmd, rowIdxStr] = command.split(',')
  const rowIdx = Number(rowIdxStr)
  const curData = form.predicatesArr[rowIdx]
  
  if (cmd === 'position-select') {
    curData.sourceType = cmd
    curPredicateItem.value = curData
    curPredicateItemIndex.value = rowIdx
    positionSelectDialogVisible.value = true
  }
}

const submitPosSelection = () => {
  form.predicatesArr.splice(curPredicateItemIndex.value, 1, curPredicateItem.value)
  positionSelectDialogVisible.value = false
}

// 监听器
watch(() => curData.value.predicates, () => {
  transformNodeToFormData()
})

watch(() => form.predicatesArr, () => {
  transferPredicatesArrToPredicates()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  transformNodeToFormData()
})
</script>

<style lang="scss" scoped>
.operation {
  width: 18px;
  padding: 2px;
  margin-left: 4px;
}

.operation-container {
  display: flex;
}

.predicate-table {
  :deep(.predicate-table-cell) {
    .cell {
      padding-left: 2px;
      padding-right: 2px;
      
      input {
        padding-left: 2px;
        padding-right: 2px;
      }
      
      .input-pos input {
        width: 56px;
      }
      
      .input-name {
        padding-right: 4px;
      }
    }
  }
  
  :deep(.el-input__inner) {
    padding-left: 2px;
    padding-right: 2px;
  }
  
  :deep(.el-button) {
    padding-left: 2px;
    padding-right: 2px;
  }
  
  :deep(.el-input-group__append) {
    padding-right: 2px;
  }
}
</style>