<template>
  <div class="station-node">
    <div
      class="main-area"
      @mouseenter="mouseEnter"
      @mouseleave="mouseLeave"
    >
      <div class="main-info">
        <!-- 节点类型icon -->
        <i
          class="node-logo"
          :style="'background-image: url('+ProcedureIcon+')'"
        />
      </div>

      <div class="main-label">
        <el-tooltip class="item" effect="dark" :content="nodeName" placement="bottom">
          <div class="node-name">{{ nodeName }}</div>
        </el-tooltip>
        <!-- 节点状态信息 -->
        <div class="status-action">
          <!-- 节点操作菜单 -->
          <div class="more-action-container">
            <el-dropdown trigger="hover" @command="onNewAction">
              <div>
                <i class="more-action" :style="'background-image: url('+moreIcon+')'" />
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>新增方法</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加下游节点 -->
    <div v-if="nodeType !== 'COMMAND'" class="plus-dag">
      <i
        :class="['plus-action', plusActionSelected ? 'el-icon-remove-outline': 'el-icon-circle-plus-outline']"
        @click="toggleSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { Graph, StringExt } from '@antv/x6'
import { useDagNode } from '@/hooks/useDagNode'

import MoreIcon from '@/assets/images/more.svg'
import ProcedureIcon from '@/assets/images/procedure.svg'

// Props 定义
defineProps({
  type: {
    type: String,
    required: false
  },
  name: {
    type: String,
    required: false
  }
})

// 注入
const getNode = inject('getNode')

// Store
const store = useStore()

// 响应式状态
const moreIcon = ref(MoreIcon)
const nodeName = ref('')
const nodeType = ref('')
const devices = ref([]) // 设备实例列表
const selectedNextNodeId = ref('')
const nextNodeOptions = ref([
  { label: '新增节点', value: 0 }, 
  { label: '方法1', value: 1 }
])
const plusActionSelected = ref(false)

// 计算属性
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)
const plusDropdown = computed(() => ({
  'plus-dropdown': true,
  'plus-dropdown-hide': !plusActionSelected.value,
  'plus-dropdown-visible': plusActionSelected.value
}))

// 节点类型常量
const NodeType = {
  PROCEDURE: 'PROCEDURE',
  METHOD: 'METHOD',
  STEP: 'STEP',
  STATION: 'STATION',
  ACTION: 'ACTION',
  COMMAND: 'COMMAND'
}

// 加工类型列表
const PROCESSING_TYPE_LIST = [
  { type: 'PROCEDURE', name: '新建流程' },
  { type: 'METHOD', name: '新建方法' },
  { type: 'STATION', name: '新建步骤' },
  { type: 'ACTION', name: '新建动作' },
  { type: 'COMMAND', name: '新增命令' }
]

// 方法定义
const getPortsByType = (type: string, nodeId: string) => {
  let ports = []
  switch (type) {
    case NodeType.STATION:
    case NodeType.PROCEDURE:
      ports = [{ id: `${nodeId}-out`, group: 'out' }]
      break
    case NodeType.COMMAND:
      ports = [{ id: `${nodeId}-in`, group: 'in' }]
      break
    default:
      ports = [
        { id: `${nodeId}-in`, group: 'in' },
        { id: `${nodeId}-out`, group: 'out' }
      ]
  }
  return ports
}


const toggleSelect = () => {
  const node = getNode()
  const { graph } = node.model || {}
  showNodesRecursivelly(graph, node.id, plusActionSelected.value)
  plusActionSelected.value = !plusActionSelected.value
}

const showNodesRecursivelly = (graph, nodeId, bShow) => {
  const edges = graph.getEdges()
  edges.forEach(edge => {
    if (edge.source.cell === nodeId) {
      bShow ? edge.show() : edge.hide()
      const targetNode = graph.getCellById(edge.target.cell)
      bShow ? targetNode.show() : targetNode.hide()
      showNodesRecursivelly(graph, edge.target.cell, bShow)
    }
  })
}

const onNewAction = () => {
  const node = getNode()
  const nextNodeData = { 
    name: '新建方法', 
    type: 'METHOD', 
    parentNodeId: node.id, 
    method: { name: '新建方法', type: 'MAIN' }
  }
  createDownstream(nextNodeData)
  plusActionSelected.value = false
  selectedNextNodeId.value = ''
}

const { createDownstream, mouseEnter, mouseLeave } = useDagNode({node: getNode()})

// 生命周期钩子
onMounted(() => {
  const node = getNode()
  nodeName.value = node.getData().name
  nodeType.value = node.getData().type
  
  node.on('change:data', ({ current }) => {
    nodeName.value = current.name
    devices.value = node.getData().devices
  })
})
</script>

<style  rel="stylesheet/scss" lang="scss" scoped>

.station-node {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .main-area {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
    width: 100px;
    height: 120px;
    color: rgba(0, 0, 0, 65%);
    font-size: 12px;
    font-family: PingFangSC;
    line-height: 24px;
    background-color: #fff;
    box-shadow: 0 -1px 4px 0 rgba(209, 209, 209, 50%), 1px 1px 4px 0 rgba(217, 217, 217, 50%);
    border-radius: 2px;
    border: 1px solid transparent;
  }
  .main-area:hover {
    border: 1px solid rgba(0, 0, 0, 10%);
    box-shadow: 0 -2px 4px 0 rgba(209, 209, 209, 50%), 2px 2px 4px 0 rgba(217, 217, 217, 50%);
  }

.main-info {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

  .node-logo {
    display: block;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
  }

  .node-name {
    overflow: hidden;
    display: block;
    width: 70px;
    margin-left: 6px;
    color: rgba(0, 0, 0, 65%);
    font-size: 12px;
    font-family: PingFangSC;
    line-height: normal;
    //white-space: nowrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    vertical-align: bottom;
  }

  .main-label {
    height:auto;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .status-action {
    display: flex;
    flex-direction: row;
    height: 100%;
    align-items: center;
  }

  .status-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-size: 100%;
  }

  .status-icon-error {
    background: url('https://gw.alipayobjects.com/mdn/rms_43231b/afts/img/A*SEISQ6My-HoAAAAAAAAAAAAAARQnAQ')
      no-repeat center center / 100% 100%;
  }

  .status-icon-success {
    background: url('https://gw.alipayobjects.com/mdn/rms_43231b/afts/img/A*6l60T6h8TTQAAAAAAAAAAAAAARQnAQ')
      no-repeat center center / 100% 100%;
  }

  .more-action-container {
    margin-left: 4px;
    width: 15px;
    height: 100%;
    text-align: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding-top: 4px;
  }

  .more-action {
    display: inline-block;
    width: 15px;
    height: 20px;
    background: none  no-repeat center center / 100% 100%;
    /*
    background: url('https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*tFw7SIy-ttQAAAAAAAAAAAAADtOHAQ/original')
      no-repeat center center / 100% 100%; */
  }
  .more-action:hover {
    background-color: #84cdf7;
  }

  .plus-dag {
    visibility: hidden;
    position: relative;
    margin-left: 12px;
    height: 48px;
  }

  .plus-action {
    position: absolute;
    top: calc(50% - 8px);
    left: 0;
    width: 16px;
    height: 16px;
    background: url('https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*ScX2R4ODfokAAAAAAAAAAAAADtOHAQ/original')
      no-repeat center center / 100% 100%;
    cursor: pointer;
  }
  .plus-action:hover {
    background-image: url('https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*tRaoS5XhsuQAAAAAAAAAAAAADtOHAQ/original');
  }

  .plus-action:active,
  .plus-action-selected {
    background-image: url('https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*k9cnSaSmlw4AAAAAAAAAAAAADtOHAQ/original');
  }
  .plus-dropdown {
    position:absolute;
    z-index: 9998;
    padding: 2px 0;
    width: 105px;
    background-color: #fff;
    box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 5%), 0 6px 16px 0 rgba(0, 0, 0, 8%),
      0 3px 6px -4px rgba(0, 0, 0, 12%);
    border-radius: 2px;
  }
  .plus-dropdown-hide {
    visibility: hidden;
  }
  .plus-dropdown-visible {
    visibility: visible;
  }

  .x6-node-selected .main-area {
    border-color: #3471f9;
  }

  .x6-node-selected .plus-dag {
    visibility: visible;
  }

  .processing-node-menu {
    padding: 2px 0;
    width: 105px;
    background-color: #fff;
    box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 5%), 0 6px 16px 0 rgba(0, 0, 0, 8%),
      0 3px 6px -4px rgba(0, 0, 0, 12%);
    border-radius: 2px;
  }
  .processing-node-menu ul {
    margin: 0;
    padding: 0;
  }
  .processing-node-menu li {
    list-style:none;
  }

  .each-sub-menu {
    padding: 6px 12px;
    width: 100%;
  }

  .each-sub-menu:hover {
    background-color: rgba(0, 0, 0, 4%);
  }

  .each-sub-menu a {
    display: inline-block;
    width: 100%;
    height: 16px;
    font-family: PingFangSC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 65%);
  }

  .each-sub-menu span {
    margin-left: 8px;
    vertical-align: top;
  }

  .each-disabled-sub-menu a {
    cursor: not-allowed;
      color: rgba(0, 0, 0, 35%);
  }

  .node-mini-logo {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    vertical-align: top;
  }

  @keyframes running-line {
    to {
      stroke-dashoffset: -1000;
    }
  }

  

  .loading{
  //position: absolute;
  top: 50%;
  left: 50%;
 // width: 20px;
 // height: 20px;
  border: 2px solid #E0E0E0;
  border-top-color:  #42A5F5;
  border-bottom-color:  #42A5F5;
  border-radius: 50%;
  animation: animation 1s linear infinite
}
@keyframes animation{
  0%{
    transform: rotate(0deg)
  }
  100%{
    transform: rotate(360deg)
  }
}


</style>
