<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.areaName" clearable size="small" placeholder="输入区域名称搜索" style="width: 200px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <date-range-picker v-model="query.createTime" class="date-item" />
        <el-select v-model="query.status" clearable size="small" placeholder="状态" class="filter-item" style="width: 90px" @change="crud.toQuery">
          <el-option v-for="item in dict.dept_status" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <rrOperation />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="区域名称" prop="areaName">
            <el-input v-model="form.areaName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="所属区域" prop="parentId">
            <treeselect
              v-model="form.parentId"
              :load-options="loadAreas"
              :options="areas"
              style="width: 370px;"
              placeholder="选择上级区域"
            />
          </el-form-item>
          <el-form-item label="租户" prop="tenantId">
            <el-input v-model="form.tenantId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="区域描述" prop="description">
            <el-input v-model="form.description" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="区域状态">
            <el-radio v-for="item in dict.area_status" :key="item.id" v-model="form.status" :label="item.value">{{ item.label }}</el-radio>
            <el-form-item label="区域状态" prop="status">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.status" :label="item.value">{{ item.label }}</el-radio>
            </el-form-item>
          </el-form-item></el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table
        ref="table"
        v-loading="crud.loading"
        lazy
        :load="getAreaDatas"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :data="crud.data"
        row-key="areaId"
        @select="crud.selectChange"
        @select-all="crud.selectAllChange"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="areaName" label="区域名称" />
        <el-table-column prop="description" label="区域描述" />
        <el-table-column prop="status" label="区域状态">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :disabled="scope.row.id === 1"
              active-color="#409EFF"
              inactive-color="#F56C6C"
              active-value="1"
              inactive-value="0"
              @change="changeEnabled(scope.row, scope.row.enabled,)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column v-if="checkPer(['admin','areaInfo:edit','areaInfo:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import crudAreaInfo from '@/api/areaInfo'
import CRUD, { presenter, header, form, crud } from '@/components/crud/crud'
import rrOperation from '@/components/crud/RR.operation'
import crudOperation from '@/components/crud/crud.operation'
import udOperation from '@/components/crud/UD.operation'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { LOAD_CHILDREN_OPTIONS } from '@riophae/vue-treeselect'
import DateRangePicker from '@/components/DateRangePicker'

const defaultForm = { areaId: null, areaName: null, parentId: null, description: null, grade: null, status: 1, createBy: null, createTime: null, updateBy: null, updateTime: null }
export default {
  name: 'AreaInfo',
  components: { crudOperation, rrOperation, udOperation, Treeselect, DateRangePicker },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '区域配置', url: 'api/areaInfo', idField: 'areaId', sort: 'areaId,desc', crudMethod: { ...crudAreaInfo }})
  },
  dicts: ['area_status'],
  data() {
    return {
      areas: [],
      permission: {
        add: ['admin', 'areaInfo:add'],
        edit: ['admin', 'areaInfo:edit'],
        del: ['admin', 'areaInfo:del']
      },
      rules: {
        tenantId: [
          { required: true, message: '租户ID不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'areaId', display_name: '区域ID' },
        { key: 'areaName', display_name: '区域名称' },
        { key: 'status', display_name: '区域状态: 1:有效, 0:失效' }
      ]
    }
  },
  watch: {
    'form.parentId': 'onParentIdChanged'
  },
  methods: {
    getAreaDatas(tree, treeNode, resolve) {
      debugger
      const params = { parentId: tree.id }
      setTimeout(() => {
        crudAreaInfo.getAreaInfos(params).then(res => {
          debugger
          resolve(res.content)
        })
      }, 100)
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      if (form.parentId !== null) {
        form.isTop = '0'
      } else if (form.areaId !== null) {
        form.isTop = '1'
      }
      form.status = `${form.status}`
      if (form.areaId != null) {
        this.getSupAreas(form.areaId)
      } else {
        this.getAreas()
      }
    },
    getSupAreas(id) {
      crudAreaInfo.getAreaSuperior(id).then(res => {
        const date = res.content
        this.buildDepts(date)
        this.areas = date
      })
    },
    buildAreas(areas) {
      areas.forEach(data => {
        if (data.children) {
          this.buildAreas(data.children)
        }
        if (data.hasChildren && !data.children) {
          data.children = null
        }
      })
    },
    getAreas() {
      crudAreaInfo.getAreaInfos({ status: 1 }).then(res => {
        this.areas = res.content.map(function(obj) {
          if (obj.hasChildren) {
            obj.children = null
          }
          return obj
        })
      })
    },
    // 获取弹窗内部门数据
    loadAreas({ action, parentNode, callback }) {
      debugger
      if (action === LOAD_CHILDREN_OPTIONS) {
        crudAreaInfo.getAreaInfos({ status: 1, parentId: parentNode.id }).then(res => {
          debugger
          parentNode.children = res.content.map(function(obj) {
            if (obj.hasChildren) {
              obj.children = null
            }
            return obj
          })
          setTimeout(() => {
            callback()
          }, 100)
        })
      }
    },
    onParentIdChanged(newVal, oldVal) {
      debugger
      crud.form.grade = newVal.grade + 1
    },
    // 提交前做的操作
    [CRUD.HOOK.afterValidateCU](crud) {
      return true
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('此操作将 "' + this.dict.label.dept_status[val] + '" ' + data.areaName + '区域, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudAreaInfo.edit(data).then(res => {
          this.crud.notify(this.dict.label.dept_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
        }).catch(err => {
          data.status = !data.status
          console.log(err.response.data.message)
        })
      }).catch(() => {
        data.status = !data.status
      })
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .vue-treeselect__control,::v-deep .vue-treeselect__placeholder,::v-deep .vue-treeselect__single-value {
    height: 30px;
    line-height: 30px;
  }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
