import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
//import langJsx from 'vite-plugin-lang-jsx'
import cssExport from 'vite-plugin-css-export';
import sassDts from 'vite-plugin-sass-dts';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
//import svgLoader from 'vite-svg-loader';

import path from 'path'

// https://vite.dev/config/
export default defineConfig(({mode}) => {
    
  const env = loadEnv(mode,process.cwd())
  console.log('env=' + env)

  const dd = path.resolve(process.cwd(), 'src/assets/icons/svg')
  console.log('dd=' + dd)

  return {
  plugins: [ createSvgIconsPlugin({
    // 指定需要缓存的图标文件夹
    iconDirs: [path.resolve(process.cwd(), 'src/assets/icons/svg')],
    // 指定 symbolId 格式
    symbolId: 'icon-[name]',
    inject: 'body-last'
  }), vueJsx(), vue(), cssExport(),sassDts({
    enabledMode: ['development', 'production'], // 指定生效模式
  })
   ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      path: 'path-browserify'
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    optimizeDeps: {
      include: ['path-browserify']
    }
  },
  server: {
    open: true,
    host: 'localhost',
    port: 8014,
    hmr: { overlay: false },
    proxy: {
      '/api': {
        target: env.VITE_BASE_API,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/,'api')
      },
      '/file': {
        target: env.VITE_BASE_API,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/file/,'file')
      },
      '/auth': {
        target: env.VITE_BASE_API,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/auth/,'auth')
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        manualChunks(id) {
          if (id.includes('node_modules')){
            return id.toString().split('node_modules/')[1].split('/')[0].toString();
          }
        }
      }
    }
  },
  esbuild: {
    loader: "jsx", // 或其他适合你文件类型的值
  }
}
})
