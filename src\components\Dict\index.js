import { provide, inject, reactive, onMounted } from 'vue'
import Dict from './Dict'

const install = function(app) {
  app.mixin({
    setup() {
      debugger
      const dict = reactive({
        dict: {},
        label: {}
      })

      provide('dict', dict)

      // 处理字典数据初始化
      onMounted(() => {
        debugger
        const dicts = this.$options.dicts || []
        if (dicts instanceof Array && dicts.length > 0) {
          new Dict(dict).init(dicts, () => {
            this.$nextTick(() => {
              this.$emit('dictReady')
            })
          })
        }
      })

      return {
        dict
      }
    }
  })
}

export default {
  install
}
