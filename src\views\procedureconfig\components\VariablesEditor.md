# VariablesEditor 组件

一个用于编辑可选变量的Vue组件，支持通过API加载变量定义或使用自定义变量数组。

## 功能特性

- ✅ 支持通过API动态加载变量定义
- ✅ 支持自定义变量数组
- ✅ 双向数据绑定 (v-model)
- ✅ 变量值实时同步
- ✅ 空状态展示
- ✅ 调试信息显示
- ✅ 响应式设计
- ✅ TypeScript支持

## 基本用法

### 1. 通过API加载变量定义

```vue
<template>
  <VariablesEditor
    v-model="variables"
    :action-id="actionId"
    @change="handleVariablesChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import VariablesEditor from '@/components/VariablesEditor.vue'

const actionId = ref('123')
const variables = ref(null)

const handleVariablesChange = (variables, variablesObj) => {
  console.log('Variables changed:', variables, variablesObj)
}
</script>
```

### 2. 使用自定义变量数组

```vue
<template>
  <VariablesEditor
    v-model="variables"
    :variables="customVariables"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'
import VariablesEditor from '@/components/VariablesEditor.vue'

const variables = ref(null)
const customVariables = reactive([
  {
    key: 'temperature',
    name: '温度',
    description: '设备工作温度',
    defaultValue: '25',
    value: ''
  },
  {
    key: 'pressure',
    name: '压力',
    description: '系统压力值',
    defaultValue: '1.0',
    value: ''
  }
])
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | String/Object | null | 变量值，支持v-model |
| actionId | String/Number | null | 用于获取变量定义的Action ID |
| variables | Array | [] | 自定义变量数组 |
| labelWidth | String | '30%' | 标签宽度 |
| showDebugInfo | Boolean | false | 是否显示调试信息 |
| disabled | Boolean | false | 是否禁用 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: string) | 变量值更新时触发 |
| change | (variables: string, variablesObj: object) | 变量变化时触发 |

## 变量数据结构

```typescript
interface Variable {
  key: string          // 变量键名
  name?: string        // 变量显示名称
  description?: string // 变量描述
  defaultValue?: string // 默认值
  value?: string       // 当前值
}
```

## 数据格式

### 输入格式 (modelValue)
- **字符串**: JSON格式的变量对象，如 `'{"key1":"value1","key2":"value2"}'`
- **对象**: 变量对象，如 `{key1: "value1", key2: "value2"}`
- **null**: 无变量值

### 输出格式
- 有值时: JSON字符串格式，如 `'{"key1":"value1","key2":"value2"}'`
- 无值时: `null`

## 在StepProperty.vue中的集成

```vue
<template>
  <div>
    <!-- 其他表单项 -->
    
    <el-divider content-position="left">可选变量</el-divider>
    <VariablesEditor
      v-model="form.variables"
      :action-id="getActionId()"
      @change="handleVariablesChange"
    />
    
    <!-- 其他内容 -->
  </div>
</template>

<script setup>
import VariablesEditor from '@/components/VariablesEditor.vue'

// 获取ActionId用于变量编辑器
const getActionId = () => {
  const curNode = props.curNode
  const stepData = getStoreObjectByNodeId(curNode.id)
  return stepData?.action?.id || null
}

// 处理变量变化
const handleVariablesChange = (variables, variablesObj) => {
  console.log('Variables changed:', variables, variablesObj)
  form.variables = variables
}
</script>
```

## API依赖

组件依赖以下API：
- `queryActionVarDefinesByActionId`: 根据Action ID查询变量定义

## 样式定制

组件提供了完整的CSS类名，可以通过以下方式自定义样式：

```scss
.variables-editor {
  .variables-list {
    .variable-item {
      // 自定义变量项样式
    }
  }
  
  .empty-state {
    // 自定义空状态样式
  }
}
```

## 注意事项

1. 当同时提供 `actionId` 和 `variables` 时，优先使用 `actionId` 加载变量定义
2. 变量值为空字符串、null或undefined时不会包含在输出结果中
3. 组件会自动处理JSON字符串的解析和序列化
4. 建议在使用前确保相关API接口正常工作

## 示例文件

参考 `VariablesEditor.example.vue` 文件查看完整的使用示例。
