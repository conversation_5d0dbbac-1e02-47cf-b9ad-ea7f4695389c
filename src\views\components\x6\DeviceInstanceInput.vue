<template>

  <el-input v-model="deviceInstance.deviceInstanceName" placeholder="请输入内容" class="input-with-select">
    <template #append>
      <el-popover
        placement="right"
        :width="840"
        :height="500"
        @show="openDialog"
        @hide="onDevInstanceBlur(form)"
        trigger="click">
        <template #default>
          <div class="dev-graph-container">
            <div id="main_board_container" ref="mainBoardContainerRef" />
          </div>
        </template>
        <template #reference>
          <el-button 
            :icon="Search"
          ></el-button>
        </template>
      </el-popover>
    </template>
  </el-input>

</template>

<script setup>
import { ref, reactive, watch, onMounted, inject, defineModel, defineEmits,nextTick } from 'vue'
import { Graph } from '@antv/x6'
import { Selection } from '@antv/x6-plugin-selection'
import { register } from '@antv/x6-vue-shape'
import { Search } from '@element-plus/icons-vue'
import { getLatest } from '@/api/deviceLayout'
import DeviceNode from '@/views/devicelayout/DeviceNode.vue'
import { useDict } from '@/hooks/useDict'

// 注册设备节点组件
register({
  shape: 'device-node',
  width: 212,
  height: 48,
  component: DeviceNode
})

const content = defineModel("content", {
  type: String,
  required: true,
  default: "",
});

const deviceInstance = defineModel("deviceInstance", {
  type: Object,
  default: {},
})

// Props
const props = defineProps({
  curNode: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(["blur"]);

// 使用字典
const { dict } = useDict(['position_status'])


// 注入
const getDataStore = inject('getDataStore')

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  deviceInstanceName: null,
  deviceInstanceId: null,
  status: null,
  bindPos: false,
  bindDeviceInstanceId: null,
  bindDeviceInstanceName: null,
  bindPositionName: null,
  bindPositionStatus: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}




const dialogVisible = ref(false)
const graph = ref(null)
const selectedNode = ref(null)
const layoutDataStore = reactive({})
const dataStore = reactive({})
const initing = ref(false)
const formRef = ref(null)
const mainBoardContainerRef = ref(null)

// 方法
const initGraph = () => {
  
 // if (initing.value) return
 // initing.value = true

  const container = mainBoardContainerRef.value
  console.log('container dimensions:', {
    width: container.offsetWidth,
    height: container.offsetHeight,
    clientHeight: container.clientHeight
  })

  if (graph.value){
    return
  }

  graph.value = new Graph({
    container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    autoResize: true,
    background: false,
    snapline: false,
    interacting: false,
    connecting: {
      snap: true,
      allowBlank: false,
      allowMulti: true,
      allowLoop: true,
      highlight: true,
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#5F95FF',
              stroke: '#5F95FF'
            }
          }
        }
      },
      router: {
        name: 'orth'
      },
      connector: {
        name: 'rounded',
        args: {
          radius: 8
        }
      }
    },
    panning: {
      enabled: true
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    }
  })

  graph.value.use(new Selection({
    rubberband: true,
    showNodeSelectionBox: true,
    multiple: false,
    filter: (cell) => {
      const storeData = getLayoutStoreObjectByNodeId(cell.id)
      if (storeData?.positions?.length > 0) {
        return true
      } else {
        return false
      }
    }
  }))

  graph.value.fromJSON(JSON.parse(layoutDataStore.layoutSvg))

  // 隐藏无点位设备实例
  /*
  graph.value.getNodes().forEach(node => {
    const storeData = getLayoutStoreObjectByNodeId(node.id)
    if (storeData?.positions?.length > 0) {
      node.show()
    } else {
      node.hide()
    }
  })
    */



  // 事件监听
  graph.value.on('cell:selected', ({ cell }) => {
    if (cell.isNode()) {
      selectedNode.value = cell
    }
  })

  graph.value.on('cell:unselected', () => {
    selectedNode.value = null
  })

  
  //   graph.value.zoomToFit({ padding: 10, minScale: 0.8, maxScale: 4 })
  graph.value.zoomToFit({ padding: 10})
  graph.value.centerContent()

}

const initFormData = () => {

  // 解析 content 字符串
  if (content.value && content.value.startsWith('${GET_POSITION_CODE(')) {
    try {
      // 提取括号内的内容
      const contentStr = content.value
      const paramStr = contentStr.substring(contentStr.indexOf('(') + 1, contentStr.lastIndexOf(')'))
      
      // 分割参数
      const params = []
      let currentParam = ''
      let inQuote = false
      
      for (let i = 0; i < paramStr.length; i++) {
        const char = paramStr[i]
        
        if (char === ',' && !inQuote) {
          params.push(currentParam.trim())
          currentParam = ''
        } else if (char === "'" || char === '"') {
          inQuote = !inQuote
          if (!inQuote) {
            // 引号结束，不添加引号
          } else {
            // 引号开始，不添加引号
          }
        } else {
          currentParam += char
        }
      }
      
      // 添加最后一个参数
      if (currentParam) {
        params.push(currentParam.trim())
      }
      
      // 根据参数数量判断格式
      if (params.length >= 4) {
        // 设置表单值
        form.deviceInstanceId = params[0].replace(/['"]/g, '')
        form.deviceInstanceName = params[1].replace(/['"]/g, '')
        form.name = params[2].replace(/['"]/g, '')
        form.status = params[3].replace(/['"]/g, '')
        
      }
    } catch (error) {
      console.error('解析 content 字符串失败:', error)
    }
  }
}

const transformNodeToFormData = () => {
  if (!props.curNode?.id ){
    return
  }
  dataStore.value = getDataStore()
  const cmdData = getStoreObjectByNodeId(props.curNode.id)
  if (cmdData?.parameter) {
    const paramObj = JSON.parse(cmdData.parameter)
    Object.assign(form, paramObj)
  }
}

const transformFormDataToNode = () => {
  if (!props.curNode?.id ){
    return
  }
  const cmdData = getStoreObjectByNodeId(props.curNode.id)
  if (cmdData) {
    cmdData.parameter = JSON.stringify(form)
  }
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.value?.dagNodeId === nodeId) {
    return dataStore.value
  }
  
  for (const actionObj of dataStore.value?.actions || []) {
    if (actionObj.dagNodeId === nodeId) {
      return actionObj
    }
    for (const cmdObj of actionObj.commands || []) {
      if (cmdObj.dagNodeId === nodeId) {
        return cmdObj
      }
    }
  }
  return null
}

const getLayoutStoreObjectByNodeId = (nodeId) => {
  return layoutDataStore.deviceInstanceList?.find(
    devInsObj => devInsObj.layoutNodeId === nodeId
  )
}

const initSelection = () => {
  debugger
  if (deviceInstance.value.deviceInstanceName) {
    const selectedNode = graph.value.getNodes().find(node => {
      const storeData = getLayoutStoreObjectByNodeId(node.id)
      return storeData?.name === deviceInstance.value.deviceInstanceName
    })
    debugger
    if (selectedNode) {
      graph.value.select(selectedNode)
    }
  }
}

const openDialog = () => {
  initGraph()
  initSelection()
}

const onDevInstanceSelect = (form) => {
 // dialogVisible.value = true
}

const onDevInstanceBlur = (form) => {
 // dialogVisible.value = false
  submitPosSelection()
}

const submitPosSelection = () => {
  if (!selectedNode.value) return

  const devInst = getLayoutStoreObjectByNodeId(selectedNode.value.id)

  deviceInstance.value.deviceInstanceName = devInst.name
  deviceInstance.value.deviceInstanceId = devInst.id
  emit("blur", devInst)
  /*
  const selectedPositions = devInst.positions
  const nameArr = [...new Set(selectedPositions.map(pos => pos.name))]

  form.posNames = nameArr
  if (nameArr.length > 0) {
    form.name = nameArr[0]
  }
  form.deviceInstanceName = devInst.name
  form.deviceInstanceId = devInst.id
*/
 // dialogVisible.value = false
}

const submitDialog = () => {
  content.value = '${GET_POSITION_CODE('+form.deviceInstanceId+',\''+form.deviceInstanceName+'\',\''+form.name+'\',\''+form.status+'\')}'
  dialogVisible.value = false
  emit("blur", content.value );
}

// 监听器
watch(() => dataStore.value, transformNodeToFormData, { deep: true })
watch(() => props.curNode, transformNodeToFormData)
watch(() => form, transformFormDataToNode, { deep: true })

// 生命周期钩子
onMounted(async () => {
  transformNodeToFormData()
  try {
    const res = await getLatest(null)
    Object.assign(layoutDataStore, res || {})
  } catch (error) {
    console.error('Failed to get latest layout:', error)
  }
})
</script>

<style lang="scss">
.dev-graph-container {
  width: 800px;
  height: 500px;
  
  #main_board_container {
    height: 100%;
    background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
    background-size: cover;
  }
}
</style>