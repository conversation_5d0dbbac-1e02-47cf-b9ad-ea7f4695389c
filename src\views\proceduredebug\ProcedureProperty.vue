<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="流程名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { Node } from '@antv/x6'

const defaultForm = { id: null, name: null, description: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }

export default {
  name: 'ProcedureProperty',
  props: {
    curNode: {
      default: () => {
        return {}
      },
      type: Node
    }
  },
  dicts: ['device_types'],
  inject: ['getDataStore'],
  data() {
    return {
      form: { ...defaultForm },
      rules: {
        name: [
          { required: true, message: '方法名称不能为空', trigger: 'blur' }
        ]
      },

      initing: false,
      dataStore: {}

    }
  },
  computed: {

  },
  watch: {
    dataStore: { handler(newVal, oldVal) {
      this.transformNodeToFormData()
    }, deep: true },
    curNode: { handler(newVal, oldVal) {
      this.transformNodeToFormData()
    }, deep: false },
    form: { handler(newVal, oldVal) {
      this.transformFormDataToNode()
    }, deep: true }
  },
  created() {
    this.transformNodeToFormData()
  },
  methods: {

    init() {
      this.initing = true
    },
    transformNodeToFormData() {
      const curNode = this.curNode
      this.dataStore = this.getDataStore()
      this.form.name = curNode.getData().name
      this.form.description = curNode.getData().description
    },
    transformFormDataToNode() {
      const curNode = this.curNode
      this.dataStore = this.getDataStore()
      curNode.getData().name = this.form.name
      curNode.getData().description = this.form.description
      this.getStoreObjectByNodeId(curNode.id).name = this.form.name
      this.getStoreObjectByNodeId(curNode.id).description = this.form.description

      const changeState = 'changed from property'
      curNode.setData({ ...this.curNode.getData(), changeState })
     // this.$emit('update-device-instance', deviceInst)
    },
    getStoreObjectByNodeId(nodeId) {
      if (this.dataStore.dagNodeId === nodeId) {
        return this.dataStore
      }
      for (const methodObj of this.dataStore.methods) {
        if (methodObj.dagNodeId === nodeId) {
          return methodObj
        }
        for (const stepObj of methodObj.steps) {
          if (stepObj.dagNodeId === nodeId) {
            return stepObj
          }
          for (const actionObj of stepObj.actions) {
            if (actionObj.dagNodeId === nodeId) {
              return actionObj
            }
            for (const cmdObj of actionObj.commands) {
              if (cmdObj.dagNodeId === nodeId) {
                return cmdObj
              }
            }
          }
        }
      }

      return null
    }
  }
}
</script>

<style lang="scss">

</style>
