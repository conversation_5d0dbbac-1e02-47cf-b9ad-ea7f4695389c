<template>
  <div class="app-container">
    <!-- 工具栏 -->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item :label="$t('样本名称')" prop="name">
            <el-input 
              v-model="query.name" 
              clearable 
              :placeholder="$t('样本名称')" 
              style="width: 185px;" 
              @keyup.enter="crud.toQuery" 
            />
          </el-form-item>
          <el-form-item :label="$t('样本分类')" prop="type">
            
            <el-select
              v-model="query.type"
              value-key="name"
              filterable
              clearable
              allow-create
              default-first-option
              placeholder="请选择分类"
              style="width: 185px;"
              @change="crud.toQuery"
            >
              <el-option
                v-for="item in categoryOptions"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('创建时间')" prop="createTime">
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rrOperation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>

      <crudOperation :permission="permission" />
      <SampleEditor v-if="sampleEditorSelected === 'default'" v-model="sampleEditorVisible" @on-success="onSampleEditSuccess" :form="form" />
      <SampleEditorForPrinter v-if="sampleEditorSelected === 'printer'" v-model="sampleEditorVisible" @on-success="onSampleEditSuccess" :form="form" />
      <SampleEditorAsBoard v-if="sampleEditorSelected === 'board'" v-model="sampleEditorVisible" @on-success="onSampleEditSuccess" :form="form" />
      <!-- 表格渲染 -->
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="crud.data"
        size="small"
        style="width: 100%;"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="样本名称" />
        <el-table-column prop="categoryName" label="样本分类" />
        <el-table-column prop="description" label="样本描述" />
        <el-table-column prop="customerName" label="样本所属客户" />
        <el-table-column prop="taskPrepares" label="样本标签">
          <template #default="scope">
            <el-tag 
              v-for="item in scope.row.taskPrepares" 
              :key="item.id" 
              :type="getTaskPrepareStatusTagType(item)" 
              effect="light"
            >
              {{ !item.rfTag ? '无' : item.rfTag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('创建时间')" />
        <el-table-column prop="updateTime" :label="$t('更新时间')" />
        <el-table-column 
          v-if="checkPer(['admin', 'sample:edit', 'sample:del'])" 
          :label="$t('操作')" 
          width="150px" 
          align="center"
        >
          <template #default="scope">
            <udOperation :data="scope.row" :permission="permission" />
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <pagination />

  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, inject,provide, onRenderTracked, shallowReactive   } from 'vue';
import { useCrud } from '@/hooks/useCrud'; // 假设你有一个自定义的 CRUD Hook
//import { usePermission } from '@/hooks/usePermission'; // 假设你有一个权限管理 Hook
import { ElMessage } from 'element-plus';

import rrOperation from '@/components/crud/RR.operation'
import crudOperation from '@/components/crud/CRUD.operation'
import udOperation from '@/components/crud/UD.operation.vue'
import pagination from  '@/components/crud/Pagination'
import DateRangePicker from '@/components/DateRangePicker'

import crudSample from '@/api/sample'
import crudSampleCategory from '@/api/sampleCategory'
import crudSampleCustomer from '@/api/sampleCustomer'

import SampleEditor from './SampleEditor'
import SampleEditorForPrinter from './SampleEditorForPrinter'
import SampleEditorAsBoard from './SampleEditorAsBoard'


const sampleEditorVisible = ref(false);

// 表单状态
const form = reactive({
  name: '',
  description: '',
  category: null,
  customer: null,
  taskPrepares: []
});

// 查询条件
const query = reactive({
  name: null,
  type: '',
  createTime: null
});

// CRUD 管理
const { crud, CRUD } = useCrud({
  query,
  url: '/api/sample'
});

provide('crud', crud);


// 权限管理
const  checkPer = inject('checkPer');

// 权限配置
const permission = {
  add: ['admin', 'sample:add'],
  edit: ['admin', 'sample:edit'],
  del: ['admin', 'sample:del']
}

const sampleEditorSelected = ref('board');

// 分类和客户下拉框选项
const categoryOptions = ref([]);
const customerOptions = ref([]);

// 表单验证规则
const rules = reactive({
  name: [{ required: true, message: '请输入样本名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入样本描述', trigger: 'blur' }],
  category: [{ required: true, message: '请选择样本分类', trigger: 'change' }],
  customer: [{ required: true, message: '请选择所属客户', trigger: 'change' }]
});

// 初始化数据
onMounted(async () => {
  try {
    
    const [categories, customers] = await Promise.all([
      crudSampleCategory.selectSampleCategories().then(res => {
        
        return res
      }),
      crudSampleCustomer.selectSampleCustomers().then(res => {
        return res
      })
    ]);
    categoryOptions.value = categories;
    customerOptions.value = customers;
  } catch (error) {
    ElMessage.error('加载分类和客户数据失败');
  }
  crud.toQuery();
});

// 更改分类事件
const onChangeCategory = (value) => {
  console.log('选择的分类:', value);
};

// 更改客户事件
const onChangeCustomer = (value) => {
  console.log('选择的客户:', value);
};

// 添加任务准备
const addTaskPrepare = (taskPrepares) => {
  taskPrepares.push({
    id: Date.now(),
    procedure: null,
    rfTag: '',
    status: 'PENDING'
  });
};

// 删除任务准备
const deleteTaskPrepare = (taskPrepare) => {
  const index = form.taskPrepares.indexOf(taskPrepare);
  if (index !== -1) {
    form.taskPrepares.splice(index, 1);
  }
};

// 获取任务准备状态标签类型
const getTaskPrepareStatusTagType = (taskPrepare) => {
  switch (taskPrepare.status) {
    case 'PENDING':
      return 'warning';
    case 'COMPLETED':
      return 'success';
    default:
      return 'info';
  }
};

// 获取 RF 标签文本
const getRfTagText = (taskPrepare) => {
  return taskPrepare.rfTag || '无';
};

// 当前行变化事件
const handleCurrentChange = (row) => {
  console.log('当前行变化:', row);
};

const onSampleEditSuccess = (form) => {
  sampleEditorVisible.value = false;
  crud.toQuery();
};

crud.hooks[CRUD.HOOK.beforeToAdd] = (state) => {
  
  Object.assign(form, state.form)
  form.id = null
  sampleEditorVisible.value = true;
  return false
};
crud.hooks[CRUD.HOOK.beforeToEdit] = (state) => {
  debugger
  Object.assign(form, state.form)
  sampleEditorVisible.value = true;
  return false
};
</script>

<style scoped>
.operation {
  width:18px;
  padding: 2px 2px;
  margin-left: 4px;
}
.flowing-border {
    position: relative;
    padding: 4px 4px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
  }

  /* 动态边框效果 */
  .flowing-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    padding: 2px; /* 边框宽度 */
    background: linear-gradient(90deg,
      #ff0000, #00ff00, #0000ff, #ff0000); /* 渐变色条 */
    background-size: 300% 100%;
    animation: flowing 3s linear infinite;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude; /* 仅保留边框部分 */
  }

  @keyframes flowing {
    from {
      background-position: 0% 50%;
    }
    to {
      background-position: 100% 50%;
    }
  }
</style>
