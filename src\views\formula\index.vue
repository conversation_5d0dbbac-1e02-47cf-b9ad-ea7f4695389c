<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryForm" :model="query" size="default" :inline="true">
          <el-form-item label="脚本名称">
            <el-input
              v-model="query.name"
              clearable
              placeholder="脚本名称"
              style="width: 185px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="query.type" clearable placeholder="类型" style="width: 120px">
              <el-option
                v-for="item in (dict?.data?.script_types || [])"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <rr-operation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
    <crud-operation :permission="permission" :crud="crud" />
    <!--表单组件-->
    <el-dialog
      align-center
      :model-value="crud.status.cu > 0"
      :title="crud.status.title"
      width="800px"
      :close-on-click-modal="false"
      @close="crud.cancelCU"
      class="formula-editor-dialog"
    >
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px" class="formula-form">
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="类型" >
            <el-option
              v-for="item in (dict?.data?.script_types || [])"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="脚本名称">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="脚本描述">
          <el-input v-model="form.description" />
        </el-form-item>
        <el-form-item label="脚本内容">
          <LesFormulaEditor v-if="form && !isUnmounted" :cur-form="form" />
        </el-form-item>
        
      </el-form>
      
      <template #footer>
        <div class="footer-inner-container">
          <el-button text @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" :icon="Finished" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </template>
    </el-dialog>
    <!--表格渲染-->
    <el-table
      v-if="!isUnmounted && crud"
      ref="tableRef"
      v-loading="crud.loading"
      :data="crud.data || []"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="脚本名称" />
      <el-table-column prop="type" label="脚本类型" >
        <template #default="{ row }">
          {{ row?.type ? getDictLabel('script_types', row.type) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="description" label="脚本描述" />
      <el-table-column prop="content" label="脚本内容" />
      <el-table-column prop="createTime" label="创建时间"  width="120"/>
      <el-table-column prop="updateTime" label="更新时间"  width="120"/>
      <el-table-column v-if="checkPer(['admin','formula:edit','formula:del'])" label="操作" width="150px" align="center">
        <template #default="{ row }">
          <ud-operation :crud="crud" :data="row" :permission="permission" />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination v-if="!isUnmounted && crud" :crud="crud" />
    
  </div>
</template>

<script setup>
import { ref, inject, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { useDict } from '@/hooks/useDict'
import crudFormula from '@/api/formula'
import LesFormulaEditor from './LesFormulaEditor.vue'
import CrudOperation from '@/components/crud/CRUD.Operation.vue'
import RrOperation from '@/components/crud/RR.Operation.vue'
import UdOperation from '@/components/crud/UD.Operation.vue'
import Pagination from '@/components/crud/Pagination.vue'
import { Plus, Delete, Finished } from '@element-plus/icons-vue'

// 组件卸载标志
const isUnmounted = ref(false)

const checkPer = inject('checkPer')
// 响应式数据
const { dict, getDictLabel } = useDict(['script_types'])

const formRef = ref(null)
const tableRef = ref(null)

// CRUD配置
const defaultForm = {
  id: null,
  type: 'FORMULA',
  name: null,
  description: null,
  content: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const permission = {
  add: ['admin', 'formula:add'],
  edit: ['admin', 'formula:edit'],
  del: ['admin', 'formula:del']
}

const { crud, CRUD, query, form, rules } = useCrud({
  title: '脚本管理',
  url: 'api/formula',
  idField: 'id',
  sort: 'id,desc',
  crudMethod: { ...crudFormula },
  defaultForm,
  permission,
  formRef,
  tableRef
})

// 初始化CRUD钩子
crud.hooks[CRUD.HOOK.beforeRefresh] = () => {
  // 检查组件是否已卸载
  if (isUnmounted.value) {
    return false
  }
  return true
}

crud.hooks[CRUD.HOOK.beforeCrudSubmitCU] = () => {
  // 检查组件是否已卸载
  if (isUnmounted.value) {
    return false
  }
  const theForm = crud.form
  // debugger逻辑保持
  return true
}

// 监听器 - 添加安全检查
let contentWatcher = null
contentWatcher = watch(() => {
  if (isUnmounted.value || !form) return null
  return form.content
}, (val) => {
  if (isUnmounted.value) return
  console.log('Watch list form.content:' + val)
}, {
  immediate: false
})

onMounted(async () => {
  try {
    await nextTick()
    if (!isUnmounted.value) {
      crud.toQuery()
    }
  } catch (error) {
    console.error('Error in onMounted:', error)
  }
})

// 组件卸载时的清理
onBeforeUnmount(() => {
  isUnmounted.value = true

  // 清理监听器
  if (contentWatcher) {
    contentWatcher()
    contentWatcher = null
  }

  // 清理CRUD钩子
  if (crud && crud.hooks) {
    crud.hooks[CRUD.HOOK.beforeRefresh] = null
    crud.hooks[CRUD.HOOK.beforeCrudSubmitCU] = null
  }

  // 清理refs
  if (formRef.value) {
    formRef.value = null
  }
  if (tableRef.value) {
    tableRef.value = null
  }
})
</script>

<style lang="scss" scoped>
.formula-editor-dialog {
  background: linear-gradient(rgb(33 85 129) 7%, rgb(200 223 246) 7%, rgb(255 255 255) 100%);
  :deep(.el-dialog__body) {
    padding-right: 20px !important;
  } 
}
.formula-form {
  margin-bottom: 0!important;
  padding-right: 20px !important;
}
.footer-inner-container {
  padding-right: 20px !important;
}
</style>
