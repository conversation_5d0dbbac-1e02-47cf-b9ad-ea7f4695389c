import { Graph, Edge, Path } from '@antv/x6'
import { register } from '@antv/x6-vue-shape'
import DataProcessingDagNode from './DataProcessingDagNode.vue'

/**
 * 注册DAG节点及边
 */
export function registerDataProcessingDag() {
  // 注册节点
  register({
    shape: 'data-processing-dag-node',
    width: 212,
    height: 32,
    component: DataProcessingDagNode,
    ports: {
      groups: {
        in: {
          position: 'left',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: 'transparent',
              strokeWidth: 1,
              fill: 'transparent'
            }
          }
        },
        out: {
          position: {
            name: 'right',
            args: {
              dx: -4
            }
          },
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: 'transparent',
              strokeWidth: 1,
              fill: 'transparent'
            }
          }
        }
      }
    }
  })

  // 注册连线
  Graph.registerConnector(
    'curveConnector',
    (sourcePoint, targetPoint) => {
      const hgap = Math.abs(targetPoint.x - sourcePoint.x)
      const path = new Path()
      path.appendSegment(Path.createSegment('M', sourcePoint.x - 4, sourcePoint.y))
      path.appendSegment(Path.createSegment('L', sourcePoint.x + 8, sourcePoint.y))
      path.appendSegment(
        Path.createSegment(
          'C',
          sourcePoint.x < targetPoint.x ? sourcePoint.x + hgap / 2 : sourcePoint.x - hgap / 2,
          sourcePoint.y,
          sourcePoint.x < targetPoint.x ? targetPoint.x - hgap / 2 : targetPoint.x + hgap / 2,
          targetPoint.y,
          targetPoint.x - 6,
          targetPoint.y,
        ),
      )
      path.appendSegment(Path.createSegment('L', targetPoint.x + 2, targetPoint.y))
      return path.serialize()
    },
    true,
  )

  // 边配置
  Edge.config({
    markup: [
      {
        tagName: 'path',
        selector: 'wrap',
        attrs: {
          fill: 'none',
          cursor: 'pointer',
          stroke: 'transparent',
          strokeLinecap: 'round'
        }
      },
      {
        tagName: 'path',
        selector: 'line',
        attrs: {
          fill: 'none',
          pointerEvents: 'none'
        }
      }
    ],
    connector: { name: 'curveConnector' },
    attrs: {
      wrap: {
        connection: true,
        strokeWidth: 10,
        strokeLinejoin: 'round'
      },
      line: {
        connection: true,
        stroke: '#A2B1C3',
        strokeWidth: 1,
        targetMarker: {
          name: 'classic',
          size: 6
        }
      }
    }
  })

  Graph.registerEdge('data-processing-curve', Edge, true)

}


/**
 * 
 * @param {*} node1 
 * @param {*} node2 
 * @returns 
 */
export function isOverlapping (node1, node2) {
  const bbox1 = node1.getBBox()
  const bbox2 = node2.getBBox()
  return !(
    bbox1.x + bbox1.width < bbox2.x ||
    bbox1.x > bbox2.x + bbox2.width ||
    bbox1.y + bbox1.height < bbox2.y ||
    bbox1.y > bbox2.y + bbox2.height
  )
}
/**
 * 缩放矩形
 * @param width
 * @param height
 * @param maxWidth
 * @param maxHeight
 * @returns {{width: number, height: number}}
 */
export function scaleRectangle (width, height, maxWidth = 100, maxHeight = 100) {
  const aspectRatio = width / height
  let newWidth = width
  let newHeight = height

  if (newWidth > maxWidth) {
    newWidth = maxWidth
    newHeight = newWidth / aspectRatio
  }

  if (newHeight > maxHeight) {
    newHeight = maxHeight
    newWidth = newHeight * aspectRatio
  }

  return {
    width: newWidth,
    height: newHeight
  }
}
