<template>
  <div class="app-container">
    <!--表单组件-->
    <el-dialog 
      v-model="selectTaskDialogVisible" 
      :close-on-click-modal="false" 
      title="选择任务" 
      width="500px"
    >
      <el-form ref="formRef" :model="selectTaskForm" :rules="rules" size="small" label-width="80px">
        <el-form-item label="流程名称">
          <el-input v-model="procedureName" readonly style="width: 370px;" />
        </el-form-item>
        <el-form-item label="任务名称">
          <el-autocomplete
            v-model="selectTaskForm.taskName"
            :fetch-suggestions="queryTaskSearchAsync"
            placeholder="请输入内容"
            style="width: 370px;"
            @select="handleTaskSelect"
          >
            <template #default="{ item }">
              <div class="name">{{ item.taskName }}</div>
              <span class="addr">{{ item.taskNumber }}</span>
            </template>
          </el-autocomplete>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button text @click="selectTaskDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="doTaskSelected">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <div class="flow">
      <div class="content">
        <div v-loading="graphLoading" class="panel"  :style="{ width: `calc(100% - 4px - ${configWidth}px)` }">
          <div class="toolbar" :style="{ width: `calc(100% - 4px - ${configWidth}px)` }">
            <el-button-group class="toolbar-group">
              <el-tooltip class="item" effect="dark" content="新建调试任务" placement="top">
                <el-button size="small" :icon="DocumentAdd" plain @click="doCreateTaskDebug" />
              </el-tooltip>

              <el-tooltip class="item" effect="dark" content="重新加载当前任务" placement="top">
                <el-button
                  size="small"
                  :icon="Refresh"
                  @click="refreshTask()"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="加载已有任务" placement="top">
                <el-button
                  size="small"
                  :icon="Download"
                  @click="reloadTask()"
                />
              </el-tooltip>
              <el-button class="divider"></el-button>
              <el-tooltip class="item" effect="dark" content="放大" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomIn"
                  @click="graph?.zoom(0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="缩小" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomOut"
                  @click="graph?.zoom(-0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="调整到合适大小" placement="top">
                <el-button
                  size="small"
                  :icon="Aim"
                  @click="graph?.zoomToFit({ padding: 48, minScale: 0.8, maxScale: 4 });
                          graph?.centerContent()"
                />
              </el-tooltip>
              <el-button class="divider"></el-button>
              <el-tooltip class="item" effect="dark" content="连续调试模式,直到断点暂停" placement="top">
                <el-button
                  size="small"
                  :icon="DArrowRight"
                  :type="(debugMode==='CONTINUE')?'info':''"
                  plain
                  @click="changeDebugMode('CONTINUE')"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="单步调试模式,每次执行一个指令后，在下一个指令执行前暂停" placement="top">
                <el-button
                  size="small"
                  :icon="ArrowRight"
                  :type="(debugMode==='MONO')?'info':''"
                  plain
                  @click="changeDebugMode('MONO')"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="跳过当前指令,执行下一个指令,依据当前调试模式决定是否自动执行下一个指令" placement="top">
                <el-button
                  size="small"
                  :icon="TopRight"
                  :type="(debugMode==='MONO')?'info':''"
                  plain
                  @click="changeDebugMode('MONO')"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="重试当前指令,当前节点出现异常时可以使用该指令" placement="top">
                <el-button
                  size="small"
                  :icon="RefreshLeft"
                  :type="(debugMode==='MONO')?'info':''"
                  plain
                  @click="retryCurrentCommand('MONO')"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="设置断点,选中节点有效" placement="top">
                <el-button
                  size="small"
                  :icon="Pointer"
                  :type="(debugMode==='MONO')?'info':''"
                  plain
                  @click="setBreakPoint('MONO')"
                />
              </el-tooltip>
            </el-button-group>
          </div>
          <div id="container" />
        </div>
        <!-- 可拖动分隔条 -->
        <div class="resizer" @mousedown="startResize"></div>


        <el-tabs type="border-card" class="config" :style="{ width: `${configWidth}px`}">
          <el-tab-pane v-if="selectedNode">
            <template #label>
              <el-icon><Crop /></el-icon>节点属性
            </template>
            <ProcedureProperty v-if="selectedNode?.getData().type === 'PROCEDURE'" :cur-node="selectedNode" />
            <MethodProperty v-if="selectedNode?.getData().type === 'METHOD'" :cur-node="selectedNode" />
            <StepProperty v-if="selectedNode?.getData().type === 'STEP'" :cur-node="selectedNode" />
            <ActionProperty v-if="selectedNode?.getData().type === 'ACTION'" :cur-node="selectedNode" />
            <CommandProperty v-if="selectedNode?.getData().type === 'COMMAND'" :cur-node="selectedNode" />
          </el-tab-pane>
          <el-tab-pane>
            <template #label>
              <el-icon><Document /></el-icon>运行日志
            </template>
            <TaskRunningLogProperty :cur-node="rootNode" 
            :data-filter="{
              nodeType: 'TASK',
              id: selectedTaskId
            }" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, provide } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  ZoomIn,
  ZoomOut,
  Aim,
  Delete,
  Finished,
 DocumentAdd, 
 Document,
 Crop,
 Refresh,
 Download,
 VideoPlay,
 VideoPause,
 Pointer,
 RefreshLeft,
 TopRight,
 DArrowRight,
 ArrowRight,
 Switch } from '@element-plus/icons-vue'
import { X6, Graph, Platform } from '@antv/x6'
import { register } from '@antv/x6-vue-shape'
import { Selection } from '@antv/x6-plugin-selection'
import { Scroller } from '@antv/x6-plugin-scroller'

// 导入所有需要的组件和API
import { registerDataProcessingDag } from '@/views/components/x6/dag/index'
import ProcedureProperty from '../procedureconfig/ProcedureProperty.vue'
import MethodProperty from '../procedureconfig/MethodProperty.vue'
import StepProperty from './StepProperty.vue'
import ActionProperty from './ActionProperty.vue'
import CommandProperty from './CommandProperty.vue'
import TaskRunningLogProperty from './TaskRunningLogProperty.vue'

import ProcedureNode from '../procedureconfig/ProcedureNode.vue'
import { createNode as createProcedureNode, createNode, appendNode } from '@/hooks/useDagNode'

import useWebSocket from '@/hooks/useWebSocket'

import { add, edit, queryProcedureById } from '@/api/procedure'
import crudTask from '@/api/task'
import crudTaskDebug from '@/api/taskDebug'

// 注册 procedure-node
register({
  shape: 'procedure-node',
  width: 100,
  height: 120,
  component: ProcedureNode,
  ports: {
    groups: {
      in: {
        position: 'left',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: 'transparent',
            strokeWidth: 1,
            fill: 'transparent'
          }
        }
      },
      out: {
        position: {
          name: 'right',
          args: { dx: 0, dy: 0 }
        },
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: 'transparent',
            strokeWidth: 1,
            fill: 'transparent'
          }
        }
      }
    }
  }
})

registerDataProcessingDag()

// 状态定义
const store = useStore()
const route = useRoute()
const router = useRouter()

const graph = ref<Graph | null>(null)
const selectTaskDialogVisible = ref(false)
const selectTaskForm = reactive({
  taskName: ''
})
const graphLoading = ref(false)
const selectedNode = ref(null)
const rootNode = ref(null)
const dataStore = reactive({})
const debugMode = ref('MONO')
const procedureName = ref('')
const procedureId = ref(0)
const selectedTaskId = ref(null)
const addOrEdit = ref('add')
const selectTaskTimeout = ref<NodeJS.Timeout | null>(null)

const configWidth = ref(390) // 右侧属性面板宽度
const isResizing = ref(false)

// 验证规则
const rules = {
  tenantId: [
    { required: true, message: '租户ID不能为空', trigger: 'blur' }
  ]
}

// provide 数据
provide('getDataStore', () => dataStore)

// 初始化数据
const initData = async () => {
  addOrEdit.value = route.query.addOrEdit as string || 'add'
  procedureId.value = Number(route.query.id) || 0
  procedureName.value = route.query.name as string || ''
  selectedTaskId.value = Number(route.query.taskId) || 0
  
  if (addOrEdit.value === 'edit') {
    const res = await queryProcedureById({ id: procedureId.value })
    if (res == null) {
      Object.assign(dataStore, {})
    } else {
      Object.assign(dataStore, res)
      graph.value?.fromJSON(JSON.parse(dataStore.dagJson))
      if (graph.value) {
        graph.value.dataStore = dataStore
      }
      rootNode.value = getRootNode()
    }
  } else if (selectedTaskId.value > 0) {
    await doLoadTaskDebug()
  }
}

// 初始化图表
const initGraph = () => {
  const container = document.getElementById('container')
  if (!container) return
  
  graph.value = new Graph({
    container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    autoResize: true,
    background: false,
    snapline: false,
    panning: {
      enabled: false,
      eventTypes: ['rightMouseDown']
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    },
    grid: {
      type: 'dot',
      size: 10,
      visible: false,
      args: {
        color: '#c0c0c0',
        thickness: 1
      }
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF',
            strokeWidth: 4
          }
        }
      }
    },
    connecting: {
      snap: true,
      allowBlank: false,
      allowMulti: true,
      allowLoop: false,
      highlight: true,
      sourceAnchor: {
        name: 'left',
        args: {
          dx: Platform.IS_SAFARI ? 4 : 8
        }
      },
      targetAnchor: {
        name: 'right',
        args: {
          dx: Platform.IS_SAFARI ? 4 : -8
        }
      },
      createEdge() {
        return graph.value.createEdge({
          shape: 'data-processing-curve',
          attrs: {
            line: {
              strokeDasharray: '5 5'
            }
          },
          zIndex: -1
        })
      },
      validateConnection({ sourceMagnet, targetMagnet }) {
        if (!sourceMagnet || sourceMagnet.getAttribute('port-group') === 'in') {
          return false
        }
        if (!targetMagnet || targetMagnet.getAttribute('port-group') !== 'in') {
          return false
        }
        return true
      }
    }
  })

  graph.value.use(
    new Selection({
      rubberband: true,
      showNodeSelectionBox: false
    })
  )

  graph.value.use(
    new Scroller({
      enabled: true
    })
  )
}

// 初始化事件
const initEvents = () => {
  if (!graph.value) return
  
  graph.value.on('blank:click', () => {
    // Cancelling operations
  })
  
  graph.value.on('cell:selected', ({ cell }) => {
    if (cell.isNode()) {
      selectedNode.value = cell
      console.log('selectedNode is changed to:', cell.id)
    }
  })
  
  graph.value.on('cell:unselected', () => {
    selectedNode.value = null
  })
}

// WebSocket 相关方法
const {} = useWebSocket({
  url: ( id ) => `${import.meta.env.VITE_APP_WS_API}/webSocket/taskMonitorLog_${id}`,
  onMessage: ( message ) => {
    onTaskResponse(message)
  }
})

// 生命周期钩子
onMounted(() => {
  initGraph()
  initEvents()
  initData()
})

// 业务方法
const doCreateTaskDebug = async () => {
  graphLoading.value = true
  try {
    const data = { procedureId: procedureId.value, debugMode: debugMode.value }
    const res = await crudTaskDebug.createTaskDebug(data)
    selectedTaskId.value = res.id
    Object.assign(dataStore, res)
    if (graph.value) {
      graph.value.dataStore = dataStore
    }
    refreshGraph()
    ElNotification({
      title: '创建成功',
      type: 'success',
      duration: 2000
    })
    
    router.replace({ 
      path: route.path,
      query: {
        ...route.query,
        taskId: selectedTaskId.value
      }
    })
    
  } finally {
    graphLoading.value = false
  }
}

const doLoadTaskDebug = async () => {
  graphLoading.value = true
  try {
    const data = { 
      procedureId: procedureId.value, 
      taskId: selectedTaskId.value, 
      debugMode: debugMode.value 
    }
    const res = await crudTaskDebug.loadTaskDebug(data)
    selectedTaskId.value = res.id
    Object.assign(dataStore, res)
    if (graph.value) {
      graph.value.dataStore = dataStore
    }
    
    refreshGraph()
    ElNotification({
      title: '加载成功',
      type: 'success',
      duration: 2000
    })
  } finally {
    graphLoading.value = false
  }
}

// 工具方法
const getStoreObjectByNodeId = (nodeId: string) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  if (!Array.isArray(dataStore.methods)) {
    return null
  }
  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    if (!Array.isArray(methodObj.method.steps)) {
      continue
    }
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      if (!Array.isArray(stepObj.step.actions)) {
        continue
      }
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }
  return null
}

const getStoreParentObjectByNodeId = (nodeId: string) => {
  if (dataStore.dagNodeId === nodeId) {
    return null
  }
  if (!Array.isArray(dataStore.methods)) {
    return null
  }
  
  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return dataStore
    }
    if (!Array.isArray(methodObj.method?.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return methodObj
      }
      if (!Array.isArray(stepObj.step?.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return stepObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return actionObj
          }
        }
      }
    }
  }
  return null
}

const getRootNode = () => {
  let rootNode = null
  graph.value?.getNodes().forEach((graphNode) => {
    if (graphNode.getData().parentNodeId == null) {
      rootNode = graphNode
    }
  })
  return rootNode
}

const refreshGraph = () => {
  if (!graph.value) return
  // 设置为调试状态
  graph.value.dataStore.isDebugging = true
  const rootData = graph.value.dataStore
  const nodes = graph.value.getCells()
  
  for (const node of nodes) {
    graph.value.removeCell(node)
    graph.value.removeConnectedEdges(node)
  }
  
  let y4 = 40
  rootData.type = 'PROCEDURE'
  rootNode.value = createProcedureNode(
    graph.value,
    { x: 20, y: 100 },
    rootData
  )

  let firstMthPos
  let lastMthPos

  for (const idxMth in rootData.methodList) {
    const mth = rootData.methodList[idxMth]
    mth.type = 'METHOD'
    const mthNode = createNode(graph.value, { x: 20 + 50 * 1, y: 100 }, mth)
    appendNode(rootNode.value, mthNode)

    let firstStepPos
    let lastStepPos
    for (const idxStep in mth.steps) {
      const step = mth.steps[idxStep]
      step.type = 'STEP'
      const stepNode = createNode(graph.value, { x: 20 + 100 * 2, y: 100 }, step)
      appendNode(mthNode, stepNode)

      let firstActPos
      let lastActPos
      for (const idxAct in step.actions) {
        const act = step.actions[idxAct]
        act.type = 'ACTION'
        const actNode = createNode(graph.value, { x: 20 + 100 * 3, y: 100 }, act)
        appendNode(stepNode, actNode)
        
        const firstY4 = y4
        for (const idxCmd in act.commands) {
          const cmd = act.commands[idxCmd]
          cmd.type = 'COMMAND'
          const cmdNode = createNode(graph.value, { x: 20 + 100 * 4, y: 100 }, cmd)
          appendNode(actNode, cmdNode)
          y4 = y4 + 42
          cmdNode.position(cmdNode.position().x, y4)
          console.log('cmd.position.x=' + cmdNode.position().x + ',.position.y=' + y4 + ', name=' + cmd.name)
        }
        
        actNode.position(actNode.position().x, firstY4 + (y4 - firstY4 + 50) / 2)
        y4 = y4 + 42 + 4

        if (Number(idxAct) === 0) {
          firstActPos = actNode.position()
        }
        if (Number(idxAct) === step.actions.length - 1) {
          lastActPos = actNode.position()
        }
      }

      if (firstActPos){
        stepNode.position(stepNode.position().x, firstActPos.y + (lastActPos.y - firstActPos.y) / 2)
      }
      if (Number(idxStep) === 0) {
        firstStepPos = stepNode.position()
      }
      if (Number(idxStep) === mth.steps.length - 1) {
        lastStepPos = stepNode.position()
      }
    }

    if (firstStepPos){
      mthNode.position(mthNode.position().x, firstStepPos.y + (lastStepPos.y - firstStepPos.y) / 2)
    }
    if (Number(idxMth) === 0) {
      firstMthPos = mthNode.position()
    }
    if (Number(idxMth) === rootData.methodList.length - 1) {
      lastMthPos = mthNode.position()
    }
  }

  rootNode.value.position(rootNode.value.position().x, firstMthPos.y + (lastMthPos.y - firstMthPos.y) / 2)
  graphLoading.value = false

}

const onTaskResponse = (data: any) => {
  const dto = JSON.parse(data.msg)
  
  graph.value?.getNodes().forEach(node => {
    if (equalsLogType(dto, node.getData().type) && 
        node.getData().id === getIdByLogType(dto)) {
      const nodeData = node.getData()
      delete nodeData.changeState
      nodeData.status = dto.status
      nodeData.message = dto.message
      console.log('onTaskResponse: matched node:', nodeData.name, nodeData.status, nodeData.changeState)
      node.setData({ ...nodeData, changeState: 'CHANGE_STATUS' })
    }
  })
}

const equalsLogType = (dto: any, logType: string) => {
  if (logType === 'PROCEDURE') {
    return dto.logType === 'TASK'
  }
  return dto.logType === logType
}

const getIdByLogType = (dto: any) => {
  const logType = dto.logType
  switch(logType) {
    case 'TASK': return dto.taskId
    case 'METHOD': return dto.taskMethodId
    case 'STEP': return dto.taskStepId
    case 'ACTION': return dto.taskActionId
    case 'COMMAND': return dto.taskCommandId
    default: return null
  }
}

const queryTaskSearchAsync = (queryString: string, cb: Function) => {
  if (selectTaskTimeout.value) {
    clearTimeout(selectTaskTimeout.value)
  }
  selectTaskTimeout.value = setTimeout(() => {
    crudTask.queryForSelection({ 
      procedureId: procedureId.value, 
      taskName: queryString 
    }).then(res => {
      cb(res)
    })
  }, 1000 * Math.random())
}

const handleTaskSelect = (item: any) => {
  selectTaskForm.taskName = item.taskName
  selectedTaskId.value = item.id
}

const doTaskSelected = () => {
  router.replace({ 
    path: route.path,
    query: {
      ...route.query,
      taskId: selectedTaskId.value
    }
  })
  selectTaskDialogVisible.value = false
  doLoadTaskDebug()
}

const changeDebugMode = (mode: string) => {
  debugMode.value = mode
  const data = { 
    taskId: selectedTaskId.value, 
    debugMode: debugMode.value 
  }
  crudTaskDebug.setDebugMode(data).then(() => {
    ElNotification({
      title: `设置调试模式${mode}成功`,
      type: 'success',
      duration: 2000
    })
  })
}

const setBreakPoint = (nodeId: string) => {
  if (!selectedNode.value){
    return
  }
  
  const data = {
  }
  const nodeData = selectedNode.value.data
  const nodeType = nodeData.type
  data.taskId = nodeData.taskId
  if (nodeType === 'ACTION') {
    data.taskActionId = nodeData.id
  }else if (nodeType === 'COMMAND') {
    data.taskCommandId = nodeData.id
  }else if (nodeType === 'STEP') {
    data.taskStepId = nodeData.id
  }else if (nodeType === 'METHOD') {
    data.taskMethodId = nodeData.id
  }
  crudTaskDebug.setBreakPoint(data).then(() => {
    ElNotification({
      title: `设置断点成功`,
      type:'success',
      duration: 2000
    })
  })
}

const retryCurrentCommand = () => {
  if (!selectedNode.value){
    return
  }
  crudTaskDebug.doRetryCommand(selectedNode.value.data).then(res => {
    ElNotification({
      title: '执行成功',
      type: 'success',
      duration: 2000
    })
  })
}

const reloadTask = () => {
  selectTaskDialogVisible.value = true
}

const refreshTask = () => {
  if (selectedTaskId.value && selectedTaskId.value > 0) {
    doLoadTaskDebug()
  }
}

// 添加拖动相关的方法
const startResize = (e) => {
  isResizing.value = true
  
  const handleMouseMove = (moveEvent) => {
    if (isResizing.value) {
      // 计算新的右侧面板宽度
      const containerWidth = document.querySelector('.content').offsetWidth
      const newWidth = containerWidth - moveEvent.clientX
      
      // 设置最小和最大宽度限制
      if (newWidth >= 200 && newWidth <= 500) {
        configWidth.value = newWidth
      }
    }
  }
  
  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    
    // 调整图表大小以适应新的布局
   // if (graph.value) {
   //   const container = document.getElementById('container')
   //   graph.value.resize(container.offsetWidth, container.offsetHeight)
   // }
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  
  // 阻止默认行为和冒泡
  e.preventDefault()
  e.stopPropagation()
}

// 导出需要的方法供模板使用
defineExpose({
  doCreateTaskDebug,
  reloadTask,
  refreshTask,
  changeDebugMode,
  handleTaskSelect,
  doTaskSelected
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped >

.app-container {
  width: 100%;
  height: calc(100vh - 50px);
  padding:0px;
}
.app-container-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

.flow {
  /* width: 100vw; */
  width: 100%;
  height: calc(100vh - 50px);
}
.flow-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  #stencil {
    width: 260px;
    height: 100%;
    position: relative;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
  }

  .panel {
    width: calc(100% - 390px);
    height: 100%;
    background-color: #dff0ff;
  }

  .panel .toolbar {
    width: calc(100% - 407px);
    height: 38px;
    padding-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
   // background-color: #f7f9fb80;
   // border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position:absolute;
    z-index: 1900;

    .divider {
      width:8px;
      padding:5px 4px;
      background-color: #FFFFFF;
    }
  }
  .toolbar-group {
    border-radius: 20px;
    background-color: #FFFFFF;
    padding: 0px 20px;
  }
  .panel #container {
    width: 100%;
    height: calc(100% - 38px);
  }

// 添加分隔条样式
.resizer {
  width: 4px;
  height: 100%;
  background-color: #e0e0e0;
  cursor: col-resize;
  transition: background-color 0.2s;
  z-index: 1000;
  
  &:hover, &:active {
    background-color: #1890ff;
  }
}

  .config {
    width: 390px;
    height: 100%;
    padding: 0 0px;
    border-left: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    
    :deep(.el-tabs__content) {
      overflow-y: auto;
    }
  }

  .node_icon {
    color: darkcyan;
  }

</style>
