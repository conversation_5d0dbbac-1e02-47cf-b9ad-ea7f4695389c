import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/deviceInstanceCmd',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/deviceInstanceCmd/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/deviceInstanceCmd',
    method: 'put',
    data
  })
}

export function executeCommand(data) {
  return request({
    url: 'api/deviceInstanceCmd/executeCommand',
    method: 'post',
    data
  })
}

export function executeCommandByControlCode(data) {
  return request({
    url: 'api/deviceInstanceCmd/executeCommandByControlCode',
    method: 'post',
    data
  })
}
export default { add, edit, del, executeCommand, executeCommandByControlCode }
