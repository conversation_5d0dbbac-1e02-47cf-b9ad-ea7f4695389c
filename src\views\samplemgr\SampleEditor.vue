<template>

  <!--表单组件-->
  <el-dialog 
  align-center
  :close-on-click-modal="false" 
  :before-close="onCloseDialog" 
  v-model="localDialogVisible" 
  :title="$t('编辑样品')" 
  @open="onDialogOpen"
  append-to-body 
  width="500px"
  >
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="样本名称" prop="name">
        <el-autocomplete
          v-model="form.name"
          :fetch-suggestions="onSampleSelect"
          placeholder="请输入样品名称"
          value-key="name"
          style="width: 370px;"
          @select="handleSelectSample"
        />
      </el-form-item>
      <el-form-item label="样本描述" prop="description">
        <el-input v-model="form.description" style="width: 370px;" />
      </el-form-item>
      <el-form-item :label="$t('样本分类')">
        <el-select
          v-model="form.category"
          value-key="name"
          filterable
          clearable
          allow-create
          default-first-option
          placeholder="请选择分类"
          style="width: 370px;"
          @change="onChangeCategory"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.name"
            :label="item.name"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('所属客户')">
        <el-select
          v-model="form.customer"
          value-key="name"
          filterable
          clearable
          allow-create
          default-first-option
          placeholder="请选择客户"
          style="width: 370px;"
          @change="onChangeCustomer"
        >
          <el-option
            v-for="item in customerOptions"
            :key="item.name"
            :label="item.name"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('检测流程')">
        <el-table
          ref="tableTaskPrepare"
          v-loading="taskPrepareLoading"
          :data="form.taskPrepares"
          size="small"
          max-height="200px"
          highlight-current-row
          style="width: 370px;"
          @current-change="handleCurrentChange"
        >
          <el-table-column prop="schedureName" label="流程名称">
            <template #default="scope">
              <el-select
                v-model="scope.row.procedure"
                value-key="id"
                filterable
                default-first-option
                placeholder="请选择"
              >
                <el-option
                  v-for="item in procedureOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="rfTag" label="RF标签">
            <template #default="scope">
              <div :class="{ 'flowing-border': scope.row.selected && scope.row.status ==='WAIT_TAG_INPUT' }">
                {{ getRfTagText(scope.row) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="60" />
          <el-table-column v-if="checkPer(['admin','sample:edit','sample:del'])" :label="$t('操作')" width="80px" align="center">
            <template #header>
              <span>{{ $t('操作') }}</span>
              <el-button :icon="Plus" class="operation" @click="addTaskPrepare(form.taskPrepares)" />
            </template>
            <template #default="scope">
              <el-button :icon="Delete" class="operation" @click="deleteTaskPrepare(scope.row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onCloseDialog">取消</el-button>
      <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">保存</el-button>
      <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">保存并继续录入</el-button>
    </template>
  </el-dialog>

</template>

<script setup>
import { ref, reactive, watch, onMounted, computed, inject, defineModel, nextTick } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import crudSample from '@/api/sample'
import crudSampleCategory from '@/api/sampleCategory'
import crudSampleCustomer from '@/api/sampleCustomer'
import crudProcedure from '@/api/procedure'
import crudTaskPrepare from '@/api/taskPrepare'
import { debounce } from '@/utils'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

// 依赖注入
const checkPer = inject('checkPer')


const props = defineProps({
  form: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['on-success'])

//const dialogVisible = ref(false)
//const modelValue = defineModel({ type: Boolean, default: false })
const dialogVisible = defineModel({ type: Boolean, default: false })

// 修改模板中的绑定
const localDialogVisible = computed({
  get: () => dialogVisible.value,
  set: (val) => {
    dialogVisible.value = val
  }
})

const checkSampleName = async (rule, value, callback) => {
  if (!value) return callback()
  
  try {
    const res = await crudSample.checkSampleName({ 
      id: form.id,  // form 是 reactive 对象，不需要 .value
      name: value 
    })
    res.errorLevel === 'ERROR' 
      ? callback(new Error(res.errorCode)) 
      : callback()
  } catch (error) {
    callback(new Error('验证失败'))
  }
}

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '样品名称不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
    { validator: checkSampleName, trigger: 'blur' }
  ]
}

// 响应式数据
const categoryOptions = ref([])
const customerOptions = ref([])
const procedureOptions = ref([])
const taskPrepareView = ref({})
const taskPrepareLoading = ref(false)
const formRef = ref(null)
const tableTaskPrepare = ref(null)

// CRUD配置
const { crud, CRUD, form } = useCrud({
  title: '样品管理',
  url: 'api/sample',
  idField: 'id',
  sort: 'id,desc',
  crudMethod: { ...crudSample },
  formRef,
  rules,
  defaultForm: () => ({
    id: null,
    number: null,
    name: null,
    description: null,
    category: { id: null, name: null },
    customer: { id: null, name: null },
    taskPrepares: [],
    deleteFlag: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  })
})

// 生命周期
onMounted(initData)

// 初始化数据
async function initData() {
  
  await Promise.all([
    loadCategories(),
    loadCustomers(),
    loadProcedures()
  ])

  
}

// 数据加载方法
async function loadCategories() {
  categoryOptions.value = await crudSampleCategory.selectSampleCategories()
}

async function loadCustomers() {
  customerOptions.value = await crudSampleCustomer.selectSampleCustomers()
}

async function loadProcedures() {
  procedureOptions.value = await crudProcedure.queryProceduresForSelection()
}

// 核心业务方法
const onCloseDialog = () => {
  crud.cancelCU()
  dialogVisible.value = false
}

const getTaskPrepareStatusTagType = (item) => {
  if (item.status === 'SUCCESS') {
    return 'success'
  }

  return ''
}

// 修改 onSampleSelect 方法
const onSampleSelect = (keyword, cb) => {
  //const sampleLoading = ref(false)
  //sampleLoading.value = true
  debounce(() => {
    crudSample.querySamplesForSelection({ name: keyword }).then(res => {
   //   sampleLoading.value = false
      cb(res)
    })
  }, 30)()
}

// 2. 修改 onChangeCategory 方法
const onChangeCategory = (val) => {
  if (typeof val === 'string') {
    const newVal = { id: null, name: val }
    categoryOptions.value.push(newVal)
    nextTick(() => {
      form.category = newVal  // 移除 .value
    })
  }
}

// 3. 修改 onChangeCustomer 方法
const onChangeCustomer = (val) => {
  if (typeof val === 'string') {
    const newVal = { id: null, name: val }
    customerOptions.value.push(newVal)
    nextTick(() => {
      form.customer = newVal  // 移除 .value
    })
  }
}

// 4. 修改 addTaskPrepare 方法
const addTaskPrepare = (taskprepares) => {
  const defaultProc = procedureOptions.value[0] || { id: null, name: null }
  const newLine = { procedure: defaultProc, rfTag: null, status: 'DRAFT', selected: true }
  form.taskPrepares.push(newLine)  // 移除 .value
  tableTaskPrepare.value?.setCurrentRow(newLine)
}

// 5. 修改 handleCurrentChange 方法
const handleCurrentChange = (currentRow, oldCurrentRow) => {
  currentRow.selected = true
  if (oldCurrentRow) {
    oldCurrentRow.selected = false
  }
  if (!currentRow.rfTag) {
    crudSample.submitSampleTagScanning(form).then(res => {  // 移除 .value
      Object.assign(form, res)  // 移除 .value
      crud.refresh()
      getCurrentTaskPrepareView(res)
    })
  }
}

// 6. 修改 updateTaskPrepare 方法
const updateTaskPrepare = () => {
  let curTaskPrepare = null
  if (taskPrepareView.value.taskPrepareDtos && taskPrepareView.value.taskPrepareDtos.length > 0) {
    curTaskPrepare = taskPrepareView.value.taskPrepareDtos[0]
  }
  if (!curTaskPrepare) {
    return null
  }

  for (var i in form.taskPrepares) {  // 移除 .value
    const fTp = form.taskPrepares[i]  // 移除 .value
    if (fTp.id === curTaskPrepare.id) {
      if (fTp.status !== curTaskPrepare.status || fTp.rfTag !== curTaskPrepare.rfTag) {
        curTaskPrepare.selected = true
        form.taskPrepares.splice(i, 1, curTaskPrepare)  // 移除 .value
      }
      return curTaskPrepare
    }
  }
  return null
}

// 7. 修改 validateTaskPrepares 方法
function validateTaskPrepares() {
  if (form.taskPrepares.some(tp => !tp.procedure?.id)) {  // 移除 .value
    ElMessage.warning('请为所有检测流程选择检测方法')
    return false
  }
  return true
}

crud.hooks[CRUD.HOOK.afterSubmit] = () => {
  emit('on-success', form)
  dialogVisible.value = false
}
const onDialogOpen = () => {
  debugger
  if (props.form && props.form.id) {
      crud.toEdit(props.form)
    } else {
      crud.toAdd()
    }
}

watch(() => dialogVisible.value, (newVal, oldVal) => {
  
})
// 修改 defineModel 的使用方式
//const modelValue = defineModel()

</script>

<style scoped>
.operation {
  width:18px;
  padding: 2px 2px;
  margin-left: 4px;
}
.flowing-border {
    position: relative;
    padding: 4px 4px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
  }

  /* 动态边框效果 */
  .flowing-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    padding: 2px; /* 边框宽度 */
    background: linear-gradient(90deg,
      #ff0000, #00ff00, #0000ff, #ff0000); /* 渐变色条 */
    background-size: 300% 100%;
    animation: flowing 3s linear infinite;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude; /* 仅保留边框部分 */
  }

  @keyframes flowing {
    from {
      background-position: 0% 50%;
    }
    to {
      background-position: 100% 50%;
    }
  }
</style>
