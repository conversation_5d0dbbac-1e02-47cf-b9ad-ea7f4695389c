<template>

  <div class="button-wrapper">
    <div :style="backgroundStyle" 
    :class="isDisabled?'background-container-disabled':'background-container'">
      <div
        v-if="doorStatus==='LOCKED'"
        :title="tips"
        class="button-icon button-icon-lock"
        @click="openDoor"
      />
      <!--
      <div
        v-if="doorStatus==='UNLOCKED'"
        :title="'点击上锁,或者请手工开门后重新关门自动上锁'"
        class="button-icon button-icon-unlock-closed"
        @click="openDoor"
      />
      -->
      <div
        v-if="doorStatus==='UNLOCKED'"
        :title="'点击上锁,或手工关门后自动上锁'"
        class="button-icon button-icon-unlock"
        @click="openDoor"
      />
      <div
        v-if="!(doorStatus==='LOCKED'||doorStatus==='UNLOCKED')"
        :title="'未知异常，该功能禁用'"
        class="button-icon button-icon-unlock-unknown"
      />

    </div>

    <div class="status-text"><span>{{doorStatusText}}</span></div>
  </div>

</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElNotification } from 'element-plus'

import { Graph } from '@antv/x6'
import { mapGetters } from 'vuex'

import LockIcon from '@/assets/images/lock.svg'
import UnlockIcon from '@/assets/images/unlock.svg'
import UnlockClosedIcon from '@/assets/images/unlock_closed.svg'
import UnlockUnknownIcon from '@/assets/images/unlock_unknown.svg'

const store = useStore()

// 计算属性
const task = computed(() => store.state.task)
const doorStatus = computed(() => store.state.task.ctrlStatus.doorStatus)

const doorStatusText = computed(() => {
  const status = store.state.task.ctrlStatus.doorStatus
  if (status ==='LOCKED') {
    return '门已上锁'
  }else if (status ==='UNLOCKED') {
    return '门已解锁'
  }else {
    return '状态未知'
  }
})
const isDisabled = computed(() => {
  if (store.state.globalSetting.globalSetting.debugMode){
    return false
  }
  return !(
    store.state.task.ctrlStatus.doorStatus ==='LOCKED' ||
    store.state.task.ctrlStatus.doorStatus ==='UNLOCKED'
  ) || 
 !(store.state.task.ctrlStatus.isTaskStarted === false)
})


const tips = computed(() => {
  let msg = ''
  if (doorStatus.value === 'LOCKED') {
    if (isDisabled.value && store.state.task.ctrlStatus.isTaskStarted) {
      msg += '任务开启中,请暂停任务后开锁。'
    }else{
      msg += '点击开锁'
    }
  }else if (doorStatus.value === 'UNLOCKED') {
    msg += '点击上锁,或手工关门后自动上锁'
  }
  return msg
})

const backgroundStyle = computed(() => ({
  backgroundImage: `url(${import('@/assets/icons/svg/status-btn-bg.svg')})`
}))

const openDoor = () => {
  if (isDisabled.value){
    return
  }
  if (doorStatus.value === 'LOCKED') {
    store.dispatch('setDoorStatus', 'UNLOCK')
  } else{
    store.dispatch('setDoorStatus', 'LOCK')
  }
}

/*
export default {
  props: {
    graph: {
      default: () => {
        return {}
      },
      type: Graph
    }
  },
  data() {
    return {
      LockIcon: LockIcon,
      UnlockIcon: UnlockIcon,
      UnlockClosedIcon: UnlockClosedIcon,
      UnlockUnknownIcon: UnlockUnknownIcon,
      startAnalyseDialogVisible: false,
      procedureOptions: [{ label: '新建流程', value: 0 }],
      startState: 'pause',
      startIcon: this.imageAccessUrl + '%E5%90%AF%E5%8A%A8%E5%88%86%E6%9E%90-20240620031632806.svg',
      websocketState: 'NONE' // OPENED,CLOSED
    }
  },
  computed: {
    ...mapGetters([
      'imagesUploadApi',
      'imageAccessUrl',
      'baseApi',
      'motion'
    ]),
    backgroundStyle() {
      return {
        backgroundImage: `url(${import('@/assets/icons/svg/status-btn-bg.svg')})`
      }
    },
    buttonLockIconStyle() {
      return {
        backgroundImage: `url(${import('@/assets/images/lock.svg')})`
      }
    },
    buttonUnlockClosedIconStyle() {
      return {
        backgroundImage: `url(${import('@/assets/images/unlock_closed.svg')})`
      }
    },
    buttonUnlockIconStyle() {
      return {
        backgroundImage: `url(${import('@/assets/images/unlock.svg')})`
      }
    },
    buttonUnlockUnknownIconStyle() {
      return {
        backgroundImage: `url(${import('@/assets/images/unlock_unknown.svg')})`
      }
    }
  },
  created() {
    this.$store.dispatch('getCurrentMotionInfo')
  },
  beforeDestroy() {
    // this.moveDropdownToBody()
  },
  methods: {
    openDoor() {
      debugger
      const _this = this
      if (this.motion.doorStatus === 1) {
        this.motion.toOpenDoor = false
      } else if (this.motion.doorStatus === 0) {
        this.motion.toOpenDoor = true
      }

      this.$store.dispatch('openDoor', this.motion).then(() => {
        console.log('opened Door(' + _this.motion.toOpenDoor + '):' + _this.doorStatus)
      })
    }
  }
}
  */
</script>

<style lang="scss" scoped>
    .status-board-container {
      display: flex;
      justify-content: center; /* 水平居中 */
      align-items: center; /* 垂直居中 */
      flex-wrap: wrap; /* 换行布局 */
      gap: 20px; /* 元素之间的间距 */
      text-align: center; /* 文本居中对齐 */
    }

    .button-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center; /* 水平居中 */
      gap: 10px; /* 按钮和文本之间的间距 */
    }

    .background-container {
      width: 60px; /* 宽度 */
      height: 60px; /* 高度 */
      border-radius: 50%; /* 圆形 */
      background-size: cover; /* 背景图片适应按钮 */
      background-position: center; /* 背景图片居中 */
      background-image:none;
      border: 2px solid #59AED2; /* 边框颜色和样式 */
      display: flex;
      align-items: center; /* 内部内容垂直居中 */
      justify-content: center; /* 内部内容水平居中 */
      cursor: pointer; /* 鼠标移到按钮上变为手指 */
    }
    .background-container:hover {
      background-image: radial-gradient(circle at bottom, #FFFFFF,#399AC3 75%, #0580B4 125%,);
    }
    
    .button-icon {
      width: 40px;
      height: 40px;
      background-size: contain;
    }
    .button-icon-lock {
      background-image: url('@/assets/images/lock.svg');
    }
    .button-icon-unlock {
      background-image: url('@/assets/images/unlock.svg');
    }
    .button-icon-unlock-closed {
      background-image: url('@/assets/images/unlock_closed.svg');
    }
    .button-icon-unlock-unknown {
      background-image: url('@/assets/images/unlock_unknown.svg');
    }

    .background-container-disabled {
      width: 60px; /* 宽度 */
      height: 60px; /* 高度 */
      border-radius: 50%; /* 圆形 */
      background-size: cover; /* 背景图片适应按钮 */
      background-position: center; /* 背景图片居中 */
      background-image:none;
      border: 2px solid #858585; /* 边框颜色和样式 */
      display: flex;
      align-items: center; /* 内部内容垂直居中 */
      justify-content: center; /* 内部内容水平居中 */
      cursor: pointer; /* 鼠标移到按钮上变为手指 */
    }
    .status-text {
      font-size: 16px;
      font-weight: bold;
      color:#4f96c5;
    }
    .status-text-disabled {
      font-size: 16px;
      font-weight: bold;
      color:#888888;
    }
    .img{
      width:40px;
      height:40px;
      color:red;
      fill: #878787;
    }

.el-select-dropdown {
  position: fixed !important;
  z-index: 9999 !important;
  top: auto !important;
  left: auto !important;
}
</style>
