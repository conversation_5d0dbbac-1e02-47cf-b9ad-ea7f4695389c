import { reactive, ref, watch, toRefs } from 'vue';
import { initData } from '@/api/data'; // 假设接口函数
import { ElNotification } from 'element-plus';
import { nextTick } from 'vue';

const CRUD = {}

/**
 * CRUD钩子
 */
CRUD.HOOK = {
  /** 刷新 - 之前 */
  beforeRefresh: 'beforeCrudRefresh',
  /** 刷新 - 之后 */
  afterRefresh: 'afterCrudRefresh',
  /** 删除 - 之前 */
  beforeDelete: 'beforeCrudDelete',
  /** 删除 - 之后 */
  afterDelete: 'afterCrudDelete',
  /** 删除取消 - 之前 */
  beforeDeleteCancel: 'beforeCrudDeleteCancel',
  /** 删除取消 - 之后 */
  afterDeleteCancel: 'afterCrudDeleteCancel',
  /** 新建 - 之前 */
  beforeToAdd: 'beforeCrudToAdd',
  /** 新建 - 之后 */
  afterToAdd: 'afterCrudToAdd',
  /** 编辑 - 之前 */
  beforeToEdit: 'beforeCrudToEdit',
  /** 编辑 - 之后 */
  afterToEdit: 'afterCrudToEdit',
  /** 开始 "新建/编辑" - 之前 */
  beforeToCU: 'beforeCrudToCU',
  /** 开始 "新建/编辑" - 之后 */
  afterToCU: 'afterCrudToCU',
  /** "新建/编辑" 验证 - 之前 */
  beforeValidateCU: 'beforeCrudValidateCU',
  /** "新建/编辑" 验证 - 之后 */
  afterValidateCU: 'afterCrudValidateCU',
  /** 添加取消 - 之前 */
  beforeAddCancel: 'beforeCrudAddCancel',
  /** 添加取消 - 之后 */
  afterAddCancel: 'afterCrudAddCancel',
  /** 编辑取消 - 之前 */
  beforeEditCancel: 'beforeCrudEditCancel',
  /** 编辑取消 - 之后 */
  afterEditCancel: 'afterCrudEditCancel',
  /** 提交 - 之前 */
  beforeSubmit: 'beforeCrudSubmitCU',
  /** 提交 - 之后 */
  afterSubmit: 'afterCrudSubmitCU',
  afterAddError: 'afterCrudAddError',
  afterEditError: 'afterCrudEditError',
  afterDeleteError: 'afterDeleteError'
}

/**
 * CRUD状态
 */
CRUD.STATUS = {
  NORMAL: 0,
  PREPARED: 1,
  PROCESSING: 2
}

/**
 * CRUD通知类型
 */
CRUD.NOTIFICATION_TYPE = {
  SUCCESS: 'success',
  WARNING: 'warning',
  INFO: 'info',
  ERROR: 'error'
}

/**
 * 调用 crud 实例中的钩子函数
 * @param {Object} crud crud实例
 * @param {String} hook 钩子名称
 * @param {Array} args 参数数组
 * @returns {*} 钩子函数的返回值
 */
function callVmHook(crud, hook) {
  // 如果钩子函数存在
  if (crud.hooks[hook]) {
    // 使用 apply 调用钩子函数，传入 crud 实例作为第一个参数
    const nargs = [crud]
    for (let i = 2; i < arguments.length; ++i) {
      nargs.push(arguments[i])
    }
    const result = crud.hooks[hook].apply(null,nargs)
    // 如果钩子函数返回 false，表示阻止后续操作
    if (result === false) {
      return false
    }
    // 如果钩子函数返回 Promise，等待其完成
    if (result && result.then) {
      return result.then(res => {
        return res !== false
      }).catch(() => {
        return false
      })
    }
    // 默认返回 true，表示继续执行
    return true
  }
  // 如果钩子函数不存在，返回 true 表示继续执行
  return true
}

/**
 * useCrud Hook
 * @param {Object} options - 配置项
 * @returns {Object} 包括状态和方法
 */
export function useCrud(options = {}) {
  const defaultOptions = {
    tag: 'default',
    idField: 'id',
    title: '',
    url: '',
    data: [],
    selections: [],
    query: {},
    params: {},
    form: {},
    formRef: null,
    tableRef: null,
    rules: [],
    hooks: {},
    dialogVisible: false,
    defaultForm: () => ({}),
    sort: ['id,desc'],
    time: 50,
    crudMethod: {
      add: async () => {},
      del: async () => {},
      edit: async () => {},
      get: async () => {}
    },
    optShow: {
      add: true,
      edit: true,
      del: true,
      download: true,
      reset: true
    },
    props: {
      searchToggle: true
    },
    queryOnPresenterCreated: true,
    debug: false
  };

  const config = { ...defaultOptions, ...options };
  if (!options.form){
    config.form = {...config.defaultForm}
  }
  if (!options.defaultQuery){
    config.defaultQuery = {...config.query}
  }
  
  // 响应式状态
  const crudState = reactive({
    ...config,
    data: [],
    dataStatus:{},
    selections: [],
    status: {
      add: 0,
      edit: 0,
      get cu() {
        if (crudState.status.add === 0 && crudState.status.edit === 0) return 0;
        if (crudState.status.add === 1 || crudState.status.edit === 1) return 1;
        if (crudState.status.add === 2 || crudState.status.edit === 2) return 2;
        return 0;
      },
      // 标题
      get title() {
        return crudState.status.add > CRUD.STATUS.NORMAL ? `新增${crudState.title}` : crudState.status.edit > CRUD.STATUS.NORMAL ? `编辑${crudState.title}` : crudState.title
      }
    },
    page: {
      page: 1,
      size: 10,
      total: 0
    },
    loading: false,
    downloadLoading: false,
    delAllLoading: false,
    msg: {
      submit: '提交成功',
      add: '新增成功',
      edit: '编辑成功',
      del: '删除成功'
    },

    getTableRef: () => crudState.tableRef,
    // 添加树形表格支持
    props: {
      ...config.props,
      children: 'children',
      hasChildren: 'hasChildren'
    }
  });

  // 方法定义
  const methods = {
    // 通用通知
    notify(message, type = 'info') {
      //ElMessage({ message, type, duration: 2500 , plain: true});
      ElNotification({
        title: message,
        type: type
      })
    },

    // 搜索
    async toQuery() {
      crudState.page.page = 1;
      await methods.refresh();
    },
    /**
     * 重置查询参数
     * @param {Boolean} toQuery 重置后进行查询操作
     */
    async resetQuery(toQuery = true) {
      const defaultQuery = JSON.parse(JSON.stringify(crudState.defaultQuery))
      const query = crudState.query
      Object.keys(query).forEach(key => {
        query[key] = defaultQuery[key]
      })
      // 重置参数
      crudState.params = {}
      if (toQuery) {
        crudState.toQuery()
      }
    },
    // 刷新数据
    async refresh() {
      if (!(await callVmHook(crudState, CRUD.HOOK.beforeRefresh))) return;
      
      crudState.loading = true;
      try {
        const params = methods.getQueryParams();
        
        const result = await initData(crudState.url, params);
        
        crudState.data = result.content;
        crudState.page.total = result.totalElements;
        methods.resetDataStatus();
      } catch (error) {
        
        methods.notify('数据加载失败', 'error');
        console.log('数据加载失败:' + error)
      } finally {
        crudState.loading = false;
        callVmHook(crudState, CRUD.HOOK.afterRefresh);
      }
    },

    // 启动新增
    toAdd() {
      methods.resetForm();
      if (!(callVmHook(crudState, CRUD.HOOK.beforeToAdd, crudState.form) 
        && callVmHook(crudState, CRUD.HOOK.beforeToCU, crudState.form))) {
        return
      }
      crudState.status.add = CRUD.STATUS.PREPARED
      callVmHook(crudState, CRUD.HOOK.afterToAdd, crudState.form)
      callVmHook(crudState, CRUD.HOOK.afterToCU, crudState.form)

      crudState.dialogVisible = true;
    },

    // 启动编辑
    toEdit(row) {
      methods.resetForm(row);
      if (!(callVmHook(crudState, CRUD.HOOK.beforeToEdit, crudState.form) && 
            callVmHook(crudState, CRUD.HOOK.beforeToCU, crudState.form))) {
              return;
            }
      
      crudState.status.edit = CRUD.STATUS.PREPARED;
      const id = methods.getDataId(row);
      crudState.dataStatus[id] = crudState.dataStatus[id] || {};
      crudState.dataStatus[id].edit = CRUD.STATUS.PREPARED;
      crudState.dialogVisible = true;
    
      callVmHook(crudState, CRUD.HOOK.afterToEdit, crudState.form);
      callVmHook(crudState, CRUD.HOOK.afterToCU, crudState.form);

    },
    /**
     * 取消新增/编辑
     */
    cancelCU() {
      
      const addStatus = crudState.status.add
      const editStatus = crudState.status.edit
      if (addStatus === CRUD.STATUS.PREPARED) {
        if (!callVmHook(crudState, CRUD.HOOK.beforeAddCancel, crudState.form)) {
          return
        }
        crudState.status.add = CRUD.STATUS.NORMAL
      }
      if (editStatus === CRUD.STATUS.PREPARED) {
        if (!callVmHook(crudState, CRUD.HOOK.beforeEditCancel, crudState.form)) {
          return
        }
        crudState.status.edit = CRUD.STATUS.NORMAL
        const id = methods.getDataId(crudState.form)
        const dataStatus = methods.getDataStatus(id)
        methods.getDataStatus(id).edit = CRUD.STATUS.NORMAL
      }
      methods.resetForm()
      if (addStatus === CRUD.STATUS.PREPARED) {
        callVmHook(crudState, CRUD.HOOK.afterAddCancel, crudState.form)
      }
      if (editStatus === CRUD.STATUS.PREPARED) {
        callVmHook(crudState, CRUD.HOOK.afterEditCancel, crudState.form)
      }
      // 清除表单验证
      //if (crudState.findVM('form').$refs['form']) {
      //  crudState.findVM('form').$refs['form'].clearValidate()
      //}
      crudState.dialogVisible = false
    },
    // 提交新增或编辑
    async submitCU() {
      
      if (!(await callVmHook(crudState, CRUD.HOOK.beforeValidateCU))) return;
      
        const valid = await crudState.formRef?.validate();
        if (!valid) return;
        
      try {
        if (!(await callVmHook(crudState, CRUD.HOOK.afterValidateCU))) return;
        
        const isAdd = crudState.status.add === CRUD.STATUS.PREPARED;
        if (!(await callVmHook(crudState, CRUD.HOOK.beforeSubmit))) return;
        
        const action = isAdd ? 'add' : 'edit';
        crudState.status[action] = CRUD.STATUS.PROCESSING;
        
        const res = await crudState.crudMethod[action](crudState.form);
        methods.notify(isAdd ? crudState.msg.add : crudState.msg.edit, 'success');
        
        await methods.refresh();
        crudState.dialogVisible = false;
        methods.resetForm();
        
        await callVmHook(crudState, CRUD.HOOK.afterSubmit, res);
      } catch (error) {
        const action = crudState.status.add ? 'add' : 'edit';
        crudState.status[action] = CRUD.STATUS.PREPARED;
        callVmHook(crudState, action === 'add' ? CRUD.HOOK.afterAddError : CRUD.HOOK.afterEditError);
      } finally{
        const action = crudState.status.add ? 'add' : 'edit';
        crudState.status[action] = CRUD.STATUS.NORMAL;
      }
    },
    /**
     * 取消删除
     * @param {*} data 数据项
     */
    cancelDelete(data) {
      if (!callVmHook(crudState, CRUD.HOOK.beforeDeleteCancel, data)) {
        return
      }
      crudState.getDataStatus(crudState.getDataId(data)).delete = CRUD.STATUS.NORMAL
      callVmHook(crudState, CRUD.HOOK.afterDeleteCancel, data)
    },
    // 删除
    async doDelete(row) {
      let delAll = false
      let dataStatus
      const ids = []
      if (row instanceof Array) {
        delAll = true
        row.forEach(val => {
          ids.push(crudState.getDataId(val))
        })
      } else {
        ids.push(crudState.getDataId(row))
        dataStatus = crudState.getDataStatus(crudState.getDataId(row))
      }
      if (!callVmHook(crudState, CRUD.HOOK.beforeDelete, row)) {
        return
      }
      if (!delAll) {
        dataStatus.delete = CRUD.STATUS.PROCESSING
      }
      try {
        const resp = await crudState.crudMethod.del(ids);
        if (delAll) {
          crudState.delAllLoading = false
        } else dataStatus.delete = CRUD.STATUS.PREPARED
        // 处理删除结果
        if (resp?.status === 400) {
          const ret = callVmHook(crudState, CRUD.HOOK.afterDeleteError, row, resp)
          if (ret === false) {
            return
          }
          methods.notify('删除失败','error');
          return;
        } 
        methods.notify('删除成功', 'success');
        callVmHook(crudState, CRUD.HOOK.afterDelete, row)
        await methods.refresh();
      } catch (error) {
        methods.notify('删除失败', 'error');
      }
      if (delAll) {
        crudState.delAllLoading = false
      } else dataStatus.delete = CRUD.STATUS.PREPARED
    },

    // 获取查询参数
    getQueryParams() {
      // 清除参数无值的情况
      Object.keys(crudState.query).length !== 0 && Object.keys(crudState.query).forEach(item => {
        if (crudState.query[item] === null || crudState.query[item] === '') crudState.query[item] = undefined
      })
      Object.keys(crudState.params).length !== 0 && Object.keys(crudState.params).forEach(item => {
        if (crudState.params[item] === null || crudState.params[item] === '') crudState.params[item] = undefined
      })
      
      // 构建基础参数对象
      const baseParams = {
        page: crudState.page.page - 1,
        size: crudState.page.size,
        sort: crudState.sort
      }
      
      // 处理 query 和 params 中的函数类型字段
      const processedQuery = {}
      const processedParams = {}
      
      // 处理 query 对象
      Object.keys(crudState.query).forEach(key => {
        const value = crudState.query[key]
        if (typeof value === 'function') {
          // 如果值是函数，则计算其返回值
          processedQuery[key] = value()
        } else {
          processedQuery[key] = value
        }
      })
      
      // 处理 params 对象
      Object.keys(crudState.params).forEach(key => {
        const value = crudState.params[key]
        if (typeof value === 'function') {
          // 如果值是函数，则计算其返回值
          processedParams[key] = value()
        } else {
          processedParams[key] = value
        }
      })
      
      // 合并所有参数并返回
      return {
        ...baseParams,
        ...processedQuery,
        ...processedParams
      };
    },
    //获取数据ID
    getDataId(data) {
      return data[crudState.idField]
    },
    /**
     * 获取数据状态
     * @param {Number | String} id 数据项id
     */
    getDataStatus(id) {
      return crudState.dataStatus[id]
    },
    // 重置表单
    resetForm(data) {
      const defaultForm = typeof crudState.defaultForm === 'function' 
        ? crudState.defaultForm() 
        : JSON.parse(JSON.stringify(crudState.defaultForm));
      
      const formData = data ? JSON.parse(JSON.stringify(data)) : defaultForm;
      
      // 保留原有响应式属性
      Object.keys(crudState.form).forEach(key => {
        if (!(key in formData)) {
          delete crudState.form[key];
        }
      });
      
      // 更新表单数据
      Object.keys(formData).forEach(key => {
        crudState.form[key] = formData[key];
      });
      
      // 重置表单验证
      nextTick(() => {
        if (crudState.formRef) {
          crudState.formRef.clearValidate();
        }
      });
    },

    // 重置数据状态
    resetDataStatus() {
      if (!crudState.data){
        return
      }
      crudState.data.forEach((item) => {
        crudState.dataStatus[item[crudState.idField]] = {
          delete: 0,
          edit: 0
        };
      });
    },

    // 页码改变
    async pageChangeHandler(page) {
      crudState.page.page = page;
      await methods.refresh();
    },

    // 每页条数改变
    async sizeChangeHandler(size) {
      crudState.page.size = size;
      crudState.page.page = 1;
      await methods.refresh();
    },

    // 新增选择处理逻辑
    handleSelectionChange(selection) {
      crudState.selections = selection;
    },
    // 选择改变
    selectionChangeHandler(val) {
      crudState.selections = val
    },
    // 新增树形表格处理方法
    handleTreeSelect(selection, row) {
      if (selection.some(item => methods.getDataId(item) === methods.getDataId(row))) {
        // 处理子节点选中
        const walkTree = (nodes) => {
          nodes.forEach(node => {
            crudState.getTableRef().toggleRowSelection(node, true);
            if (node.children) walkTree(node.children);
          });
        };
        walkTree(row.children || []);
      } else {
        // 处理子节点取消选中
        const walkTree = (nodes) => {
          nodes.forEach(node => {
            crudState.getTableRef().toggleRowSelection(node, false);
            if (node.children) walkTree(node.children);
          });
        };
        walkTree(row.children || []);
      }
    }
  };

  // 监听分页变化
  watch(
    () => crudState.page.page,
    () => methods.refresh()
  );

  Object.assign(crudState, methods)
  return {
    CRUD: CRUD,
    crud: crudState,
    form: crudState.form,
    query: crudState.query,
    params: crudState.params,
    rules: crudState.rules,
    dialogVisible: crudState.dialogVisible,
    crudMethods: methods
  };
}
