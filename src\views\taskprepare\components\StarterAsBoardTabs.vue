<template>
  <div class="main-form-container">
    <el-tabs
    v-model="activeName"
    type="border-card"
    tab-position="left"
    class="demo-tabs"
    addable
    @tab-add="addNewBoard"
  >
    <template #add-icon>
      <el-button type="primary" plain size="small">
        <el-icon><Plus /></el-icon>
        新增板卡
      </el-button>
    </template>
    <el-tab-pane v-for = "item in boards" :key="item.tag" :label="item.tag" :name="item.tag" lazy>
      <template #label>
        <div class="label-container">
          <SampleBoard :board="item" />
          <div>{{item.tag}}</div>
        </div>
      </template>
      <BoardSampleEditor :board="item" @onTaskSubmitted="onTaskSubmitted" @onBoardUpdated="onBoardUpdated"/>
    </el-tab-pane>

  </el-tabs>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, version, defineModel, defineEmits } from 'vue'
import { useStore } from 'vuex'
import { debounce, createUniqueString } from '@/utils'
import { useCrud } from '@/hooks/useCrud'
import crudTaskPrepare from '@/api/taskPrepare'
import { queryProceduresForSelection } from '@/api/procedure'
import { querySamplesForSelection } from '@/api/sample'
import { ElMessage, ElNotification as Notification } from 'element-plus'
import { Delete, VideoPlay,Plus } from '@element-plus/icons-vue'

import pagination from '@/components/crud/Pagination'

import SampleBoard from '@/views/samplemgr/components/SampleBoard.vue'
import BoardSampleEditor from './BoardSampleEditor.vue'

const emit = defineEmits(['onTaskSubmitted', 'onBoardUpdated'])

// CRUD配置
const { crud, CRUD } = useCrud({
  title: '任务启动准备',
  url: 'api/taskPrepare/queryTaskPrepareForConveyor',
  idField: 'id',
  sort: 'id,desc',
  queryOnPresenterCreated: true,
  crudMethod: { ...crudTaskPrepare }
})

// 组件引用
const store = useStore()

const activeName = ref('first')
const tableTaskPrepare = ref(null)
const formRef = ref(null)

// 响应式状态
const sampleEditorVisible = ref(false)
const procedureOptions = ref([{ label: '新建流程', value: 0 }])
const sampleOptions = ref([])
const message = ref(' ')
const messageClass = ref('')
const inputDisabled = ref(false)
const batchSubmitLoading = ref(false)
const taskPrepareView = ref({})
const currentDeletingRow = ref({})
const taskPrepareLoading = ref(false)

const boards = ref([])

const sampleEditorSelected = ref('printer');


// 计算属性
const backgroundStyle = computed(() => ({
  backgroundImage: `url(${require('@/assets/icons/svg/status-btn-bg.svg')})`
}))

const buttonIconStyle = computed(() => ({
  backgroundImage: `url(${store.getters.imageAccessUrl}%E5%90%AF%E5%8A%A8%E5%88%86%E6%9E%90-20240620031632806.svg)`
}))

const initBoards = async () => {
  boards.value = await crudTaskPrepare.queryTagsToEdit()
  if (boards.value.length === 0) {
    addNewBoard()
  }
  if (boards.value.length > 0) {
    activeName.value = boards.value[0].tag
  }
}

const addNewBoard = (tab, event) => {
  const newBoard = { tag: createUniqueString(), rows:2, columns:4,width: 84,
  height: 40, positions:[] }
  boards.value.push(newBoard)
  activeName.value = newBoard.tag
}

const onTaskSubmitted = (taskPrepare) => {
  emit('onTaskSubmitted', taskPrepare)
}

const onBoardUpdated = (board) => {
  debugger
  const curBoard = boards.value.find(b => board.oldTag === b.tag)
  if (curBoard) {
    curBoard.tag = board.newTag
    curBoard.positions = []
    for (const tp of board.taskPrepareDtos) {
      curBoard.positions.push(tp.posIndex)
    }
    activeName.value = curBoard.tag
  }
  
}

// 生命周期
onMounted(async () => {
  initBoards()
})

// 方法
const isRowSelectable = (row) => !row.inQueue

const handleAnalyseStart = () => {
   doSubmitFactory()
}

const doSubmitFactory = async () => {
  if (crud.selections.length === 0) {
    ElMessage.error('请选择待进样样本标签')
    return
  }
  
  batchSubmitLoading.value = true
  inputDisabled.value = true
  taskPrepareView.value.taskPrepareDtos = crud.selections
  
  try {
    //await crudTaskPrepare.submitEntryConveyorAcceptingStep(taskPrepareView.value)
    await crudTaskPrepare.submitBatchTasks(taskPrepareView.value)
    
    setTimeout(getCurrentTaskPrepareView, 100)
  } finally {
    batchSubmitLoading.value = false
  }
}

const openSampleEditor = () => {
  debugger
  sampleEditorVisible.value = true
}

const onSampleEditSuccess = (form) => {
  sampleEditorVisible.value = false;
  crud.refresh();
};

const addTaskPrepare = (taskprepares) => {
  const defaultProc = procedureOptions.value[0] || { id: null, name: null }
  const newLine = { procedure: defaultProc, rfTag: null, status: 'DRAFT', selected: true }
  crud.data.push(newLine)
  tableTaskPrepare.value.setCurrentRow(newLine)
}

const handleCurrentChange = (currentRow, oldCurrentRow) => {
  // currentRow.selected = true
  // oldCurrentRow.selected = false
}

const deleteTaskPrepare = (curTaskPrepare) => {
  crud.toDelete(curTaskPrepare)
}

const cancelAnalyse = () => {
  message.value = ''
  inputDisabled.value = false
  taskPrepareView.value = {}
  crudTaskPrepare.cancelCurrentTaskPrepareView({ id: taskPrepareView.value.id }).then(res => {
    ElMessage.success('撤销任务成功')
    console.log(new Date() + ': TaskPrepare was cancelled done.')
    getCurrentTaskPrepareView()
  }).catch((error) => {
    console.log('doSubmitTrial error=' + error)
    ElMessage.error('撤销任务失败')
  })
}

const onSampleSelect = (keyword) => {
  sampleOptions.value = []
  sampleLoading.value = true
  debounce(() => {
    querySamplesForSelection({ name: keyword }).then(res => {
      sampleLoading.value = false
      sampleOptions.value = res
    })
  }, 200)()
}

// 获取任务准备状态标签类型
const getTaskPrepareStatusTagType = (taskPrepare) => {
  switch (taskPrepare.status) {
    case 'PENDING':
      return 'warning';
    case 'COMPLETED':
      return 'success';
    default:
      return 'info';
  }
};
const getCurrentTaskPrepareView = async (param) => {
  try {
    taskPrepareView.value = await crudTaskPrepare.getCurrentTaskPrepareView({ mode: 'FACTORY' })
    updateTaskPrepare()
    let finishedCount = 0
    for (const tp of taskPrepareView.value.taskPrepareDtos) {
      if (tp.status === 'PUSHED' || tp.status === 'SUCCESS' || tp.status === 'FAILED') {
        finishedCount++
      }
    }

    if (finishedCount !== crud.finishedCount) {
      crud.finishedCount = finishedCount
      crud.refresh()
    }

    if (taskPrepareView.value.status === 'DRAFT') {
      message.value = ''
    } else {
      message.value = '进样完成进度：' + finishedCount + '/' + taskPrepareView.value.taskPrepareDtos.length
    }

    if (finishedCount === taskPrepareView.value.taskPrepareDtos.length) {
      batchSubmitLoading.value = false
      inputDisabled.value = false
    } else {
      inputDisabled.value = true
      setTimeout(() => {
        getCurrentTaskPrepareView(param)
      }, 500)
    }
  } catch (error) {
    console.log('getCurrentTaskPrepareView error=' + error)
    ElMessage.error('获取任务准备视图失败')
  }
}

const updateTaskPrepare = () => {
  if (taskPrepareView.value.taskPrepareDtos && taskPrepareView.value.taskPrepareDtos.length > 0) {
    for (const respTpDto of taskPrepareView.value.taskPrepareDtos) {
      respTpDto.inQueue = true
      for (var i in crud.data) {
        const row = crud.data[i]
        if (row.id === respTpDto.id && (row.status !== respTpDto.status || !row.inQueue)) {
          crud.data.splice(i, 1, respTpDto)
        }
      }
    }
  }
}

// BEGIN: 处理删除操作
const doCancel = (row) => {
  row.deletePopVisible = false
  //crud.cancelDelete(this.data)
}

const toDelete = (row) => {
  currentDeletingRow.value = row
  for (let i = 0; i < crud.data.length; i++) {
    if (row.id === crud.data[i].id) {
      crud.data[i] = { ...crud.data[i], deletePopVisible: true }
      break
    }
  }
}

const onPopoverShow = (row) => {
  currentDeletingRow.value = row
  setTimeout(() => {
    document.addEventListener('click', handleDocumentClick)
  }, 0)
}

const onPopoverHide = (row) => {
  currentDeletingRow.value = row
  document.removeEventListener('click', handleDocumentClick)
}

const handleDocumentClick = (event) => {
  // currentDeletingRow.value.deletePopVisible = false
}
// END: 处理删除操作

// 添加watch来监听对话框关闭
watch(() => sampleEditorVisible.value, (newVal) => {
  console.log('Vue version:', version)
  
  if (typeof defineModel === 'undefined') {
    console.warn('当前 Vue 版本不支持 defineModel，需要 Vue 3.4+ 版本')
  }

})

</script>

<style lang="scss" scoped>
.main-form-container {
  height: 400px;
  :deep(.el-tabs ){
    height:100%;
    margin-bottom: 0px;
  }
  :deep(.el-tabs__item) {
    height: 100%;
  }

  :deep(.el-tabs--left .el-tabs__header.is-left) {
    border-right: solid 1px #F0F0F0;
    border-bottom: none;
    background-color: #052850a1;
  }
  :deep(.is-active) {
    background-color: rgb(0 57 95 / 63%);
  }
}
.label-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.main-form-body {
  text-align: left;
}
.message-container {
  width: 100%;
  padding-left: 80px;
}
.main-form-footer {
  margin-top: 10px;
  padding-left: 80px;
  display: flex;
  .btn-container {
    display: flex;
  }
}
.span-message {
  height: 24px;
  font-size: 24px;
  font-weight: bold;
}
.span-message-FAILED {
  color: red;
}
.span-message-SUCCESS {
  color: green;
}
.span-message-RUNNING {
  color: #0580B4;
}

  .span-message-RUNNING {
    position: relative;
    padding: 10px 20px;
    font-size: 24px;
    color: #fff;
    text-align: center;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
  }

  /* 动态边框效果 */
  .span-message-RUNNING::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    padding: 2px; /* 边框宽度 */
    background: linear-gradient(90deg,
      #ff0000, #00ff00, #0000ff, #ff0000); /* 渐变色条 */
    background-size: 300% 100%;
    animation: flowing 3s linear infinite;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude; /* 仅保留边框部分 */
  }

  @keyframes flowing {
    from {
      background-position: 0% 50%;
    }
    to {
      background-position: 100% 50%;
    }
  }

.operation {
  width:18px;
  padding: 2px 2px;
  margin-left: 4px;
}
.flowing-border {
    position: relative;
    padding: 4px 4px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
  }

  /* 动态边框效果 */
  .flowing-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    padding: 2px; /* 边框宽度 */
    background: linear-gradient(90deg,
      #ff0000, #00ff00, #0000ff, #ff0000); /* 渐变色条 */
    background-size: 300% 100%;
    animation: flowing 3s linear infinite;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude; /* 仅保留边框部分 */
  }

  @keyframes flowing {
    from {
      background-position: 0% 50%;
    }
    to {
      background-position: 100% 50%;
    }
  }
</style>
