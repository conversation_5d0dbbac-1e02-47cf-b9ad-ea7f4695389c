// useDict.js
import { reactive, ref, onMounted } from 'vue';
import { get as getDictDetail } from '@/api/system/dictDetail';

/**
 * useDict 组合函数，用于获取和管理字典数据。
 * @param {Array} names - 字典名称数组
 * @returns {Object} 返回响应式字典对象和加载状态
 */
export function useDict(names) {
  const dict = reactive({
    dict: {},
    label: {},
    // 使用动态字典数据存储
    data: {}
  });

  const loading = ref(false); // 加载状态
  const error = ref(null); // 错误信息

  const loadDicts = async () => {
    if (!Array.isArray(names) || names.length === 0) {
      throw new Error('Invalid dict names');
    }

    loading.value = true;
    error.value = null;

    const promises = names.map(name => {
      if (!dict.dict[name]) dict.dict[name] = {};
      if (!dict.label[name]) dict.label[name] = {};
      if (!dict.data[name]) dict.data[name] = [];

      return getDictDetail(name)
        .then(data => {
          // 更新字典数组
          dict.data[name] = data.content || [];
          // 更新字典字典项和标签
          data.content.forEach(item => {
            dict.dict[name][item.value] = item;
            dict.label[name][item.value] = item.label;
          });
        })
        .catch(err => {
          console.error(`Failed to load dictionary ${name}:`, err);
          error.value = err; // 设置错误信息
        });
    });

    await Promise.all(promises);
    loading.value = false;
  };

  const getDictLabel = (name, value) => {
    return dict.label[name] ? dict.label[name][value] : '';
  };

  onMounted(() => {
    loadDicts();
  });

  return {
    dict,
    loading,
    error,
    getDictLabel,
    reload: loadDicts // 提供重载函数
  };
}
