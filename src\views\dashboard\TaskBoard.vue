<template>
  <div class="task-board-container">
    <div class="task-stat-inner-container inner-container">
      <div class="task-board-title">
        <div class="title">
          <el-icon><DataLine /></el-icon>
          <span>任务统计</span>
        </div>
      </div>
      <TaskStatViewer />
    </div>
    <div class="task-schedule-inner-container inner-container">
      <div class="task-board-title">
        <div class="title">
          <el-icon><Calendar /></el-icon>
          <span>任务排程</span>
        </div>
        <el-popover
          placement="left"
          title="任务排程图"
          width="600"
          trigger="click"
          @show="onShowScheduleChartPopover"
          @hide="onHideScheduleChartPopover"
        >
          <template #default>
            <ScheduleChart v-if="scheduleChartPoperVisiable" :show-mode="'detail'" />
          </template>
          <template #reference>
            <el-button :icon="Setting" class="schedule-chart-btn" />
          </template>
        </el-popover>
      </div>
      <div class="task-schedule-chart">
        <ScheduleChart v-if="!scheduleChartPoperVisiable" />
      </div>
    </div>
    <div class="inner-container-log inner-container">
      <div class="task-board-title">
        <div class="title">
        <el-icon><ChatDotSquare /></el-icon>
        <span>执行日志</span>
        </div>
      </div>
      <TaskLogViewer />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Setting, Calendar, DataLine, ChatDotSquare } from '@element-plus/icons-vue'
import TaskStatViewer from '../components/TaskStatViewer.vue'
import ScheduleChart from '@/views/components/ScheduleChart2.vue'
import TaskLogViewer from '@/views/components/TaskLogViewer.vue'

// 响应式状态
const chart = ref(null)
const currerntProcedureName = ref('水分检测(Wet)')
const scheduleChartPoperVisiable = ref(false)

// 方法
const onShowScheduleChartPopover = () => {
  scheduleChartPoperVisiable.value = true
}

const onHideScheduleChartPopover = () => {
  scheduleChartPoperVisiable.value = false
}
</script>

<style lang="scss" scoped>
.flow {
  width: 100%;
  height: 100vh;
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
}

.task-board-container {
  width: 390px;
  height: 100%;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  
  .task-stat-inner-container {
    height: 140px;
  }
  
  .task-schedule-inner-container {
    height: 420px;
  }
  
  .inner-container-log {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
}

.inner-container {
  border: 1px solid #238DBD;
  box-sizing: border-box;
  background-color: #158CBE40;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  padding: 10px;
  color: white;
  margin: 0 0 10px 10px;
  
  .task-board-title {
    padding: 4px 10px;

    .title {
      font-size: 13px;
      font-weight: bold;
      align-items: center;
      justify-content: center;
      display: flex;
    }
  }
}

.task-schedule-chart {
  position: relative;
  top: -30px;
}

.task-board-title {
  padding: 4px 10px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .el-icon {
    margin-right: 4px;
  }
}

.inner-container-log {
  :deep(.log-entry) {
    border-bottom: 0 solid #4c6c81;
  }
  
  :deep(.log-timestamp) {
    color: #888;
  }
}

.log-container-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  i {
    padding-left: 0;
    font-size: smaller;
    font-weight: 600;
    color: #606266;
    
    &::before {
      margin-right: 4px;
    }
  }
}

.schedule-chart-btn {
  width: 18px;
  height: 18px;
  padding: 2px;
  margin-left: 4px;
  position: relative;
  z-index: 1996;
}
</style>
