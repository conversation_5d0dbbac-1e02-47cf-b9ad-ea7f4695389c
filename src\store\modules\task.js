import crudTask from '@/api/task'
import crudDeviceInstanceCmd from '@/api/deviceInstanceCmd'

const task = {
  state: {
    task: {},
    ctrlStatus: { doorStatus:'UNKNOWN',needToConfirmIndicator: false,isTaskStarted:false}
  },

  mutations: {
    SET_TASK: (state, task) => {
      state.task = task
    },
    SET_CTRL_STATUS: (state, ctrlStatus) => {
      state.ctrlStatus = ctrlStatus
    }
  },

  actions: {

    // 获取当前运动信息
    getCurrentTaskGlobalInfo({ commit }) {
      return new Promise((resolve, reject) => {
        crudTask.getCurrentTaskGlobalInfo(task.state.task).then(res => {
          commit('SET_TASK', res)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    setStarted({ commit }, status) {
      task.state.task.started = status
      task.state.ctrlStatus.isTaskStarted = status
      commit('SET_CTRL_STATUS', {...task.state.ctrlStatus})
      return new Promise((resolve, reject) => {
        crudTask.setStarted(task.state.task).then(res => {
          commit('SET_TASK', res)
          commit('SET_CTRL_STATUS', task.state.ctrlStatus)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    setDoorStatus({ commit }, status) {
      if (status === 'LOCK'){
        task.state.ctrlStatus.doorStatus = 'LOCKED'
      }else if (status === 'UNLOCK'){
        task.state.ctrlStatus.doorStatus = 'UNLOCKED'
      }
      commit('SET_CTRL_STATUS', {...task.state.ctrlStatus})
      const data = {bindControlCode: status }
      return new Promise((resolve, reject) => {
        crudDeviceInstanceCmd.executeCommandByControlCode(data).then(res => {
          commit('SET_CTRL_STATUS', task.state.ctrlStatus)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    setCtrlStatus({ commit }, ctrlStatus) {
      commit('SET_CTRL_STATUS', ctrlStatus)
    },
    setCtrlStatusToDefault({ commit }) {
      commit('SET_CTRL_STATUS', { doorStatus:'UNKNOWN',needToConfirmIndicator: false,isTaskStarted:false})
    }
  }
}

export default task
