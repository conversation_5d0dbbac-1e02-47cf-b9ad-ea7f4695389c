<template>
  <!-- 展开下游节点 -->
  <div v-if="nodeType!='COMMAND'" class="plus-dag">
    <el-icon :class="['plus-action']" v-if="plusActionSelected" @click="toggleSelect"><Remove/></el-icon>
    <el-icon :class="['plus-action']" v-if="!plusActionSelected" @click="toggleSelect"><CirclePlus/></el-icon>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, inject } from 'vue'
import { Graph, Node, Path, StringExt } from '@antv/x6'
import { CirclePlus, Remove } from '@element-plus/icons-vue'

import { ElIcon } from 'element-plus'

// Props 定义
defineProps({
  type: {
    type: String,
    required: false
  },
  name: {
    type: String,
    required: false
  }
})

// 注入
const getNode = inject('getNode')

// 响应式状态
const nodeName = ref('')
const nodeType = ref('')

const plusActionSelected = ref(false)

const toggleSelect = () => {
  const node = getNode()
  const { graph } = node.model || {}
  showNodesRecursivelly(graph, node.id, plusActionSelected.value)
  plusActionSelected.value = !plusActionSelected.value
}

const showNodesRecursivelly = (graph, nodeId, bShow) => {
  const edges = graph.getEdges()
  edges.forEach(edge => {
    if (edge.source.cell === nodeId) {
      bShow ? edge.show() : edge.hide()
      const targetNode = graph.getCellById(edge.target.cell)
      bShow ? targetNode.show() : targetNode.hide()
      showNodesRecursivelly(graph, edge.target.cell, bShow)
    }
  })
}



// 生命周期钩子
onMounted(() => {
  const node = getNode()
  
  nodeName.value = node.getData().name
  nodeType.value = node.getData().type
  if (node.model?.graph) {
    
  }
  
})
</script>

<style  rel="stylesheet/scss" lang="scss" scoped>

  .plus-dag {
    visibility: hidden;
    position: relative;
    margin-left: 4px;
    height: 32px;
  }

  .plus-action {
    position: absolute;
    top: calc(50% - 8px);
    left: 0;
    width: 16px;
    height: 16px;
    background: no-repeat center center / 100% 100%;
    cursor: pointer;
    color:#A2B1C3;
  }
  .plus-action:hover {
    color:#3471f9;
  }

  .plus-action:active,
  .plus-action-selected {
    background-image: url('https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*k9cnSaSmlw4AAAAAAAAAAAAADtOHAQ/original');
  }
  .plus-dropdown {
    position:absolute;
    z-index: 9998;
    padding: 2px 0;
    width: 105px;
    background-color: #fff;
    box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 5%), 0 6px 16px 0 rgba(0, 0, 0, 8%),
      0 3px 6px -4px rgba(0, 0, 0, 12%);
    border-radius: 2px;
  }
  .plus-dropdown-hide {
    visibility: hidden;
  }
  .plus-dropdown-visible {
    visibility: visible;
  }

  .x6-node-selected .main-area {
    border-color: #3471f9;
  }

  .x6-node-selected .plus-dag {
    visibility: visible;
  }

</style>
