<template>
  <div class="field-variable">
    <div class="field-search">
      <el-input
        v-model="searchVariable"
        placeholder="搜索变量"
        prefix-icon="el-icon-search"
        clearable
      />
    </div>
    <div
      v-for="item in filterFieldList"
      :key="item.name"
      class="field-item"
      @click="$emit('fieldSelect', item)"
    >
      <span class="field-text-wrapper">
      <span>{{ item.name }}</span>
      <span :field-type="item.type" class="text-tag">
        {{ item.type }}
      </span>
    </span>
      <span v-if="item.tip" class="tip">
            {{ item.tip }}
          </span>
    </div>
  </div>
</template>

<script>

export default {
  name: 'FieldVariable',
  components: {},
  props: {
    fieldList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    const type = {
      string: '字符串',
      number: '数字',
      array: '数组'
    }
    return {
      type,
      // 搜索值
      searchVariable: '',
      // 过滤后的变量
      filterFieldList: []
    }
  },
  computed: {},
  watch: {
    searchVariable(val, old) {
      this.filterFieldList = this.fieldList.filter(({ name }) => name.includes(val))
    },
    fieldList(val, old){
      this.filterFieldList = this.fieldList
    }
  },
  async created() {
  },
  mounted() {
    this.filterFieldList = this.fieldList
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
  .field-variable {
    max-width: 255px;
    padding: 10px;
    overflow: auto;
    height: 100%;
    border-right: 1px solid #d7d9dc;
    .field-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      cursor: pointer;
      padding: 8px;
      user-select: none;
      &:hover {
        background-color: #f0f1f4;
      }
      .field-text-wrapper{
        display:flex;
        flex-direction: row;
        justify-content: space-between;
        align-items:baseline;
        width: 100%;
        .text-tag {
          padding: 2px 6px;
          border-radius: 4px;
          color: #fff;
          white-space: nowrap;
          &[field-type='String'] {
            color: #409eff;
            background-color: #ecf5ff;
          }
          &[field-type='Number'] {
            color: #67c23a;
            background-color: #f0f9eb;
          }
          &[field-type='Array'] {
            color: #e6a23c;
            background-color: #fdf6ec;
          }
        }
      }
      .tip {
        width: 100%;
        font-size: 12px;
        color: #999;
        margin-top: 1px;
        line-height: 1.2;
        text-align: left;
      }
    }
  }
</style>
