<template>
  <div id="gantt-chart" style="width: 100%;" />
</template>

<script setup>
import * as echarts from 'echarts'
import { ref, reactive, onMounted, onBeforeUnmount, defineProps } from 'vue'
import crudTask from '@/api/task'

// 定义props
const props = defineProps({
  showMode: {
    default: 'summary', // summary/detail
    type: String
  }
})

// 响应式数据
const chart = ref(null)
const tasks = ref([])
const intervalId = ref(null)

const rawData = reactive({
  taskdefs: {
    data: []
  },
  taskItems: {
    data: []
  }
})

// 常量定义
const HEIGHT_RATIO = 0.6
const DIM_CATEGORY_INDEX = 0
const DIM_TIME_ARRIVAL = 7
const DIM_TIME_DEPARTURE = 8
const DATA_ZOOM_AUTO_MOVE_THROTTLE = 30
const DATA_ZOOM_X_INSIDE_INDEX = 1
const DATA_ZOOM_Y_INSIDE_INDEX = 3
const DATA_ZOOM_AUTO_MOVE_SPEED = 0.2
const DATA_ZOOM_AUTO_MOVE_DETECT_AREA_WIDTH = 30

// 变量定义
let myChart = null
let option = null
let _draggable = false
let _draggingEl = null
let _dropShadow = null
let _draggingCursorOffset = [0, 0]
let _draggingTimeLength = null
let _draggingRecord = null
let _dropRecord = null
let _cartesianXBounds = []
let _cartesianYBounds = []
//let rawData = null
let _autoDataZoomAnimator = null
let xStart = 0
let selectedRange = null

// 颜色定义
const taskColorDefs = ['tomato', 'teal', 'crimson', 'yellowgreen', 'blueviolet', 'forestgreen', 'steelblue', 'sandybrown', 'seagreen', 'slateblue']

// 获取数据最大结束时间
function getDataMaxEndTime() {
  if (!rawData || !rawData.taskItems) {
    return 0
  }
  const maxData = rawData.taskItems.data.reduce((acc, cur) => { return acc[DIM_TIME_DEPARTURE] > cur[DIM_TIME_DEPARTURE] ? acc : cur }, rawData.taskItems.data[0])
  if (!maxData) {
    return 0
  }
  return maxData[DIM_TIME_DEPARTURE]
}

// 创建图表选项
function makeOption() {
  // 展示12条
  let yEnd = 100
  if (rawData && rawData.taskItems.data.length > 0) {
    yEnd = 100 * (40 / rawData.taskItems.data.length)
    if (yEnd > 100) {
      yEnd = 100
    }
  }
  const yStart = 0

  // 展示1小时
  const duration = 60 * 60 * 1000

  if (xStart === 0) {
    if (rawData && rawData.taskItems.data.length > 0) {
      const minData = rawData.taskItems.data.reduce((acc, cur) => {
        return acc[DIM_TIME_ARRIVAL] < cur[DIM_TIME_ARRIVAL] ? acc : cur
      }, rawData.taskItems.data[0])
      const maxData = rawData.taskItems.data.reduce((acc, cur) => { return acc[DIM_TIME_DEPARTURE] > cur[DIM_TIME_DEPARTURE] ? acc : cur }, rawData.taskItems.data[0])
      const totalDuration = maxData[DIM_TIME_DEPARTURE] - minData[DIM_TIME_ARRIVAL]
      xStart = 100 - 100 * (duration / totalDuration)
      if (xStart < 0) {
        xStart = 0
      }
    }
  }

  const xEnd = 100

  return {
    tooltip: {
      // trigger: 'item' // 设置 tooltip 在轴上触发
    },
    animation: false,
    backgroundColor: 'transparent',
    toolbox: {
      show: false,
      left: 20,
      top: 0,
      itemSize: 20,
      feature: {
        myDrag: {
          show: false,
          title: 'Make bars\ndraggable',
          icon: 'path://M990.55 380.08 q11.69 0 19.88 8.19 q7.02 7.01 7.02 18.71 l0 480.65 q-1.17 43.27 -29.83 71.93 q-28.65 28.65 -71.92 29.82 l-813.96 0 q-43.27 -1.17 -72.5 -30.41 q-28.07 -28.07 -29.24 -71.34 l0 -785.89 q1.17 -43.27 29.24 -72.5 q29.23 -29.24 72.5 -29.24 l522.76 0 q11.7 0 18.71 7.02 q8.19 8.18 8.19 18.71 q0 11.69 -7.6 19.29 q-7.6 7.61 -19.3 7.61 l-518.08 0 q-22.22 1.17 -37.42 16.37 q-15.2 15.2 -15.2 37.42 l0 775.37 q0 23.39 15.2 38.59 q15.2 15.2 37.42 15.2 l804.6 0 q22.22 0 37.43 -15.2 q15.2 -15.2 16.37 -38.59 l0 -474.81 q0 -11.7 7.02 -18.71 q8.18 -8.19 18.71 -8.19 l0 0 ZM493.52 723.91 l-170.74 -170.75 l509.89 -509.89 q23.39 -23.39 56.13 -21.05 q32.75 1.17 59.65 26.9 l47.94 47.95 q25.73 26.89 27.49 59.64 q1.75 32.75 -21.64 57.3 l-508.72 509.9 l0 0 ZM870.09 80.69 l-56.13 56.14 l94.72 95.9 l56.14 -57.31 q8.19 -9.35 8.19 -21.05 q-1.17 -12.86 -10.53 -22.22 l-47.95 -49.12 q-10.52 -9.35 -23.39 -9.35 q-11.69 -1.17 -21.05 7.01 l0 0 ZM867.75 272.49 l-93.56 -95.9 l-380.08 380.08 l94.73 94.73 l378.91 -378.91 l0 0 ZM322.78 553.16 l38.59 39.77 l-33.92 125.13 l125.14 -33.92 l38.59 38.6 l-191.79 52.62 q-5.85 1.17 -12.28 0 q-6.44 -1.17 -11.11 -5.84 q-4.68 -4.68 -5.85 -11.7 q-2.34 -5.85 0 -11.69 l52.63 -192.97 l0 0 Z',
          onclick: onDragSwitchClick
        }
      }
    },
    title: {
      show: false,
      text: '任务排程图'
    },
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'weakFilter',
        height: 10,
        bottom: 20,
        start: xStart,
        end: xEnd,
        handleIcon: 'M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z',
        handleSize: '80%',
        showDetail: false
      },
      {
        type: 'inside',
        id: 'insideX',
        xAxisIndex: 0,
        filterMode: 'weakFilter',
        start: xStart,
        end: xEnd,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true
      },
      {
        type: 'slider',
        yAxisIndex: 0,
        width: 10,
        right: 10,
        top: 50,
        bottom: 20,
        start: yStart,
        end: yEnd,
        handleIcon: 'M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z',
        handleSize: 0,
        showDetail: false
      },
      {
        type: 'inside',
        id: 'insideY',
        yAxisIndex: 0,
        start: yStart,
        end: yEnd,
        zoomOnMouseWheel: false,
        moveOnMouseMove: true,
        moveOnMouseWheel: true
      }
    ],
    grid: {
      show: true,
      top: 50,
      bottom: 20,
      left: 100,
      right: 20,
      borderWidth: 0
    },
    xAxis: {
      type: 'time',
      position: 'top',
      boundaryGap: ['10%', '10%'],
      splitLine: {
        lineStyle: {
          color: ['#E9EDFF']
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#929ABA'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#929ABA'
        }
      },
      axisLabel: {
        color: '#929ABA',
        inside: false,
        align: 'center',
        hideOverlap: true
      }
    },
    yAxis: {
      type: 'category',
      axisTick: { show: true },
      splitLine: { 
        show: true,
        interval: (index, value) => {
          const data = rawData.taskdefs.data[index]
          if (!data) {
            return false
          }
          if (data[3] === 'TASK') {
            return true
          } else {
            return false
          }
        }
      },
      axisLine: { 
        show: true,
        lineStyle: {
          color: '#929ABA'
        }
      },
      axisLabel: { show: false },
      min: 0,
      max: rawData.taskdefs.data.length
    },
    brush: {
      toolbox: ['lineX', 'clear'], // 启用 x 轴上的 brush 选择和清除按钮
      xAxisIndex: 'all', // 应用于所有 x 轴
      brushMode: 'single', // 单选模式
      throttleType: 'debounce',
      throttleDelay: 300,
      inBrush: {
        color: '#ff0000',
        colorAlpha: 0.1
      },
      outOfBrush: {
        color: '#ff0000',
        colorAlpha: 0.8
      }
    },
    series: [
      {
        id: 'flightData',
        type: 'custom',
        renderItem: renderGanttItem,
        dimensions: rawData.taskItems.dimensions,
        encode: {
          x: [DIM_TIME_ARRIVAL, DIM_TIME_DEPARTURE],
          y: DIM_CATEGORY_INDEX,
          tooltip: [DIM_CATEGORY_INDEX, 4, 5, DIM_TIME_ARRIVAL, DIM_TIME_DEPARTURE]
        },
        z: 10001,
        data: rawData.taskItems.data
      },
      {
        type: 'custom',
        renderItem: renderAxisLabelItem,
        dimensions: rawData.taskdefs.dimensions,
        encode: {
          x: -1,
          y: 'index',
          tooltip: [0, 1, 2, 3]
        },
        data: rawData.taskdefs.data
      }
    ]
  }
}

// 渲染甘特图项目
function renderGanttItem(params, api) {
  const categoryIndex = api.value(DIM_CATEGORY_INDEX)
  const timeArrival = api.coord([api.value(DIM_TIME_ARRIVAL), categoryIndex])
  const timeDeparture = api.coord([api.value(DIM_TIME_DEPARTURE), categoryIndex])
  const coordSys = params.coordSys
  _cartesianXBounds[0] = coordSys.x
  _cartesianXBounds[1] = coordSys.x + coordSys.width
  _cartesianYBounds[0] = coordSys.y
  _cartesianYBounds[1] = coordSys.y + coordSys.height
  const barLength = timeDeparture[0] - timeArrival[0]
  // 获取y轴上长度为1对应的高度
  const barHeight = api.size([0, 1])[1] * HEIGHT_RATIO
  const x = timeArrival[0]
  const y = timeArrival[1] - barHeight
  const flightNumber = api.value(3) + ''
  const flightNumberWidth = echarts.format.getTextRect(flightNumber).width
  const text = barLength > flightNumberWidth + 40 && x + barLength >= 180 ? flightNumber : ''
  
  const rectNormal = clipRectByRect(params, {
    x: x,
    y: y,
    r: [4, 4, 4, 4],
    width: barLength,
    height: barHeight
  })
  
  if (!rectNormal) {
    return
  }
  
  const rectCustom = { ...rectNormal, d: createRoundedRectPath(rectNormal.x, rectNormal.y, rectNormal.width, rectNormal.height, 8) }
  const rectStyle = { ...api.style(), fill: getItemFillStyle(api.value(9)) }
  
  const rectVIP = clipRectByRect(params, {
    x: x,
    y: y,
    r: 4,
    width: barLength / 2,
    height: barHeight
  })
  
  const rectText = clipRectByRect(params, {
    x: x,
    y: y,
    r: 4,
    width: barLength,
    height: barHeight
  })

  return {
    type: 'group',
    children: [
      {
        type: 'path',
        ignore: !rectNormal,
        shape: rectCustom,
        style: rectStyle
      }
    ]
  }
}

// 渲染轴标签项
function renderAxisLabelItem(params, api) {
  const idx = api.value(0)
  const y = api.coord([0, idx])[1]
  
  if (y < params.coordSys.y + 5) {
    return
  }
  
  return {
    type: 'group',
    position: [1, y],
    children: [
      {
        type: 'text',
        style: {
          x: 75,
          y: 0,
          textVerticalAlign: 'bottom',
          textAlign: 'right',
          text: api.value(2),
          textFill: api.value(3) === 'TASK' ? '#aaa' : '#000',
          width: 72,
          overflow: 'truncate'
        }
      },
      {
        type: 'rect',
        shape: {
          x: 73,
          y: -17,
          width: 26, // 矩形宽度
          height: 17, // 矩形高度
          r: [8, 0, 0, 8] // 圆角半径
        },
        style: {
          fill: getItemFillStyle(api.value(5)) // 背景色
        }
      },
      {
        type: 'text',
        style: {
          x: 95,
          y: 0,
          textVerticalAlign: 'bottom',
          textAlign: 'right',
          text: api.value(4),
          textFill: '#fff',
          width: 70,
          overflow: 'truncate'
        }
      }
    ]
  }
}

// 通过矩形裁剪矩形
function clipRectByRect(params, rect) {
  return echarts.graphic.clipRectByRect(rect, {
    x: params.coordSys.x,
    y: params.coordSys.y,
    r: [4, 4, 4, 4],
    width: params.coordSys.width,
    height: params.coordSys.height
  })
}

// 启用拖拽开关点击
function onDragSwitchClick(model, api, type) {
  _draggable = !_draggable
  myChart.setOption({
    dataZoom: [
      {
        id: 'insideX',
        disabled: _draggable
      },
      {
        id: 'insideY',
        disabled: _draggable
      }
    ]
  })
  this.model.setIconStatus(type, _draggable ? 'emphasis' : 'normal')
}

// 初始化拖拽功能
function initDrag() {
  _autoDataZoomAnimator = makeAnimator(dispatchDataZoom)
  
  myChart.on('mousedown', function(param) {
    if (!_draggable || !param || param.seriesIndex == null) {
      return
    }
    // 拖拽开始
    _draggingRecord = {
      dataIndex: param.dataIndex,
      categoryIndex: param.value[DIM_CATEGORY_INDEX],
      timeArrival: param.value[DIM_TIME_ARRIVAL],
      timeDeparture: param.value[DIM_TIME_DEPARTURE]
    }
    const style = {
      lineWidth: 2,
      fill: 'rgba(255,0,0,0.1)',
      stroke: 'rgba(255,0,0,0.8)',
      lineDash: [6, 3]
    }
    _draggingEl = addOrUpdateBar(_draggingEl, _draggingRecord, style, 100)
    _draggingCursorOffset = [
      _draggingEl.position[0] - param.event.offsetX,
      _draggingEl.position[1] - param.event.offsetY
    ]
    _draggingTimeLength = _draggingRecord.timeDeparture - _draggingRecord.timeArrival
  })
  
  myChart.getZr().on('mousemove', function(event) {
    if (!_draggingEl) {
      return
    }
    const cursorX = event.offsetX
    const cursorY = event.offsetY
    // 移动拖拽元素
    _draggingEl.attr('position', [
      _draggingCursorOffset[0] + cursorX,
      _draggingCursorOffset[1] + cursorY
    ])
    prepareDrop()
    autoDataZoomWhenDraggingOutside(cursorX, cursorY)
  })
  
  myChart.getZr().on('mouseup', function() {
    // 放置
    if (_draggingEl && _dropRecord) {
      updateRawData() &&
        myChart.setOption({
          series: {
            id: 'flightData',
            data: rawData.taskItems.data
          }
        })
    }
    dragRelease()
  })
  
  myChart.getZr().on('globalout', dragRelease)
  
  function dragRelease() {
    _autoDataZoomAnimator.stop()
    if (_draggingEl) {
      myChart.getZr().remove(_draggingEl)
      _draggingEl = null
    }
    if (_dropShadow) {
      myChart.getZr().remove(_dropShadow)
      _dropShadow = null
    }
    _dropRecord = _draggingRecord = null
  }
  
  function addOrUpdateBar(el, itemData, style, z) {
    const pointArrival = myChart.convertToPixel('grid', [
      itemData.timeArrival,
      itemData.categoryIndex
    ])
    const pointDeparture = myChart.convertToPixel('grid', [
      itemData.timeDeparture,
      itemData.categoryIndex
    ])
    const barLength = pointDeparture[0] - pointArrival[0]
    const barHeight = Math.abs(
      myChart.convertToPixel('grid', [0, 0])[1] -
      myChart.convertToPixel('grid', [0, 1])[1]
    ) * HEIGHT_RATIO
    
    if (!el) {
      el = new echarts.graphic.Rect({
        shape: { x: 0, y: 0, width: 0, height: 0 },
        style: style,
        z: z
      })
      myChart.getZr().add(el)
    }
    
    el.attr({
      shape: { x: 0, y: 0, width: barLength, height: barHeight },
      position: [pointArrival[0], pointArrival[1] - barHeight]
    })
    
    return el
  }
  
  function prepareDrop() {
    // 检查可放置位置
    const xPixel = _draggingEl.shape.x + _draggingEl.position[0]
    const yPixel = _draggingEl.shape.y + _draggingEl.position[1]
    const cursorData = myChart.convertFromPixel('grid', [xPixel, yPixel])
    
    if (cursorData) {
      // 创建放置阴影和记录
      _dropRecord = {
        categoryIndex: Math.floor(cursorData[1]),
        timeArrival: cursorData[0],
        timeDeparture: cursorData[0] + _draggingTimeLength
      }
      const style = { fill: 'rgba(0,0,0,0.4)' }
      _dropShadow = addOrUpdateBar(_dropShadow, _dropRecord, style, 99)
    }
  }
  
  // 业务逻辑，更新原始数据
  function updateRawData() {
    const flightData = rawData.taskItems.data
    const movingItem = flightData[_draggingRecord.dataIndex]
    // 检查冲突
    for (let i = 0; i < flightData.length; i++) {
      const dataItem = flightData[i]
      if (
        dataItem !== movingItem &&
        _dropRecord.categoryIndex === dataItem[DIM_CATEGORY_INDEX] &&
        _dropRecord.timeArrival < dataItem[DIM_TIME_DEPARTURE] &&
        _dropRecord.timeDeparture > dataItem[DIM_TIME_ARRIVAL]
      ) {
        alert('Conflict! Find a free space to settle the bar!')
        return
      }
    }
    // 无冲突，更新数据
    movingItem[DIM_CATEGORY_INDEX] = _dropRecord.categoryIndex
    movingItem[DIM_TIME_ARRIVAL] = _dropRecord.timeArrival
    movingItem[DIM_TIME_DEPARTURE] = _dropRecord.timeDeparture
    return true
  }
  
  function autoDataZoomWhenDraggingOutside(cursorX, cursorY) {
    // 当光标在笛卡尔坐标系外并且正在拖拽时，自动移动dataZoom
    const cursorDistX = getCursorCartesianDist(cursorX, _cartesianXBounds)
    const cursorDistY = getCursorCartesianDist(cursorY, _cartesianYBounds)
    
    if (cursorDistX !== 0 || cursorDistY !== 0) {
      _autoDataZoomAnimator.start({
        cursorDistX: cursorDistX,
        cursorDistY: cursorDistY
      })
    } else {
      _autoDataZoomAnimator.stop()
    }
  }
  
  function dispatchDataZoom(params) {
    const option = myChart.getOption()
    const optionInsideX = option.dataZoom[DATA_ZOOM_X_INSIDE_INDEX]
    const optionInsideY = option.dataZoom[DATA_ZOOM_Y_INSIDE_INDEX]
    const batch = []
    
    prepareBatch(
      batch,
      'insideX',
      optionInsideX.start,
      optionInsideX.end,
      params.cursorDistX
    )
    
    prepareBatch(
      batch,
      'insideY',
      optionInsideY.start,
      optionInsideY.end,
      -params.cursorDistY
    )
    
    batch.length &&
      myChart.dispatchAction({
        type: 'dataZoom',
        batch: batch
      })
    
    function prepareBatch(batch, id, start, end, cursorDist) {
      if (cursorDist === 0) {
        return
      }
      const sign = cursorDist / Math.abs(cursorDist)
      const size = end - start
      const delta = DATA_ZOOM_AUTO_MOVE_SPEED * sign
      start += delta
      end += delta
      
      if (end > 100) {
        end = 100
        start = end - size
      }
      
      if (start < 0) {
        start = 0
        end = start + size
      }
      
      batch.push({
        dataZoomId: id,
        start: start,
        end: end
      })
    }
  }
  
  function getCursorCartesianDist(cursorXY, bounds) {
    const dist0 = cursorXY - (bounds[0] + DATA_ZOOM_AUTO_MOVE_DETECT_AREA_WIDTH)
    const dist1 = cursorXY - (bounds[1] - DATA_ZOOM_AUTO_MOVE_DETECT_AREA_WIDTH)
    
    return dist0 * dist1 <= 0
      ? 0 // 光标在笛卡尔坐标系内
      : dist0 < 0
        ? dist0 // 光标在笛卡尔坐标系左侧/顶部
        : dist1 // 光标在笛卡尔坐标系右侧/底部
  }
  
  function makeAnimator(callback) {
    let requestId
    let callbackParams
    // 使用节流防止频繁调用dispatchAction
    callback = echarts.throttle(callback, DATA_ZOOM_AUTO_MOVE_THROTTLE)
    
    function onFrame() {
      callback(callbackParams)
      requestId = requestAnimationFrame(onFrame)
    }
    
    return {
      start: function(params) {
        callbackParams = params
        if (requestId == null) {
          onFrame()
        }
      },
      stop: function() {
        if (requestId != null) {
          cancelAnimationFrame(requestId)
        }
        requestId = callbackParams = null
      }
    }
  }
}

// 初始化鼠标移动事件
function initMouseMove() {
  const chart = myChart
  // 监听mousemove事件，判断是否在选中的范围内
  chart.getZr().on('mousemove', function(event) {
    if (selectedRange) {
      const pointInGrid = chart.convertFromPixel({ seriesIndex: 0 }, [event.offsetX, event.offsetY])
      const [xValue] = pointInGrid

      // 判断鼠标当前的x轴值是否在选中的范围内
      if (xValue >= new Date(selectedRange[0]).getTime() && xValue <= new Date(selectedRange[1]).getTime()) {
        // 获取tooltip并显示在单个数据点上
        const dataIndex = chart.convertFromPixel({ seriesIndex: 0 }, [event.offsetX])[0]
        chart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: Math.round(dataIndex)
        })
      } else {
        // 如果不在选区范围内，隐藏tooltip
        chart.dispatchAction({
          type: 'hideTip'
        })
      }
    }
  })
}

/**
 * 创建一个圆角矩形的SVG path
 * @param {number} x - 矩形的起始x坐标
 * @param {number} y - 矩形的起始y坐标
 * @param {number} width - 矩形的宽度
 * @param {number} height - 矩形的高度
 * @param {number} radius - 圆角半径
 * @returns {string} SVG path 数据
 */
function createRoundedRectPath(x, y, width, height, radius) {
  // 限制圆角半径不能大于矩形的一半
  radius = Math.min(radius, width / 2, height / 2)

  return `
        M ${x + radius},${y} 
        H ${x + width - radius} 
        A ${radius},${radius} 0 0 1 ${x + width},${y + radius} 
        V ${y + height - radius} 
        A ${radius},${radius} 0 0 1 ${x + width - radius},${y + height} 
        H ${x + radius} 
        A ${radius},${radius} 0 0 1 ${x},${y + height - radius} 
        V ${y + radius} 
        A ${radius},${radius} 0 0 1 ${x + radius},${y} 
        Z
    `
}

// 获取项目填充样式
function getItemFillStyle(taskId) {
  return taskColorDefs[(Number(taskId) % taskColorDefs.length)]
}

// 初始化图表
function initChart() {
  chart.value = echarts.init(document.getElementById('gantt-chart'))
  myChart = chart.value

  if (option) {
    myChart.setOption(option)
  }
  
  // 监听渲染完成的'finished'事件
  myChart.on('finished', function() {
    // 在渲染完成后自动触发brush选区
    myChart.dispatchAction({
      type: 'brush',
      areas: [
        {
          brushType: 'lineX', // x轴上的选择
          coordRange: [new Date(), getDataMaxEndTime()], // 选择时间范围
          xAxisIndex: 0 // 应用于第一个x轴
        }
      ]
    })
  })
  
  // 监听brushSelected事件
  myChart.on('brushSelected', function(params) {
    const selected = params.batch[0].areas[0].coordRange // 获取选中的范围
    selectedRange = selected
  })
}

// 初始化数据
async function initData() {
  await crudTask.querySchedulerGanttDataOnline().then(res => {
    Object.assign(rawData,res)
    myChart.setOption((option = makeOption()), false)

   // initDrag()
   //initMouseMove()
/*
    if (props.showMode === 'summary') {
      setTimeout(() => {
        initData()
      }, 5000)
    }
      */
  })
}

const refreshData = () => {
  intervalId.value = setInterval(()=>{
    if (props.showMode === 'summary') {
      initData()
    }
  }, 5000) 
}

// 生命周期钩子
onMounted(() => {
  initChart()
  initData()
  refreshData()
})

onBeforeUnmount(() => {
  if (chart.value) {
    chart.value.dispose()
  }
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
  intervalId.value = null
})
</script>

<style scoped>
#gantt-chart {
  width: 100%;
  height: 400px;
}
</style>
