import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/sample',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/sample/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sample',
    method: 'put',
    data
  })
}

export function querySamplesForSelection(params) {
  return request({
    url: 'api/sample/querySamplesForSelection',
    method: 'get',
    params
  })
}

export function checkSampleName(params) {
  return request({
    url: 'api/sample/checkSampleName',
    method: 'get',
    params
  })
}

export function queryLatestSample(params) {
  return request({
    url: 'api/sample/queryLatestSample',
    method: 'get',
    params
  })
}

export function submitSampleTagScanning(data) {
  return request({
    url: 'api/sample/submitSampleTagScanning',
    method: 'post',
    data
  })
}

export default { add, edit, del, querySamplesForSelection, checkSampleName, 
  queryLatestSample, submitSampleTagScanning }
