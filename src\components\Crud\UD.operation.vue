<template>
  <div>
    <el-button
      v-permission="permission.edit"
      :loading="crud.status.cu === 2"
      :disabled="disabledEdit"
      size="small"
      type="primary"
      link
      @click="crud.toEdit(data)"
    >
      <el-icon><EditPen /></el-icon>
      编辑
    </el-button>
    <el-popconfirm
      
      :title="msg"
      width="180"
      @confirm="crud.doDelete(data)"
      @cancel="crud.cancelDelete(data)"
    >
      <template #reference>
        <el-button
          v-permission="permission.del"
          :disabled="disabledDle"
          :loading="crud.dataStatus[crud.getDataId(data)]?.delete === 2"
          size="small"
          type="primary"
          link
        >
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </template>
    </el-popconfirm>
  </div>
</template>

<script setup>
import { inject } from 'vue'
import { EditPen, Delete } from '@element-plus/icons-vue'

// Props 定义
const props = defineProps({
  crud: {
    type: Object,
    default: null
  },
  data: {
    type: Object,
    required: true
  },
  permission: {
    type: Object,
    required: true
  },
  disabledEdit: {
    type: Boolean,
    default: false
  },
  disabledDle: {
    type: Boolean,
    default: false
  },
  msg: {
    type: String,
    default: '确定删除本条数据吗？'
  }
})


// 获取 crud 对象：优先使用 props 传入的，否则从 inject 获取
const crud = props.crud || inject('crud')
if (!crud) {
  throw new Error('`crud` 未提供，请确保在父组件中通过 provide 注入')
}

</script>
