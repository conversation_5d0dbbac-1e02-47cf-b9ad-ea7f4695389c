<template>
  <div class="material-consumption-container">
    <LesScriptEditorDialog
      v-model="lesScriptEditorDialogVisible" 
      :editName="modelValue"
      :title="编辑脚本"   
      @success="onScriptSubmitted"   
    />

    <el-select ref="selectRef" v-model="modelValue" clearable size="small" placeholder="请选择">
      <el-option v-for="item in postExecutions" :key="item.name" :label="item.name" :value="item.name" >
        <span>{{ item.name }}</span><el-button :icon="Edit" class="operation" plain  @click="openScriptEditor(item.name)" />
      </el-option>
      <template #footer>
        <el-button text @click="openScriptEditor(null)">新建脚本</el-button>
      </template>
    </el-select>

  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, defineModel } from 'vue'
import {  Edit } from '@element-plus/icons-vue';
import crudFormula from '@/api/formula'
import LesScriptEditorDialog from '@/views/formula/LesScriptEditorDialog.vue'

// 定义v-model
const modelValue = defineModel({
  type: String,
  default: ''
})


const postExecutions = ref([
  { label: '无', value: null }
])

const lesScriptEditorDialogVisible = ref(false)

const selectRef = ref(null)

const loadPostExecutions = async () => {
  try {
    const res = await crudFormula.queryForSelection({ 
      type: 'CMD_POST_SCRIPT' 
    })
    postExecutions.value = res

  } catch (error) {
    console.error('加载物料列表失败:', error)
  }
}

const openScriptEditor = (name) => {
  // 打开脚本编辑器
  if (name) {
    modelValue.value = name
  } else {
    modelValue.value = null
    selectRef.value.blur()
  }
  lesScriptEditorDialogVisible.value = true
}

const onScriptSubmitted = (script) => {
  // 处理脚本提交事件
  loadPostExecutions()
  modelValue.value = script.name

  lesScriptEditorDialogVisible.value = false
}


// 生命周期钩子
onMounted(async () => {
  await loadPostExecutions()

})
</script>

<style lang="scss" scoped>
.material-consumption-container {
  display: flex;
  align-items: center;

  .material-select {
    min-width: 100px;
  }
  
  
  .unit-select {
    width: 80px;
  }
}

.operation {
  padding: 2px 2px;
  margin-left: 4px;
}

</style>