<template>
  <div class="config-container">
    <LesScriptEditorDialog
      v-model="lesScriptEditorDialogVisible" 
      :editName="lesScriptName"
      :title="编辑脚本"   
      @success="onScriptSubmitted"   
    />

    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="命令名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-divider content-position="left">参数设置<el-button :icon="RefreshLeft" class="btn-parameter-refresh" @click="onParameterRefresh"/></el-divider>
      <PositionSelect v-if="inputComponent ==='position-select'" :cur-node="curNode" />
      <RestfulReqEditor v-if="inputComponent ==='restful-req-map'" :cur-node="curNode" :form-parameters="form.parameters" />
      <LockSelect v-if="inputComponent ==='lock-select'" :cur-node="curNode"  v-model="form.parameter" />
      <div v-if="inputComponent === 'input' || inputComponent === 'position-input' || inputComponent === 'position-value-map'">
        <el-form-item v-for="item in form.parameters" :key="item.name" :label="item.name" prop="arg1">
          <el-input v-model="item.value" />
        </el-form-item>
      </div>
      <el-divider content-position="left">选项</el-divider>
      <el-form-item label="执行失败动作" prop="failedThen">
        <el-select v-model="form.failedThen" size="small" placeholder="请选择">
          <el-option v-for="item in dict.data.failed_then_enum" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="重试次数" prop="failedRetries">
        <el-input v-model="form.failedRetries" />
      </el-form-item>
      <el-form-item label="执行前点位状态检测" prop="positionCheckingStatus">
        <el-select v-model="form.positionCheckingStatus"
        clearable 
          multiple
          filterable
          allow-create
          default-first-option
          value-key="value" 
           size="small" placeholder="请选择">
           <template #label="{ label, value, type }">
              <span style="font-weight: bold">{{ label }}</span>
              <span> @{{ value.type }} </span>
          </template>
          <el-option v-for="item in positionCheckingStatusOptions" :key="item.value" :label="item.label" :value="item" >
            <span>{{ item.label }} @</span>
            <el-select v-model="item.type" size="small" @click.stop @mousedown.stop @mouseup.stop style="margin-right:10px;">
              <el-option v-for="item in positionNameOptions" :key="item.value" :label="item.label" :value="item.value" 
                @click.stop @mousedown.stop @mouseup.stop>
                {{ item.label }}
              </el-option>
            </el-select>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="执行前点位状态设置" prop="positionPreExecutionStatus">
        <el-select v-model="form.positionPreExecutionStatus" 
          clearable 
          multiple
          filterable
          allow-create
          default-first-option
          value-key="value" 
           size="small" placeholder="请选择">
           <template #label="{ label, value, type }">
              <span style="font-weight: bold">{{ label }}</span>
              <span> @{{ value.type }} </span>
            </template>
          <el-option v-for="item in positionPreExecutionStatusOptions" :key="item.value" :label="item.label" :value="item" >
            <span>{{ item.label }}</span>
            <el-select v-model="item.type" size="small" @click.stop @mousedown.stop @mouseup.stop style="margin-right:10px;">
              <el-option v-for="item in positionNameOptions" :key="item.value" :label="item.label" :value="item.value" 
                @click.stop @mousedown.stop @mouseup.stop>
                {{ item.label }}
              </el-option>
            </el-select>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="执行后脚本" prop="postExecution">
        <el-select v-model="form.postExecution" clearable size="small" placeholder="请选择">
          <el-option v-for="item in postExecutions" :key="item.name" :label="item.name" :value="item.name" >
            <span>{{ item.name }}</span><el-button :icon="Edit" class="operation" plain @click.stop @click="openScriptEditor(item.name)" />
          </el-option>
          <template #footer>
            <el-button text @click="openScriptEditor(null)">新建脚本</el-button>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="执行后延迟(ms)" prop="executedDelay">
        <el-input v-model="form.executedDelay" />
      </el-form-item>
      <el-form-item label="执行后点位状态设置" prop="positionExecutedStatus">
        <el-select v-model="form.positionExecutedStatus"
          clearable 
          multiple
          filterable
          allow-create
          default-first-option
          value-key="value" 
           size="small" placeholder="请选择">
           <template #label="{ label, value, type }">
              <span style="font-weight: bold">{{ label }}</span>
              <span> @{{ value.type }} </span>
            </template>
          <el-option v-for="item in positionExecutedStatusOptions" :key="item.value" :label="item.label" :value="item" >
            <span>{{ item.label }}</span>
            <el-select v-model="item.type" size="small" @click.stop @mousedown.stop @mouseup.stop style="margin-right:10px;">
              <el-option v-for="item in positionNameOptions" :key="item.value" :label="item.label" :value="item.value" 
                @click.stop @mousedown.stop @mouseup.stop>
                {{ item.label }}
              </el-option>
            </el-select>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="执行后扣减物料" prop="materialConsumption">
        <MaterialConsumptionInput v-model="form.materialConsumption" />
      </el-form-item>
      <el-form-item label="备注提示" prop="comment">
        <el-input v-model="form.comment" />
      </el-form-item>
      <el-form-item label="是否选项" prop="isOption">
        <el-switch v-model="form.isOption" active-value="Y" inactive-value="N" active-text="是" inactive-text="否"/>
      </el-form-item>
      <el-form-item label="选项编码" prop="optionCode">
        <el-input v-model="form.optionCode" />
      </el-form-item>
      <el-divider content-position="left">所属设备</el-divider>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, inject, watch, onMounted, computed } from 'vue'
import { Node } from '@antv/x6'
import PositionSelect from '@/views/components/x6/PositionSelect.vue'
import RestfulReqEditor from '@/views/components/x6/RestfulReqEditor.vue'
import LockSelect from './components/LockSelect.vue'
import { useDict } from '@/hooks/useDict'
import crudFormula from '@/api/formula'
import {  Edit, RefreshLeft } from '@element-plus/icons-vue';
import LesScriptEditorDialog from '@/views/formula/LesScriptEditorDialog.vue'
import { queryPositionNamesForSelection } from '@/api/position'
import MaterialConsumptionInput from '@/views/components/MaterialConsumptionInput.vue'

// Props 定义
const props = defineProps({
  curNode: {
    type: Object,
    default: () => ({})
  }
})

// 注入
const getDataStore = inject('getDataStore')

// 使用字典
const { dict } = useDict(['failed_then_enum', 'position_status'])

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  commandId: null,
  parameter: null,
  parameters: [],
  failedThen: '',
  failedRetries: 0,
  positionCheckingStatus: null,
  positionPreExecutionStatus: null,
  postExecution: null,
  executedDelay: null,
  positionExecutedStatus: [],
  materialConsumption: null,
  isOption: 'N',
  optionCode: null,
  comment: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

const lesScriptEditorDialogVisible = ref(false)
const lesScriptName = ref(null)

const formRef = ref(null)
const dataStore = reactive({})
const initing = ref(false)
const inputComponent = ref('input')
const commandsOptions = ref([{ label: '系统等待', value: 1 }])
const postExecutions = ref([
  { label: '无', value: null },
  { label: '执行脚本', value: 'SCRIPT' },
  { label: '执行动作', value: 'ACTION' }
])

const positionTypeOptions = ref([
  { label: 'DEFAULT', value: 'DEFAULT' },
  { label: 'BOARD', value: 'BOARD' }
])
const positionNameOptions = ref([])

// 方法定义
const init = () => {
  initing.value = true
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  for (const actionObj of dataStore.actions || []) {
    if (actionObj.dagNodeId === nodeId) {
      return actionObj
    }
    for (const cmdObj of actionObj.commands || []) {
      if (cmdObj.dagNodeId === nodeId) {
        return cmdObj
      }
    }
  }
  return null
}

const getConfigTemplate = (command) => {
  if (!command) return null
  let parameterTemplate = null
  if (command.commandType === 'PROXY') {
    parameterTemplate = command.proxyInstanceCmd?.command?.parameterTemplate
  }else{
    parameterTemplate = command.command?.parameterTemplate
  }

  if (!parameterTemplate) return null
  
  return JSON.parse(parameterTemplate)
}

const getInstConfig = (storeCmdData, configTemplate) => {
  let instConfig = storeCmdData.parameter
  if (!instConfig) {
    instConfig = storeCmdData.command?.parameter
  }
    if (instConfig && configTemplate.children) {
      instConfig = JSON.parse(instConfig)
    }
    return instConfig
}

const toArray = (str) => {
  
  if (str) {
    if (typeof str === 'string') {
      try {
        // 尝试解析 JSON 字符串为数组
        const ret = JSON.parse(str)
        if (Array.isArray(ret)) {
          return reformPosArrary(ret)
        } else {
          return [ret]
        }
      } catch (e) {
        // 如果解析失败，则将字符串作为数组的一个元素
        return [str]
      }
    } else if (Array.isArray(str)) {
      return reformPosArrary(str)
    } 
  }
  return []
}
const reformPosArrary = (arr) => {
// 如果已经是数组，处理每个元素
 if (arr.find(s => typeof s === 'object')) {
  return arr
 }
  return arr.map(item => {
  
  // 如果元素是对象，直接返回
  if (typeof item === 'object' && item !== null) {
    return {
      label: item.label,
      value: item.value,
      type: item.type || 'DEFAULT'
    }
  } 
  // 如果元素是字符串，优先按照@符号，再按冒号:分隔处理
  else if (typeof item === 'string') {
    const parts = item.split('@')
    if (parts.length <= 1 ) {
      parts = item.split(':')
    }
    const value = parts[0]
    const type = parts.length > 1 ? parts[1] : 'DEFAULT'
    
    // 从字典中查找对应的标签
    const posStatus = dict.data.position_status || []
    const statusItem = posStatus.find(s => s.value === value)
    const label = statusItem ? statusItem.label : value
    
    return {
      label: label,
      value: value,
      type: type
    }
  }
  // 其他情况，返回原值
  return item
})
}
// 修改计算属性实现
const positionCheckingStatusOptions = computed(() => {

  const posStatus = dict.data.position_status || []
  if (posStatus.length === 0) {
    return []
  }

  // 创建基础选项列表
  const options = posStatus.map(item => {
    // 查找是否已在选中列表中
    let type = 'DEFAULT'
    if (form.positionCheckingStatus && Array.isArray(form.positionCheckingStatus)) {
      const selected = form.positionCheckingStatus.find(s => s.value === item.value)
      if (selected) {
        type = selected.type || 'DEFAULT'
      }
    }

    return {
      label: item.label,
      value: item.value,
      type: type
    }
  })

  return options
})
// 修改计算属性实现
const positionPreExecutionStatusOptions = computed(() => {

const posStatus = dict.data.position_status || []
if (posStatus.length === 0) {
  return []
}

  // 创建基础选项列表
  const options = posStatus.map(item => {
    // 查找是否已在选中列表中
    let type = 'DEFAULT'
    if (form.positionPreExecutionStatus && Array.isArray(form.positionPreExecutionStatus)) {
      const selected = form.positionPreExecutionStatus.find(s => s.value === item.value)
      if (selected) {
        type = selected.type || 'DEFAULT'
      }
    }

    return {
      label: item.label,
      value: item.value,
      type: type
    }
  })

  return options
})
// 修改计算属性实现
const positionExecutedStatusOptions = computed(() => {

  const posStatus = dict.data.position_status || []
  if (posStatus.length === 0) {
    return []
  }

  // 创建基础选项列表
  const options = posStatus.map(item => {
    // 查找是否已在选中列表中
    let type = 'DEFAULT'
    if (form.positionExecutedStatus && Array.isArray(form.positionExecutedStatus)) {
      const selected = form.positionExecutedStatus.find(s => s.value === item.value)
      if (selected) {
        type = selected.type || 'DEFAULT'
      }
    }

    return {
      label: item.label,
      value: item.value,
      type: type
    }
  })

  return options
})

const onParameterRefresh = () => {
  const curNode = props.curNode
  
  const storeCmdData = getStoreObjectByNodeId(curNode.id)
  if (!storeCmdData) return

  form.parameters.length = 0
    const configTemplate = getConfigTemplate(storeCmdData.command)
    if (configTemplate) {
      let instConfig = getInstConfig( storeCmdData.command, configTemplate)

      if (configTemplate.inputComponent) {
        inputComponent.value = configTemplate.inputComponent
      }
      
      if (configTemplate.children) {
        configTemplate.children.forEach(item => {
          form.parameters.push({
            key: item.key,
            name: item.name,
            value: (!instConfig) ? '' : instConfig[item.key]
          })
        })
      } else {
        form.parameters.push({
          name: configTemplate.name,
          value: instConfig
        })
      }
    }
}

const transformNodeToFormData = () => {
  debugger
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  
  form.name = curNode.getData().name
  form.description = curNode.getData().description

  const storeCmdData = getStoreObjectByNodeId(curNode.id)
  if (!storeCmdData) return

  form.failedThen = storeCmdData.failedThen
  form.failedRetries = storeCmdData.failedRetries
  form.positionCheckingStatus = toArray(storeCmdData.positionCheckingStatus)
  form.positionPreExecutionStatus = toArray(storeCmdData.positionPreExecutionStatus)
 // refreshPositionPreExecutionStatusOptions()
 
  form.postExecution = storeCmdData.postExecution
  
  form.positionExecutedStatus = toArray(storeCmdData.positionExecutedStatus)

  form.executedDelay = storeCmdData.executedDelay
  form.materialConsumption = storeCmdData.materialConsumption
  form.isOption = storeCmdData.isOption==='Y'? 'Y' : 'N'
  form.optionCode = storeCmdData.optionCode
  form.comment = storeCmdData.comment

  form.parameter = storeCmdData.parameter
  form.parameters.length = 0
  const configTemplate = getConfigTemplate(storeCmdData.command)
  if (configTemplate) {
    let instConfig = getInstConfig( storeCmdData, configTemplate)

    if (configTemplate.inputComponent) {
      inputComponent.value = configTemplate.inputComponent
    }
    
    if (configTemplate.children) {
      configTemplate.children.forEach(item => {
        form.parameters.push({
          key: item.key,
          name: item.name,
          value: (!instConfig) ? '' : instConfig[item.key]
        })
      })
    } else {
      form.parameters.push({
        name: configTemplate.name,
        value: instConfig
      })
    }
  }
}


const transformFormDataToNode = () => {
  debugger
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  
  curNode.getData().name = form.name
  curNode.getData().description = form.description
  
  const storeCmdData = getStoreObjectByNodeId(curNode.id)
  if (!storeCmdData) return

  storeCmdData.name = form.name
  storeCmdData.description = form.description
  storeCmdData.failedThen = form.failedThen
  storeCmdData.failedRetries = form.failedRetries
  if (Array.isArray(form.positionCheckingStatus)) {
    
    storeCmdData.positionCheckingStatus = form.positionCheckingStatus.map(item => {
      return `${item.value}@${item.type}`
    })
  }
  //storeCmdData.positionCheckingStatus = form.positionCheckingStatus
  if (Array.isArray(form.positionPreExecutionStatus)) {
    
    storeCmdData.positionPreExecutionStatus = form.positionPreExecutionStatus.map(item => {
      return `${item.value}@${item.type}`
    })
  }
  //storeCmdData.positionPreExecutionStatus = form.positionPreExecutionStatus
  
  storeCmdData.postExecution = form.postExecution
  
  if (Array.isArray(form.positionExecutedStatus)) {
    
    storeCmdData.positionExecutedStatus = form.positionExecutedStatus.map(item => {
      return `${item.value}@${item.type}`
    })
  }
  //storeCmdData.positionExecutedStatus = form.positionExecutedStatus
  storeCmdData.executedDelay = form.executedDelay
  storeCmdData.materialConsumption = form.materialConsumption
  storeCmdData.comment = form.comment
  storeCmdData.isOption = form.isOption
  storeCmdData.optionCode = form.optionCode
  
  storeCmdData.parameter = form.parameter

  const configTemplate = getConfigTemplate(storeCmdData.command)
  if (configTemplate) {
    
    if (inputComponent.value === 'input' 
    || inputComponent.value === 'position-input' 
    || inputComponent.value === 'position-value-map'
    || inputComponent.value === 'restful-req-map') {
      if (configTemplate.children) {
        const instConfigObj = {}
        form.parameters.forEach(item => {
          instConfigObj[item.key] = item.value
        })
        storeCmdData.parameter = JSON.stringify(instConfigObj)
      } else {
        storeCmdData.parameter = form.parameters[0].value
      }
    }
  }

  const changeState = 'changed from property'
  curNode.setData({ ...curNode.getData(), changeState })
}

const loadPositionNameOptions = async () => {
  const res = await queryPositionNamesForSelection()
  positionNameOptions.value = []
  if (res && res.length > 0) {
    res.forEach(item => {
      positionNameOptions.value.push({
        label: item.name+':'+item.type,
        value: item.name+':'+item.type
      })
    })
  }
}

const loadPostExecutions = async () => {

  try {
    const res = await crudFormula.queryForSelection({ 
      type: 'CMD_POST_SCRIPT' 
    })
    postExecutions.value = res

  } catch (error) {
    //
  }
}

const openScriptEditor = (name) => {
  // 打开脚本编辑器
  if (name) {
    lesScriptName.value = name
  } else {
    lesScriptName.value = null
  }
  lesScriptEditorDialogVisible.value = true
}

const onScriptSubmitted = (script) => {
  // 处理脚本提交事件

  lesScriptEditorDialogVisible.value = false
}

// 监听器
//watch(dataStore, () => {
//  transformNodeToFormData()
//}, { deep: true })

watch(() => props.curNode, (newData) => {
  debugger
  transformNodeToFormData()
})

watch(()=>form, (newData, oldDate) => {
  debugger
  transformFormDataToNode()
}, { deep: true, onTrigger: (e) => {
  debugger
} })

// 生命周期钩子
onMounted(() => {
  console.log('CommandProperty mounted..')
  transformNodeToFormData()
  loadPostExecutions()
  loadPositionNameOptions()
 // refreshPositionPreExecutionStatusOptions()
})
// 在 script setup 部分添加此方法
const handleTypeChange = (item) => {
  // 查找并更新 form.positionPreExecutionStatus 中对应项的 type
  if (form.positionPreExecutionStatus && Array.isArray(form.positionPreExecutionStatus)) {
    const index = form.positionPreExecutionStatus.findIndex(
      selected => selected.value === item.value
    )
    if (index !== -1) {
      // 创建新对象以避免直接修改
      const updatedItem = { ...form.positionPreExecutionStatus[index], type: item.type }
      // 替换数组中的项
      form.positionPreExecutionStatus.splice(index, 1, updatedItem)
    }
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  :deep(.el-card__body) {
    padding: 10px;
  }
  :deep(.el-form-item__label) {
    line-height: 20px;
  }
}
.operation {
  padding: 2px 2px;
  margin-left: 4px;
}
.btn-parameter-refresh {
  width: 18px;
  height: 18px;
  padding: 2px 2px;
  margin-left: 4px;
  margin-bottom: 4px;
}
</style>
