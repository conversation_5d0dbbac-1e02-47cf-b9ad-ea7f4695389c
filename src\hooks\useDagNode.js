import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Graph, Path, StringExt } from '@antv/x6'
import { create } from 'sortablejs'

// 节点类型常量
const NodeType = {
  STATION: 'STATION',
  PROCEDURE: 'PROCEDURE',
  METHOD: 'METHOD',
  STEP: 'STEP',
  ACTION: 'ACTION',
  COMMAND: 'COMMAND'
}  
// 加工类型列表
const PROCESSING_TYPE_LIST = [
  { type: 'PROCEDURE', name: '新建流程' },
  { type: 'METHOD', name: '新建方法' },
  { type: 'STEP', name: '新建步骤' },
  { type: 'ACTION', name: '新建动作' },
  { type: 'COMMAND', name: '新增命令' }
]



// 工具函数
const getDownstreamNodePosition = (node, graph, dx = 250, dy = 100) => {
  const downstreamNodeIdList = []
  graph.getEdges().forEach((edge) => {
    const originEdge = edge.toJSON().data
    if (originEdge.source === node.id) {
      downstreamNodeIdList.push(originEdge.target)
    }
  })

  const position = node.getPosition()
  let minX = Infinity
  let maxY = -Infinity
  
  graph.getNodes().forEach((graphNode) => {
    if (downstreamNodeIdList.includes(graphNode.id)) {
      const nodePosition = graphNode.getPosition()
      if (nodePosition.x < minX) minX = nodePosition.x
      if (nodePosition.y > maxY) maxY = nodePosition.y
    }
  })

  return {
    x: minX !== Infinity ? minX : position.x + dx,
    y: maxY !== -Infinity ? maxY + dy : position.y
  }
}
// 通过不同节点类型获取对应的Ports属性
const getPortsByType = (type, nodeId) => {
  switch (type) {
    case NodeType.STATION:
    case NodeType.PROCEDURE:
      return [{ id: `${nodeId}-out`, group: 'out' }]
    case NodeType.COMMAND:
      return [{ id: `${nodeId}-in`, group: 'in' }]
    default:
      return [
        { id: `${nodeId}-in`, group: 'in' },
        { id: `${nodeId}-out`, group: 'out' }
      ]
  }
}

const getShapeByType = (type) => {
  switch (type) {
    case NodeType.STATION:
      return 'station-node'
    case NodeType.PROCEDURE:
      return 'procedure-node'
    default:
      return 'data-processing-dag-node'
  }
}
// 根据节点数据的类型创建对应节点
export const createNode = (graph, position, data) => {
  if (!graph) return {}
  
  const id = StringExt.uuid()
  data.nodeId = id
  data.dagNodeId = id
  return graph.addNode({
    id,
    shape: getShapeByType(data.type),
    x: position.x,
    y: position.y,
    ports: getPortsByType(data.type, id),
    data
  })
}

// 附件节点
export const appendNode = (fromNode, toNode) => {
  const { graph } = fromNode.model || {}
  if (!graph) return
  const fromSize = fromNode.getSize()
  //debugger
  const position = getDownstreamNodePosition(fromNode, graph, fromSize.width + 40, 40)
  toNode.position(position.x, position.y)
  createEdge(fromNode.id, toNode.id, graph)
}

// 创建连接线
export const createEdge = (source, target, graph) => {
  if (!graph) return
  
  graph.addEdge({
    id: StringExt.uuid(),
    shape: 'data-processing-curve',
    source: { cell: source, port: `${source}-out` },
    target: { cell: target, port: `${target}-in` },
    zIndex: -1,
    data: { source, target }
  })
}


export function useDagNode(options) {
  // 配置项
  const {
    node : paramNode,          // 当前节点对象
    graph : paramGraph           // 图实例
  } = options

  if (!paramNode && !paramGraph) {
    console.error('useDagNode: node or graph is required')
    return
  }
  let node = paramNode
  let graph = paramGraph
  if (!paramGraph && node.model) {
    graph = node.model.graph
  }

  // 响应式状态
  const imageAccessUrl = ref(null) // 图片访问路径
    
  const plusActionSelected = ref(false)

  const baseUrl = import.meta.env.VITE_APP_BASE_API === '/' ? '' : import.meta.env.VITE_APP_BASE_API
  imageAccessUrl.value = baseUrl + '/file/%E5%9B%BE%E7%89%87/'


  // 计算属性
  const plusDropdown = computed(() => ({
    'plus-dropdown': true,
    'plus-dropdown-hide': !plusActionSelected.value,
    'plus-dropdown-visible': plusActionSelected.value
  }))

  // 方法定义
    
  const getNextNodeTypeAndName = (node) => {
    const curData = node.getData()
    const curNodeType = curData.type
    
    if (curNodeType === NodeType.STATION) {
      return { ...PROCESSING_TYPE_LIST[3], parentNodeId: node.id } 
    }

    const index = PROCESSING_TYPE_LIST.findIndex(item => item.type === curNodeType)
    const nextType = index < PROCESSING_TYPE_LIST.length - 1 ? PROCESSING_TYPE_LIST[index + 1] : null
    
    return nextType ? { ...nextType, parentNodeId: node.id } : null
  }

  const mouseEnter = () => {
    const ports = node.getPorts() || []
    ports.forEach((port) => {
      node.setPortProp(port.id, 'attrs/circle', {
        fill: '#fff',
        stroke: '#85A5FF'
      })
    })
  }

  const mouseLeave = () => {
    const ports = node.getPorts() || []
    ports.forEach((port) => {
      node.setPortProp(port.id, 'attrs/circle', {
        fill: 'transparent',
        stroke: 'transparent'
      })
    })
  }
    
  const createDownstream = (nodeData) => {
    if (graph) {
      const position = getDownstreamNodePosition(node, graph, 250, 100)
      const newNode = createNode(graph, position, nodeData)
      createEdge(node.id, newNode.id, graph)
    }
  }
// 是否处于调试模式
const isDebugging = computed(() => {
  const { graph } = node.model || {}
  if (graph) {
    return graph.dataStore?.isDebugging
  }
  return false
})
const nodeStatus = ref('')
const dataStatus = computed(() => {
  return nodeStatus.value || node.getData().status
})
const dataMessage = computed(() => {
  return node.getData().message
})

const statusIconClass = computed(() => {
  return ` status-icon-${dataStatus.value}`
})

  return {
    mouseEnter,
    mouseLeave,
    createDownstream,
    getNextNodeTypeAndName,
    isDebugging,
    nodeStatus,
    dataStatus,
    dataMessage,
    statusIconClass
  }
} 