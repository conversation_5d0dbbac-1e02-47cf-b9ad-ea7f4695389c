<template>
  <div class="app-container">
    <!--表单组件-->
    <el-dialog
      v-model="dialogVisible"
      :title="crud.status.title"
      width="500px"
      align-center
      append-to-body
      :close-on-click-modal="false"
      :before-close="crud.cancelCU"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        size="default"
        label-width="80px"
      >
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.description" style="width: 370px;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </template>
    </el-dialog>
    
    <!-- 字典列表 -->
    <el-row :gutter="10">
      <el-col :xs="24" :sm="24" :md="10" :lg="11" :xl="11" style="margin-bottom: 10px">
        <el-card class="box-card">
          <!--工具栏-->
          <div class="head-container">
            <div v-if="crud.props.searchToggle">
              <!-- 搜索 -->
              <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
                <el-form-item label="" prop="blurry">
                  <el-input
                    v-model="query.blurry"
                    clearable
                    size="default"
                    placeholder="输入名称或者描述搜索"
                    style="width: 200px;"
                    @keyup.enter="crud.toQuery"
                  />
                </el-form-item>
                <el-form-item>
                  <rrOperation />
                </el-form-item>
              </el-form>
            </div>
          </div>
          
          <crudOperation :permission="permission" />

          <!--表格渲染-->
          <el-table
            ref="tableRef"
            v-loading="crud.loading"
            :data="crud.data"
            highlight-current-row
            style="width: 100%"
            @selection-change="crud.selectionChangeHandler"
            @current-change="handleCurrentChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column :show-overflow-tooltip="true" prop="name" label="名称" />
            <el-table-column :show-overflow-tooltip="true" prop="description" label="描述" />
            <el-table-column
              v-if="checkPer(['admin','dict:edit','dict:del'])"
              label="操作"
              width="130px"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-card>
      </el-col>
      
      <!-- 字典详情列表 -->
      <el-col :xs="24" :sm="24" :md="14" :lg="13" :xl="13">
        <el-card class="box-card">
          <template #header>
            <div class="clearfix">
              <span>字典详情</span>
              <el-button
                v-if="checkPer(['admin','dict:add']) && dictDetailRef?.query.dictName"
                class="filter-item"
                size="small"
                style="float: right;padding: 4px 10px"
                type="primary"
                @click="dictDetailRef?.crud.toAdd()"
              >
                <el-icon><Plus /></el-icon>
                新增
              </el-button>
            </div>
          </template>
          <dictDetail ref="dictDetailRef" :permission="permission" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, provide, inject } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { useCrud } from '@/hooks/useCrud'
import dictDetail from './dictDetail.vue'
import crudDict from '@/api/system/dict'
import pagination from '@/components/Crud/Pagination.vue'
import crudOperation from '@/components/Crud/crud.operation.vue'
import rrOperation from '@/components/Crud/RR.operation.vue'
import udOperation from '@/components/Crud/UD.operation.vue'

// 注入权限检查函数
const checkPer = inject('checkPer')

// refs
const tableRef = ref(null)
const formRef = ref(null)
const dictDetailRef = ref(null)

// 使用 crud hook
const { crud, query, form, rules } = useCrud({
  title: '字典',
  url: 'api/dict',
  tableRef: tableRef,
  formRef: formRef,
  crudMethod: { ...crudDict },
  query: {
    blurry: null,
    createTime: null
  },
  rules: {
    name: [
      { required: true, message: '请输入字典名称', trigger: 'blur' }
    ]
  },
  defaultForm: {
    id: null,
    name: null,
    description: null,
    dictDetails: []
  }
})

// 提供 crud 给子组件使用
provide('crud', crud)

// 计算属性
const dialogVisible = computed(() => crud.status.cu > 0)

// 权限配置
const permission = {
  add: ['admin', 'dict:add'],
  edit: ['admin', 'dict:edit'],
  del: ['admin', 'dict:del']
}

// 方法
const handleCurrentChange = (val) => {
  const testPermission = checkPer(['admin','dict:add'])
  const test = checkPer(['admin','dict:add']) && dictDetailRef.value?.query.dictName
  if (val) {
    dictDetailRef.value.query.dictName = val.name
    dictDetailRef.value.dictId = val.id
    dictDetailRef.value.crud.toQuery()
  }
}

// CRUD 钩子
const afterValidateCU = () => {
  return true
}

const beforeToEdit = (crud, form) => {
  form.dictDetails = null
}

crud.afterValidateCU = afterValidateCU
crud.beforeToEdit = beforeToEdit
onMounted(() => {
  crud.refresh()
})
</script>

<style lang="scss" scoped>
.filter-item {
  margin-right: 10px;
}
</style>
