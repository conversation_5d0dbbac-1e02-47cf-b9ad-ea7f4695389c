<template>
  <div class="crud-opts">
    <span class="crud-opts-left">
      <!-- 左侧插槽 -->
      <slot name="left" />
      <el-button
        v-if="crud.optShow.add"
        v-permission="permission.add"
        size="small"
        type="primary"
        :icon="Plus"
        @click="crud.toAdd"
      >
        {{ $t('新增') }}
      </el-button>
      <el-button
        v-if="crud.optShow.edit"
        v-permission="permission.edit"
        size="small"
        type="default"
        :icon="Edit"
        :disabled="crud.selections.length !== 1"
        @click="crud.toEdit(crud.selections[0])"
      >
        {{ $t('修改') }}
      </el-button>
      <el-button
        v-if="crud.optShow.del"
        v-permission="permission.del"
        type="default"
        :icon="Delete"
        size="small"
        :loading="crud.delAllLoading"
        :disabled="crud.selections.length === 0"
        @click="toDelete(crud.selections)"
      >
        {{ $t('删除') }}
      </el-button>
      <el-button
        v-if="crud.optShow.download"
        :loading="crud.downloadLoading"
        :disabled="!crud.data.length"
        size="small"
        type="default"
        :icon="Download"
        @click="crud.doExport"
      >
        {{ $t('导出') }}
      </el-button>
      <!-- 右侧插槽 -->
      <slot name="right" />
    </span>
    <el-button-group class="crud-opts-right">
      <el-button
        size="small"
        plain
        type="info"
        :icon="Search"
        @click="toggleSearch"
      />
      <el-button
        size="small"
        :icon="Refresh"
        @click="crud.refresh"
      />
      <el-popover placement="bottom-end" width="150" trigger="click">
        <template #reference>
          <el-button size="small" :icon="Grid">
            <i class="fa fa-caret-down" aria-hidden="true" />
          </el-button>
        </template>
        <el-checkbox
          v-model="allColumnsSelected"
          :indeterminate="allColumnsSelectedIndeterminate"
          @change="handleCheckAllChange"
        >
          {{ $t('全选') }}
        </el-checkbox>
        <el-checkbox
          v-for="item in tableColumns"
          :key="item.property"
          v-model="item.visible"
          @change="handleCheckedTableColumnsChange(item)"
        >
          {{ item.label }}
        </el-checkbox>
      </el-popover>
    </el-button-group>
  </div>
</template>

<script setup>
import { ref, inject, watch } from 'vue';
import { ElMessageBox } from 'element-plus';
import {
  Plus,
  Edit,
  Delete,
  Download,
  Search,
  Refresh,
  Grid
} from '@element-plus/icons-vue';

// 工具函数：根据参考数组对源数组排序
function sortWithRef(src, ref) {
  const result = [...ref];
  let cursor = -1;
  src.forEach((e) => {
    const idx = result.indexOf(e);
    if (idx === -1) {
      cursor += 1;
      result.splice(cursor, 0, e);
    } else {
      cursor = idx;
    }
  });
  return result;
}

// 接收 props
const props = defineProps({
  crud: {
    type: Object,
    default: null
  },
  permission: {
    type: Object,
    default: () => ({})
  },
  hiddenColumns: {
    type: Array,
    default: () => []
  },
  ignoreColumns: {
    type: Array,
    default: () => []
  }
});

// 获取 crud 对象：优先使用 props 传入的，否则从 inject 获取
const crud = props.crud || inject('crud')
if (!crud) {
  throw new Error('`crud` 未提供，请确保在父组件中通过 provide 注入')
}

// 响应式状态
const tableColumns = ref([]);
const allColumnsSelected = ref(true);
const allColumnsSelectedIndeterminate = ref(false);
const ignoreNextTableColumnsChange = ref(false);

// 切换搜索状态
const toggleSearch = () => {
  crud.props.searchToggle = !crud.props.searchToggle;
};

// 更新表格列信息
const updateTableColumns = () => {
  const table = crud.getTable();
  debugger
  if (!table) {
    tableColumns.value = [];
    return;
  }
  const columnFilter = (e) =>
    e && e.type === 'default' && e.property && !props.ignoreColumns.includes(e.property);

  const refCols = table.columns.filter(columnFilter);
  if (ignoreNextTableColumnsChange.value) {
    ignoreNextTableColumnsChange.value = false;
    return;
  }

  const columns = [];
  const fullTableColumns = table.$children.map((e) => e.columnConfig).filter(columnFilter);
  const cols = sortWithRef(fullTableColumns, refCols);

  cols.forEach((config) => {
    columns.push({
      property: config.property,
      label: config.label,
      visible: refCols.includes(config)
    });
  });

  tableColumns.value = columns;
};

// 全选/取消全选
const handleCheckAllChange = (val) => {
  if (!val) {
    allColumnsSelected.value = true;
    return;
  }
  tableColumns.value.forEach((column) => {
    if (!column.visible) {
      column.visible = true;
      updateColumnVisible(column);
    }
  });
  allColumnsSelected.value = val;
  allColumnsSelectedIndeterminate.value = false;
};

// 更新单个列的可见性
const handleCheckedTableColumnsChange = (item) => {
  const totalCount = tableColumns.value.length;
  const selectedCount = tableColumns.value.filter((col) => col.visible).length;

  if (selectedCount === 0) {
    crud.notify('请至少选择一列', 'warning');
    item.visible = true;
    return;
  }

  allColumnsSelected.value = selectedCount === totalCount;
  allColumnsSelectedIndeterminate.value = selectedCount > 0 && selectedCount < totalCount;

  updateColumnVisible(item);
};

// 更新列可见性
const updateColumnVisible = (item) => {
  const table = crud.props.table;
  const vm = table.$children.find((e) => e.prop === item.property);
  const columnConfig = vm.columnConfig;

  if (item.visible) {
    const columnIndex = tableColumns.value.indexOf(item);
    vm.owner.store.commit('insertColumn', columnConfig, columnIndex + 1, null);
  } else {
    vm.owner.store.commit('removeColumn', columnConfig, null);
  }

  ignoreNextTableColumnsChange.value = true;
};

// 删除选中数据
const toDelete = (datas) => {
  ElMessageBox.confirm(`确认删除选中的${datas.length}条数据?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      crud.delAllLoading = true;
      crud.doDelete(datas);
    })
    .catch(() => {});
};

// 监听表格变化
watch(
  () => crud.props.table,
  updateTableColumns
);
</script>

<style scoped>
.crud-opts {
  padding: 4px 0;
  display: flex;
  align-items: center;
  border-bottom: 2px solid #e0e6eb;
  margin-top: 0px;
}
.crud-opts .crud-opts-left {
  display: flex;
  justify-content: center;
  .el-button {
    margin-right: 0px;
  }
  .el-button+.el-button {
    margin-left: 6px;
  }
}
.crud-opts .crud-opts-right {
  margin-left: auto;
}
.crud-opts .crud-opts-right span {
  float: left;
}
</style>
