<template>
  <div class="editor-container">
    <div class="left">

      <el-popover
        placement="left"
        :width="400"
        :height="300"
        trigger="click"
        @show="showDeviceNodeGraph"
        @hide="hideDeviceNodeGraph"
      >
        <el-tabs model-value="ImageSelect">
          <el-tab-pane label="图像选择" name="ImageSelect">
            <div style="height: 328px;">
              <el-form :inline="true" :model="localStorageForm" class="demo-form-inline">
                <el-form-item>
                  <el-input
                    v-model="localStorageForm.blurry"
                    placeholder="搜索图片"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button icon="el-icon-search" type="primary" @click="qryLocalStorage">搜索</el-button>
                </el-form-item>
              </el-form>
              <div style="overflow-y: scroll; height:300px;">
                <el-row :gutter="10">
                  <template v-if="localStorageList.length>0">
                    <el-col
                      v-for="(storage,index) in localStorageList"
                      :key="index"
                      :span="6"
                      class="image-select-col"
                      :title="storage.name"
                    >
                      <el-image
                        :src="imageAccessUrl + storage.realName"
                        fit="contain"
                        style="width: 60px; height: 60px;"
                        @click="localStorageClick(storage)"
                      />
                      <span class="image-label image-select-stration">{{ storage.name }}</span>
                    </el-col>
                  </template>
                </el-row>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="图像上传" name="ImageUpload">
            <div style="height: 328px;">
              <el-upload
                class="upload-demo"
                :headers="headers"
                :action="imagesUploadApi"
                :on-preview="handleUploadImagePreview"
                :on-remove="handleUploadImageRemove"
                :on-success="onPicUploadSuccess"
                :before-upload="beforeUpload"
                :file-list="uploadImageList"
                list-type="picture"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <div class="el-upload__tip">只能上传svg/jpg/png文件，且不超过500kb</div>
              </el-upload>
            </div>
          </el-tab-pane>
        </el-tabs>
        <template #reference>
          <div
            class="device-node-bg"
            :style="`background-image:url(${imageAccessUrl}${model});`"
          >
          </div>
        </template>
      </el-popover>

    </div>
    
  </div>
</template>

<script setup>
import { ref, reactive, inject, watch, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { Graph } from '@antv/x6'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { getToken } from '@/utils/auth'
import { getLocalStorage } from '@/api/localStorage'
import { scaleRectangle } from '@/views/components/x6/dag/index'

const store = useStore()
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// 其他全局配置（根据实际存储位置调整）
const imagesUploadApi = computed(() => store.getters.imagesUploadApi)
const baseApi = computed(() => store.getters.baseApi)

const model = defineModel()

const props = defineProps({
  curDevice: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const graph = ref(null)
const markers = ref([])
const localStorageForm = reactive({ blurry: '' })
const localStorageList = ref([])
const uploadImageList = ref([])
const headers = reactive({ Authorization: getToken() })

const getCurNode = inject('getCurNode' )
const getNode = inject('getNode')


// 图片相关方法
const localStorageClick = (storage) => {
  model.value = storage.realName
}

const qryLocalStorage = async () => {
  const res = await getLocalStorage({ ...localStorageForm })
  localStorageList.value = res
}

const onPicUploadSuccess = (res) => {
  model.value = res.realName
}

// 空方法保持模板结构
const handleUploadImagePreview = () => {}
const handleUploadImageRemove = () => {}

// 添加图片上传前的处理函数
const beforeUpload = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = (e) => {
      const image = new Image()
      image.src = e.target.result
      image.onload = () => {
        // 获取图片实际尺寸
        const {width, height} = scaleRectangle(image.width,image.height)

        // 更新设备尺寸
        props.curDevice.layoutWidth = width
        props.curDevice.layoutHeight = height

        if (getCurNode){
          const curNode = getCurNode()
          curNode.resize(width, height)
        }
        
        resolve(file)
      }
    }
  })
}

onMounted(() => {
  qryLocalStorage()
})

// 导出需要的方法供模板使用
defineExpose({
  getCurNode
})
</script>

<style  rel="stylesheet/scss" lang="scss" scoped>

.device-node-bg {
  width: 100px;
  height: 100px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  background-color: antiquewhite;
 // border: 4px solid antiquewhite;

  cursor: pointer;
  position: relative;
}

  .marker {
  position: absolute;
  width: 20px;
  aspect-ratio: 1 / 1;

  border-radius: 50%;
  cursor: pointer;
  transform: translate(-50%, -50%);
  border: 1px solid white; /* Optional for better visibility */
}
.marker-NONE {
  background-color: rgb(185, 185, 185);
  display: block;
}
.marker-IDLE {
  background-color: white;
  display: block;
}
.marker-IDLE_CONTAINER {
  background-color: rgb(6, 185, 235);
  display: none;
}
.marker-HOLD {
  background-color: rgb(0, 255, 0);
  display: block;
}
.marker-HOLD_RUNNING {
  background-color: rgb(0, 255, 0);
  display: block;
}
.marker-TO_HOLD {
  background-color: rgb(0, 255, 0);
  animation: opacity-blink 1s infinite alternate;
  display: block;
}
.marker-DONE {
  background-color: rgb(0, 140, 255);
  display: block;
}
@keyframes opacity-blink {
    0% {
        background-color: rgba(0, 255, 0, 1);
    }
    100% {
        background-color: rgba(255, 0, 0, 0);
    }
}

.marker-LEAVE {
  background-color: rgb(221, 232, 18);
  display: block;
}
.marker-SELECTED {
  border: 2px solid rgb(233, 126, 3);
  animation: selected-opacity-blink 1s infinite alternate;
  display: block;
}

@keyframes selected-opacity-blink {
    0% {
        border-color: rgba(233, 126, 3, 1);
    }
    100% {
      border-color: rgba(255, 0, 0, 0);
    }
}

.runner {
  display: none;
}
.runner-HOLD_RUNNING {
  display:block;

  width: 100%;
  aspect-ratio: 1;
  border-radius: 50%;
  background:
    radial-gradient(farthest-side,#ffa516 94%,#0000) top/4px 4px no-repeat,
    conic-gradient(#0000 30%,#ffa516);
  -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 4px),#000 0);
  animation: l13 1s infinite linear;
}
@keyframes l13{
  100%{transform: rotate(1turn)}
}
.badge-item {
  margin-top: 10px;
  margin-right: 40px;
  position:absolute;
  z-index: 3998;
}

.editor-container {
  display: flex;
  height: 100px;
  .left{
    width: 108px;
    height: 108px;
    background-color: antiquewhite;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .right{
    margin-left: 20px;
    width: 100px;
    display: flex;
    flex-direction: column;
  }
}

#deviceNodeGraph {
  width: 100%;
  height: 300px;
  background-color: antiquewhite;
}

.image-select-col{
  background-color: antiquewhite;
  padding:5px;
  border: 1px solid #fff;
}
.image-select-col:hover {
  background-color:lightskyblue;
  border: 1px solid #ffa516;
  border-radius: 4px;
  cursor: pointer;
}
.image-label {
  display: block;
  width: 60px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

}
</style>
