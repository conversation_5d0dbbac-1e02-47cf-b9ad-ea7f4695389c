import store from '@/store';

export default {
  install(app) {
    app.config.globalProperties.checkPer = (value) => {
      if (value && Array.isArray(value) && value.length > 0) {
        const roles = store.getters?.roles || [];
        const permissionRoles = value;
        return roles.some((role) => permissionRoles.includes(role));
      } else {
        console.error(`need roles! Like v-permission="['admin','editor']"`);
        return false;
      }
    };
    app.provide('checkPer', app.config.globalProperties.checkPer);
  },
};