<template>
  <div class="property-container">

    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="动作名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="异步模式" prop="asyncMode">
        <el-select v-model="form.asyncMode" placeholder="请选择" size="small">
          <el-option label="否" value="N" />
          <el-option label="是" value="Y" />
        </el-select>
      </el-form-item>
      <el-divider content-position="left">可选变量</el-divider>
      <VariablesEditor
        v-model="form.variables"
        :action-id="getActionId()"
        @change="handleVariablesChange"
      />
    </el-form>

    <el-divider content-position="left">预置条件</el-divider>
    <!--
    <el-table ref="tableRef" :data="form.predicatesArr" size="small" cell-class-name="predicate-table-cell" max-height="450" style="width:100%" class="predicate-table">
      <el-table-column prop="operator" label="连接符" width="80">
        <template #default="{ row }">
          <el-select v-model="row.joinOperator" placeholder="请选择" size="small">
            <el-option label="与" value="and" />
            <el-option label="或" value="or" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" min-width="100">
        <template #default="{ row, $index }">
          <el-input v-model="row.name" placeholder="请选择" class="input-with-select">
            <template #append>
              <el-dropdown @command="handlePredicateNameCommand">
                <el-button :icon="Search" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`position-select,${$index}`">选择点位</el-dropdown-item>
                    <el-dropdown-item :command="`command,${$index}`">选择指令</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-input>
        </template>
      </el-table-column>
      <el-table-column prop="compareOperator" label="比较符" width="80">
        <template #default="{ row }">
          <el-select v-model="row.compareOperator" placeholder="请选择" size="small">
            <el-option label="等于" value="eq" />
            <el-option label="不等" value="ne" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="targetValue" label="值" >
        <template #default="{ row }">
          <el-input v-model="row.targetValue" class="input-pos" />
        </template>
      </el-table-column>
      <el-table-column v-if="checkPer(['admin','result:edit','result:del'])" prop="zpos" label="操作" width="68px" align="center">
        <template #header>
          <span>操作</span>
          <el-button :icon="Plus" class="operation" @click="addCondition" />
        </template>
        <template #default="{ $index }">
          <div class="operation-container">
            <el-button :icon="Delete" class="operation" @click="deleteCondition($index)" />
          </div>
        </template>
      </el-table-column>
    </el-table>
    -->
    <PredicatesEditor :cur-node="curNode" />
    <el-form-item label="条件不满足时" prop="unmatchedThen">
      <el-select v-model="form.unmatchedThen" placeholder="请选择" size="small">
        <el-option label="等待" value="WAIT" />
        <el-option label="跳过" value="SKIP" />
      </el-select>
    </el-form-item>

    <el-divider content-position="left">所属工作站</el-divider>
    <el-form ref="stationFormRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="工作站名" prop="stationName">
        <el-button type="text" @click="openStationDialog">选择或新增工作站</el-button>
      </el-form-item>
      <el-form-item label="设备列表">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, watch, onMounted } from 'vue'
import { Node } from '@antv/x6'
import { Search, Plus, Delete } from '@element-plus/icons-vue'
import { useDict } from '@/hooks/useDict'
import { queryActionVarDefinesByActionId } from '@/api/procedure'
import PositionSelect from '@/views/components/x6/PositionSelect.vue'
import PredicatesEditor from './components/PredicatesEditor.vue'
import VariablesEditor from './components/VariablesEditor.vue'

// Props 定义
const props = defineProps({
  curNode: {
    type: Object as () => Node,
    default: () => ({})
  }
})

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  asyncMode: null,
  variablesArr: [],
  predicatesArr: [],
  variables: null,
  predicates: null,
  unmatchedThen: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const formRef = ref(null)
//const tableRef = ref(null)
const stationFormRef = ref(null)
const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

const positionSelectDialogVisible = ref(false)
const curPredicateItem = ref({})
const curPredicateItemIndex = ref(0)
const devPosition = ref([{ xpos: 0.0, ypos: 0.0, zpos: 0.0 }])

// 方法定义
const openDialog = () => {
  // 实现对话框打开逻辑
}

const openStationDialog = () => {
  console.log('openStationDialog ...')
}

const getStoreObjectByNodeId = () => {
  const curNode = props.curNode
  const { graph } = curNode.model || {}
  
  if (!graph) {
    return null
  }

  const dataStore = graph.dataStore
  const nodeId = curNode.id
  
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  
  if (!Array.isArray(dataStore.methods)) {
    return null
  }

  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    if (!Array.isArray(methodObj.method?.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      if (!Array.isArray(stepObj.step?.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }
  return null
}

const transformNodeToFormData = async () => {
  
  const curData = getStoreObjectByNodeId()
  if (!curData) return

  form.name = curData.action.name
  form.description = curData.action.description
  form.asyncMode = curData.asyncMode
  //debugger
  form.variables = curData.variables
  

  form.predicatesArr = []
  if (curData.predicates) {
    form.predicatesArr = JSON.parse(curData.predicates)
  }

  form.unmatchedThen = curData.unmatchedThen
}

const transferVariablesArrToVariables = () => {
  const storeData = getStoreObjectByNodeId()
  if (!storeData) return
  
  storeData.variables = null
  if (form.variablesArr?.length > 0) {
    const variables = {}
    form.variablesArr.forEach(item => {
      if (item.value) {
        variables[item.key] = item.value
      }
    })
    storeData.variables = JSON.stringify(variables)
  }
}

const transferPredicatesArrToPredicates = () => {
  const curData = getStoreObjectByNodeId()
  if (curData) {
    curData.predicates = JSON.stringify(form.predicatesArr)
  }
}

const addCondition = () => {
  form.predicatesArr.push({
    joinOperator: 'and',
    name: null,
    sourceType: null,
    sourceParameter: null,
    compareOperator: 'eq',
    targetValue: null
  })
}

const deleteCondition = (rowIndex: number) => {
  form.predicatesArr.splice(rowIndex, 1)
}

const handlePredicateNameCommand = (command: string) => {
  const [cmd, rowIdxStr] = command.split(',')
  const rowIdx = Number(rowIdxStr)
  const curData = form.predicatesArr[rowIdx]
  
  if (cmd === 'position-select') {
    curData.sourceType = cmd
    curPredicateItem.value = curData
    curPredicateItemIndex.value = rowIdx
    positionSelectDialogVisible.value = true
  }
}

const submitPosSelection = () => {
  form.predicatesArr.splice(curPredicateItemIndex.value, 1, curPredicateItem.value)
  positionSelectDialogVisible.value = false
}

// 获取ActionId用于变量编辑器
const getActionId = () => {
  const curNode = props.curNode
  const stepData = getStoreObjectByNodeId(curNode.id)
  return stepData?.action?.id || null
}

// 处理变量变化
const handleVariablesChange = (variables, variablesObj) => {
  console.log('Variables changed:', variables, variablesObj)
  form.variables = variables
  
  const curData = getStoreObjectByNodeId()
  if (!curData) return
  
  curData.variables = variables
}

// 监听器
watch(() => props.curNode, () => {
  transformNodeToFormData()
})

watch(() => form.name, (newValue, oldValue) => {
  const curNode = props.curNode
  console.log(`form.name changed from "${oldValue}" to "${newValue}", curNode.id="${curNode.id}"`)
  curNode.getData().name = newValue
  curNode.setData({ ...curNode.getData(), changeState: 'changed from property' })
})

watch(() => form.description, (newValue, oldValue) => {
  const curNode = props.curNode
  console.log(`form.description changed from "${oldValue}" to "${newValue}"`)
  curNode.getData().description = newValue
})

watch(() => form.asyncMode, (newValue, oldValue) => {
  console.log(`form.asyncMode changed from "${oldValue}" to "${newValue}"`)
  const storeData = getStoreObjectByNodeId()
  if (storeData) {
    storeData.asyncMode = newValue
  }
})

watch(() => form.variablesArr, () => {
  transferVariablesArrToVariables()
}, { deep: true , onTrigger(evt){
  
}})

watch(() => form.predicatesArr, () => {
  transferPredicatesArrToPredicates()
}, { deep: true })

watch(() => form.unmatchedThen, () => {
  const storeData = getStoreObjectByNodeId()
  if (storeData) {
    storeData.unmatchedThen = form.unmatchedThen
  }
}, { deep: false })



// 生命周期钩子
onMounted(() => {
  transformNodeToFormData()
  const storeData = getStoreObjectByNodeId()
  if (storeData) {
    form.asyncMode = storeData.asyncMode
  }
})
</script>

<style lang="scss" scoped>
.operation {
  width: 18px;
  height: 18px;
  padding: 2px 2px;
  margin-left: 4px;
  margin-bottom: 4px;
}
.operation-container {
  display: flex;
  justify-content: center;
}

.variable-label {
  :deep(.el-form-item__label) {
    line-height: 13px;
    text-align: right;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}


.predicate-table {
  :deep(.predicate-table-cell) {
    .cell {
      padding-left: 2px;
      padding-right: 2px;
      
      input {
        padding-left: 2px;
        padding-right: 2px;
      }
      
      .input-pos input {
        width: 56px;
      }
      
      .input-name {
        padding-right: 4px;
      }
    }
  }
  
  :deep(.el-input__inner) {
    padding-left: 2px;
    padding-right: 2px;
  }
  
  :deep(.el-button) {
    padding-left: 2px;
    padding-right: 2px;
  }
  
  :deep(.el-input-group__append) {
    padding-right: 2px;
  }
}
</style>