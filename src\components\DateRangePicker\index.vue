<template>
  <el-date-picker
    v-model="dateRange"
    type="datetimerange"
    :shortcuts="vshortcuts"
    value-format="YYYY-MM-DD HH:mm:ss"
    range-separator="To"
    start-placeholder="请选择开始日期"
    end-placeholder="请选择结束日期"
    @change="handleDateChange"
  />
</template>

<script setup>
import { ref, defineEmits } from 'vue'

// 定义事件
const emit = defineEmits(['change'])

// 日期范围值
const dateRange = ref(null)

// 快捷选项配置
const vshortcuts = [
  {
    text: 'Last week',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    },
  },
  {
    text: 'Last month',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: 'Last 3 months',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]

// 日期变化处理函数
const handleDateChange = (val) => {
  emit('change', val)
}
</script>
