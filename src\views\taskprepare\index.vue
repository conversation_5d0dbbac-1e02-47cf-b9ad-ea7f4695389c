<template>
  <div class="app-container">
    <div class="container-inner">
      <div class="left-column">
        <el-card class="box-card">
          <StarterAsBoardTabs v-if="taskStarterMode==='BOARD'" />
          <StarterAsDefault v-else-if="taskStarterMode==='DEFAULT'" @on-success="crud.refresh()"/>
          <el-tabs v-else v-model="activeMode" class="tab-starter" @tab-click="handleTabClick">
            <el-tab-pane label="试验模式" name="TRIAL">
              <TrialStarter />
            </el-tab-pane>
            <el-tab-pane label="工厂模式" name="FACTORY">
              <FactoryStarter />
            </el-tab-pane>
          </el-tabs>
        </el-card>
        <br>
        <el-card class="box-card">
          <!--工具栏-->
          <div class="head-container">
            <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
            <crud-operation :crud="crud" :permission="permission" />

            <!--表格渲染-->
            <el-table
              ref="tableRef"
              :data="crud.data"
              size="small"
              style="width: 100%;"
              class="task-execute-table"
              :cell-style="getCellStyle"
              @selection-change="crud.selectionChangeHandler"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="taskNumber" label="任务编号" />
              <el-table-column prop="taskName" label="任务名称" />
              <el-table-column prop="tag" label="样本标签" width="100" >
                <template #default="scope">
                  <el-tag effect="light" >
                    {{ scope.row.tag ? scope.row.tag : '无' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="120">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="message" label="最新执行详情">
                <template #default="{ row }">
                  <span :class="getMessageClassByStatus(row.lastStatus)">{{ row.message }}</span>
                  <el-tag v-if="row.lastStatus && !(row.lastStatus==='FAILED' && row.lastExecutedTaskCommandId)" :type="getStatusType(row.lastStatus)" effect="plain">
                    {{ row.lastStatus }}
                  </el-tag>

                  <el-dropdown v-if="row.lastStatus==='FAILED' && row.lastExecutedTaskCommandId" @command="handleResume">
                    <template #default>
                      <el-button :type="getStatusType(row.lastStatus)" size="small" plain>{{ row.lastStatus }}</el-button>
                    </template>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="'RETRY,'+row.lastExecutedTaskCommandId">重试失败指令</el-dropdown-item>
                        <el-dropdown-item :command="'SKIP,'+row.lastExecutedTaskCommandId">跳过失败指令</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
              <el-table-column prop="updateTime" label="更新时间" width="140" />
              <el-table-column v-if="checkPer(['admin','taskPrepare:edit','taskPrepare:del'])" label="操作" width="100px" align="right">
                <template #default="{ row }">
                  <!--
                  <el-dropdown v-if="row.status==='FAILED' &&  row.lastExecutedTaskCommandId" @command="handleResume" >
                    <el-button type="primary" icon="el-icon-video-play" size="mini" circle />
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="'RETRY,'+row.lastExecutedTaskCommandId">重试失败指令</el-dropdown-item>
                      <el-dropdown-item :command="'SKIP,'+row.lastExecutedTaskCommandId">跳过失败指令</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-button type="danger" icon="el-icon-delete" size="mini" circle />
                  -->
                  <TaskOperation
                    :crud="crud"
                    :data="row"
                    :permission="permission"
                  />
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination :crud="crud" />
          </div>
        </el-card>
      </div>
      <div class="right-column">
        <el-card class="box-card" style="height:100%">
          <!--排程显示-->
          <div class="log-container-title"><i class="el-icon-date">任务排程图</i>
            <el-popover
              placement="left"
              title="任务排程图"
              width="600"
              trigger="click"
              @show="scheduleChartPoperVisiable = true"
              @hide="scheduleChartPoperVisiable = false"
            >
              <ScheduleChart2 v-if="scheduleChartPoperVisiable" />
              <template #reference>
                <el-button type="icon" class="el-icon-setting operation" />
              </template>
            </el-popover>
          </div>
          <div class="head-container">
            <ScheduleChart2 v-if="!scheduleChartPoperVisiable" />
          </div>
          <div class="log-container-title"><i class="el-icon-chat-dot-square">执行日志</i></div>
          <div class="task-log-viewer">
            <TaskLogViewer />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, inject, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { ElNotification } from 'element-plus'
import { useCrud } from '@/hooks/useCrud'
import useWebSocket from '@/hooks/useWebSocket'

import crudTask from '@/api/task'
import { updateFailureFixInfo } from '@/api/taskCommand'

// 组件引入
import ScheduleChart2 from '@/views/components/ScheduleChart2.vue'
import TaskLogViewer from '@/views/components/TaskLogViewer.vue'
import TrialStarter from './TrialStarter.vue'
import FactoryStarter from './FactoryStarter.vue'
import CrudOperation from '@/components/crud/crud.operation.vue'
import Pagination from '@/components/crud/Pagination.vue'
import TaskOperation from './TaskOperation.vue'
import StarterAsBoardTabs from './components/StarterAsBoardTabs.vue'
import StarterAsDefault from './components/StarterAsDefault.vue'


const store = useStore()
const checkPer = inject('checkPer')

// CRUD配置
const { crud, CRUD, query } = useCrud({
  title: '任务启动准备',
  url: 'api/task/queryLatestTasks',
  idField: 'id',
  sort: 'id,desc',
  crudMethod: { ...crudTask },
  optShow: { add: false, edit: false, del: false, download: true }
})

// 响应式状态
const taskStarterMode = ref('BOARD')
const activeMode = ref('TRIAL')
const scheduleChartPoperVisiable = ref(false)
const taskColorDefs = ['tomato', 'teal', 'crimson', 'yellowgreen', 'blueviolet']

// WebSocket集成
const { connect, disconnect } = useWebSocket({
  url: (id) => `${import.meta.env.VITE_APP_WS_API}/webSocket/taskMonitor_${id}`,
  onMessage: (message) => {
    const updatedTaskCmd = JSON.parse(message.msg)
    //debugger
    /*
    const index = crud.data.findIndex(item => item.id === updatedTaskCmd.taskId)
    debugger
    if (index !== -1) {
      const newItem = { ...crud.data[index], ...{
        lastExecutedTaskCommandId: updatedTaskCmd.id,
        lastStatus: updatedTaskCmd.status,
        message: updatedTaskCmd.name + (!updatedTaskCmd.message? "" : ":" + updatedTaskCmd.message),
        updateTime: updatedTaskCmd.updateTime
      } }
      crud.data.splice(index, 1, newItem)
      
    }
      */
    let taskFound = false
    crud.data.forEach(task => {
      if (task.id === updatedTaskCmd.taskId) {
        taskFound = true
        task.lastExecutedTaskCommandId = updatedTaskCmd.id
        task.lastStatus = updatedTaskCmd.status
        task.message = updatedTaskCmd.name + (!updatedTaskCmd.message ? "" : ":" + updatedTaskCmd.message)
        task.updateTime = updatedTaskCmd.updateTime
      }
    })
    
    if (!taskFound) {
      crud.refresh()
    }

  }
})

// 计算属性
const permission = computed(() => ({
  add: ['admin', 'taskPrepare:add'],
  edit: ['admin', 'taskPrepare:edit'],
  del: ['admin', 'taskPrepare:del']
}))

// 方法
const getStatusType = (status) => {
  const statusMap = { SUCCESS: 'success', FAILED: 'danger' }
  return statusMap[status] || 'info'
}

const getMessageClassByStatus = (status) => {
  if (status === 'SUCCESS') {
    return 'message-success'
  } else if (status === 'FAILED') {
    return 'message-danger'
  } else {
    return 'message-info'
  }
}

const getCellStyle = ({ row, columnIndex }) => {
  
  if (columnIndex === 0) {
    return {borderLeft: `4px solid ${taskColorDefs[row.id % taskColorDefs.length]}` ,
      borderTopLeftRadius: '4px',
      borderBottomLeftRadius: '4px'
     }
  }
}

const handleTabClick = async () => {
  if (store.state.user.uiSetting.analyseStart?.activeMode !== activeMode.value) {
    await store.dispatch('saveUiSetting', { 
      analyseStart: { activeMode: activeMode.value }
    })
  }
}
    // 处理异常恢复操作
const handleResume = async (arg, row) => {
    const argArr = arg.split(',')

    debugger
    const taskCommand = {}
    taskCommand.failureFixAs = argArr[0]
    taskCommand.id = argArr[1]
    updateFailureFixInfo(taskCommand).then(res => {
      console.log('updateFailureFixInfo resonse OK.')
      ElNotification.success('恢复成功')
    })
  }
// 生命周期
onMounted(async () => {
  
  // 加载系统设置
  await store.dispatch('setGlobalSettingLatestOne')
  taskStarterMode.value = store.state.globalSetting.globalSetting.taskStarterMode

  if (store.state.user.uiSetting.analyseStart) {
    activeMode.value = store.state.user.uiSetting.analyseStart.activeMode
  }

  crud.refresh()
})

onUnmounted(() => {

})

// CRUD钩子
crud.hooks[CRUD.HOOK.beforeRefresh] = () => true
</script>

<style lang="scss" scoped>
.container-inner {
  display: flex;
  flex-direction: row;
}
.container-inner .left-column {
  flex:1;
  min-width: 400px; /* 设置最小宽度 */
  margin-right: 10px;
}
.container-inner .right-column {
  width: 400px;
  min-width: 200px; /* 设置最小宽度 */
}
.main-form-container{
  display: flex;
  flex-direction: column;
}
.main-form-container .main-form-body{

}
.main-form-container .main-form-footer{
  padding-left: 80px;
  display: flex;
  flex-direction: column;
}
.main-form-footer button {
  width:140px;
  height:36px;
}
.message-danger {
  color: #f56c6c;
}
.message-success {
  color: #67c23a;
}
.task-log-viewer {
  height: calc(100vh - 640px);
  min-height: 370px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.log-container-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.log-container-title i {
  padding-left: 0px;
  font-size: smaller;
  font-weight: 600;
  color: #606266;
}
.log-container-title i::before {
  margin-right: 4px;
}
.task-tag {
  height:18px;
  width:8px;
  border-radius: 4px;
  border-left-width: 4px;
  border-left-color: #67c23a;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.span-message {
  height: 24px;
  font-size: 24px;
  font-weight: bold;
}
.span-message-FAILED {
  color: red;
}
.span-message-SUCCESS {
  color: green;
}
.span-message-RUNNING {
  color: #0580B4;
}


.tab-starter {
  margin-bottom: 0px;
}

.operation {
  width:18px;
  padding: 2px 2px;
  margin-left: 4px;
}
</style>
