<template>
  <div class="result-statistics">
    <!-- 全局筛选控件 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <template #header>
          <div class="card-header">
            <span>数据筛选</span>
          </div>
        </template>
        <el-form :model="globalFilters" inline>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="globalFilters.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleGlobalFilterChange"
            />
          </el-form-item>
          <el-form-item label="检测方法">
            <el-select
              v-model="globalFilters.taskMethodName"
              placeholder="请选择检测方法"
              clearable
              @change="handleGlobalFilterChange"
            >
              <el-option
                v-for="method in methodOptions"
                :key="method.value"
                :label="method.label"
                :value="method.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="样品类型">
            <el-select
              v-model="globalFilters.sampleType"
              placeholder="请选择样品类型"
              clearable
              @change="handleGlobalFilterChange"
            >
              <el-option
                v-for="type in sampleTypeOptions"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="refreshAllCharts">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计图表网格布局 -->
    <div class="charts-grid">
      <!-- 检测值折线图 -->
      <div class="chart-item" :class="{ 'expanded': expandedChart === 'line' }">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>检测值趋势图</span>
              <div class="header-actions">
                <el-button
                  size="small"
                  text
                  @click="toggleExpand('line')"
                  :icon="expandedChart === 'line' ? FullScreen : ZoomIn"
                />
              </div>
            </div>
          </template>
          <div class="chart-filters">
            <el-select
              v-model="lineChartFilters.detectionItem"
              placeholder="选择检测物"
              size="small"
              @change="updateLineChart"
            >
              <el-option
                v-for="item in detectionItemOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div ref="lineChartRef" class="chart-container"></div>
        </el-card>
      </div>

      <!-- 检测方法统计柱状图 -->
      <div class="chart-item" :class="{ 'expanded': expandedChart === 'bar' }">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>检测方法统计</span>
              <div class="header-actions">
                <el-button
                  size="small"
                  text
                  @click="toggleExpand('bar')"
                  :icon="expandedChart === 'bar' ? FullScreen : ZoomIn"
                />
              </div>
            </div>
          </template>
          <div class="chart-filters">
            <el-radio-group
              v-model="barChartFilters.statisticType"
              size="small"
              @change="updateBarChart"
            >
              <el-radio-button label="count">检测次数</el-radio-button>
              <el-radio-button label="abnormal">异常次数</el-radio-button>
            </el-radio-group>
          </div>
          <div ref="barChartRef" class="chart-container"></div>
        </el-card>
      </div>

      <!-- 检测结论饼图 -->
      <div class="chart-item" :class="{ 'expanded': expandedChart === 'pie' }">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>检测结论分布</span>
              <div class="header-actions">
                <el-button
                  size="small"
                  text
                  @click="toggleExpand('pie')"
                  :icon="expandedChart === 'pie' ? FullScreen : ZoomIn"
                />
              </div>
            </div>
          </template>
          <div ref="pieChartRef" class="chart-container"></div>
        </el-card>
      </div>

      <!-- 样品检测热力图 -->
      <div class="chart-item" :class="{ 'expanded': expandedChart === 'heatmap' }">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>样品检测热力图</span>
              <div class="header-actions">
                <el-button
                  size="small"
                  text
                  @click="toggleExpand('heatmap')"
                  :icon="expandedChart === 'heatmap' ? FullScreen : ZoomIn"
                />
              </div>
            </div>
          </template>
          <div class="chart-filters">
            <el-select
              v-model="heatmapFilters.timeGranularity"
              placeholder="时间粒度"
              size="small"
              @change="updateHeatmapChart"
            >
              <el-option label="按小时" value="hour" />
              <el-option label="按天" value="day" />
              <el-option label="按周" value="week" />
            </el-select>
          </div>
          <div ref="heatmapChartRef" class="chart-container"></div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import { Refresh, ZoomIn, FullScreen } from '@element-plus/icons-vue'
import { debounce } from '@/utils'

// 响应式数据
const expandedChart = ref(null)
const lineChartRef = ref(null)
const barChartRef = ref(null)
const pieChartRef = ref(null)
const heatmapChartRef = ref(null)

// 图表实例
let lineChart = null
let barChart = null
let pieChart = null
let heatmapChart = null

// 全局筛选条件
const globalFilters = reactive({
  dateRange: null,
  taskMethodName: null,
  sampleType: null
})

// 各图表筛选条件
const lineChartFilters = reactive({
  detectionItem: null
})

const barChartFilters = reactive({
  statisticType: 'count'
})

const heatmapFilters = reactive({
  timeGranularity: 'day'
})

// 选项数据
const methodOptions = ref([
  { label: '标准检测法', value: 'standard' },
  { label: '快速检测法', value: 'rapid' },
  { label: '精密检测法', value: 'precision' }
])

const sampleTypeOptions = ref([
  { label: '水样', value: 'water' },
  { label: '土壤', value: 'soil' },
  { label: '空气', value: 'air' }
])

const detectionItemOptions = ref([
  { label: 'pH值', value: 'ph' },
  { label: '溶解氧', value: 'oxygen' },
  { label: '浊度', value: 'turbidity' },
  { label: '温度', value: 'temperature' }
])

// 方法定义
const toggleExpand = (chartType) => {
  if (expandedChart.value === chartType) {
    expandedChart.value = null
  } else {
    expandedChart.value = chartType
  }

  // 等待DOM更新和CSS动画完成后再调整图表大小
  nextTick(() => {
    // 使用setTimeout等待CSS transition动画完成 (300ms + 50ms缓冲)
    setTimeout(() => {
      resizeSpecificChart(chartType)
      // 也调整其他图表，因为网格布局可能影响其他图表
      resizeAllCharts()
    }, 350)
  })
}

// 调整特定图表大小
const resizeSpecificChart = (chartType) => {
  try {
    switch (chartType) {
      case 'line':
        if (lineChart && lineChartRef.value) {
          lineChart.resize()
        }
        break
      case 'bar':
        if (barChart && barChartRef.value) {
          barChart.resize()
        }
        break
      case 'pie':
        if (pieChart && pieChartRef.value) {
          pieChart.resize()
        }
        break
      case 'heatmap':
        if (heatmapChart && heatmapChartRef.value) {
          heatmapChart.resize()
        }
        break
    }
  } catch (error) {
    console.warn(`图表${chartType} resize失败:`, error)
  }
}

const handleGlobalFilterChange = () => {
  // 全局筛选变化时，更新所有图表
  updateAllCharts()
}

const refreshAllCharts = () => {
  updateAllCharts()
}

const resizeAllCharts = () => {
  // 确保图表实例存在且容器已渲染
  try {
    if (lineChart && lineChartRef.value) {
      lineChart.resize()
    }
    if (barChart && barChartRef.value) {
      barChart.resize()
    }
    if (pieChart && pieChartRef.value) {
      pieChart.resize()
    }
    if (heatmapChart && heatmapChartRef.value) {
      heatmapChart.resize()
    }
  } catch (error) {
    console.warn('图表resize失败:', error)
  }
}

// 初始化图表
const initCharts = () => {
  initLineChart()
  initBarChart()
  initPieChart()
  initHeatmapChart()
}

const initLineChart = () => {
  if (!lineChartRef.value) return
  debugger
  lineChart = echarts.init(lineChartRef.value)
  updateLineChart()
}

const initBarChart = () => {
  if (!barChartRef.value) return
  
  barChart = echarts.init(barChartRef.value)
  updateBarChart()
}

const initPieChart = () => {
  if (!pieChartRef.value) return
  
  pieChart = echarts.init(pieChartRef.value)
  updatePieChart()
}

const initHeatmapChart = () => {
  if (!heatmapChartRef.value) return
  
  heatmapChart = echarts.init(heatmapChartRef.value)
  updateHeatmapChart()
}

// 更新图表数据
const updateLineChart = () => {
  if (!lineChart) return

  // 生成模拟时间序列数据
  const generateTimeSeriesData = () => {
    const data = []
    const dates = []
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30) // 最近30天

    for (let i = 0; i < 30; i++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)
      dates.push(currentDate.toISOString().split('T')[0])

      // 根据检测物生成不同范围的模拟数据
      let value
      switch (lineChartFilters.detectionItem) {
        case 'ph':
          value = 7 + (Math.random() - 0.5) * 2 // pH 6-8
          break
        case 'oxygen':
          value = 8 + (Math.random() - 0.5) * 4 // 溶解氧 6-10
          break
        case 'turbidity':
          value = 1 + Math.random() * 3 // 浊度 1-4
          break
        case 'temperature':
          value = 20 + Math.random() * 15 // 温度 20-35
          break
        default:
          value = Math.random() * 10
      }
      data.push(Number(value.toFixed(2)))
    }
    return { dates, data }
  }

  const { dates, data } = generateTimeSeriesData()

  const option = {
    title: {
      text: `${lineChartFilters.detectionItem || 'pH值'}检测值趋势`,
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        return `${param.axisValue}<br/>${param.seriesName}: ${param.value}`
      }
    },
    legend: {
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates
    },
    yAxis: {
      type: 'value',
      scale: true
    },
    series: [{
      name: lineChartFilters.detectionItem || 'pH值',
      type: 'line',
      data: data,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2
      },
      areaStyle: {
        opacity: 0.3
      }
    }]
  }

  lineChart.setOption(option, true)
}

const updateBarChart = () => {
  if (!barChart) return

  // 生成模拟统计数据
  const methods = ['标准检测法', '快速检测法', '精密检测法', '高精度检测法', '快速筛查法']
  const countData = [120, 85, 60, 45, 30]
  const abnormalData = [8, 12, 3, 2, 5]

  const isCount = barChartFilters.statisticType === 'count'
  const data = isCount ? countData : abnormalData
  const seriesName = isCount ? '检测次数' : '异常次数'

  const option = {
    title: {
      text: '检测方法统计',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const param = params[0]
        return `${param.axisValue}<br/>${param.seriesName}: ${param.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: methods,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      name: seriesName
    },
    series: [{
      name: seriesName,
      type: 'bar',
      data: data,
      itemStyle: {
        color: isCount ? '#409EFF' : '#F56C6C',
        borderRadius: [4, 4, 0, 0]
      },
      label: {
        show: true,
        position: 'top'
      }
    }]
  }

  barChart.setOption(option, true)
}

const updatePieChart = () => {
  if (!pieChart) return

  // 生成模拟结论分布数据
  const conclusionData = [
    { value: 850, name: '正常', itemStyle: { color: '#67C23A' } },
    { value: 120, name: '轻微异常', itemStyle: { color: '#E6A23C' } },
    { value: 45, name: '严重异常', itemStyle: { color: '#F56C6C' } },
    { value: 15, name: '需复检', itemStyle: { color: '#909399' } }
  ]

  const option = {
    title: {
      text: '检测结论分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [{
      name: '检测结论',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: conclusionData
    }]
  }

  pieChart.setOption(option, true)
}

const updateHeatmapChart = () => {
  if (!heatmapChart) return

  // 根据时间粒度生成不同的数据
  const generateHeatmapData = () => {
    let xAxisData, yAxisData, data = []

    switch (heatmapFilters.timeGranularity) {
      case 'hour':
        xAxisData = Array.from({ length: 24 }, (_, i) => String(i).padStart(2, '0') + ':00')
        yAxisData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        for (let i = 0; i < yAxisData.length; i++) {
          for (let j = 0; j < xAxisData.length; j++) {
            // 工作时间检测频率更高
            const isWorkTime = j >= 8 && j <= 18
            const baseValue = isWorkTime ? 5 : 1
            const value = baseValue + Math.floor(Math.random() * 8)
            data.push([j, i, value])
          }
        }
        break
      case 'day':
        xAxisData = Array.from({ length: 31 }, (_, i) => `${i + 1}日`)
        yAxisData = ['1月', '2月', '3月', '4月', '5月', '6月']
        for (let i = 0; i < yAxisData.length; i++) {
          for (let j = 0; j < xAxisData.length; j++) {
            const value = Math.floor(Math.random() * 20) + 5
            data.push([j, i, value])
          }
        }
        break
      case 'week':
        xAxisData = Array.from({ length: 52 }, (_, i) => `第${i + 1}周`)
        yAxisData = ['2023年', '2024年']
        for (let i = 0; i < yAxisData.length; i++) {
          for (let j = 0; j < xAxisData.length; j++) {
            const value = Math.floor(Math.random() * 50) + 10
            data.push([j, i, value])
          }
        }
        break
      default:
        xAxisData = []
        yAxisData = []
    }

    return { xAxisData, yAxisData, data }
  }

  const { xAxisData, yAxisData, data } = generateHeatmapData()
  const maxValue = Math.max(...data.map(item => item[2]))

  const option = {
    title: {
      text: `样品检测热力图 (${heatmapFilters.timeGranularity === 'hour' ? '按小时' : heatmapFilters.timeGranularity === 'day' ? '按天' : '按周'})`,
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      position: 'top',
      formatter: (params) => {
        return `${yAxisData[params.data[1]]}<br/>${xAxisData[params.data[0]]}<br/>检测次数: ${params.data[2]}`
      }
    },
    grid: {
      height: '60%',
      top: '15%'
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      splitArea: {
        show: true
      },
      axisLabel: {
        rotate: heatmapFilters.timeGranularity === 'week' ? 45 : 0,
        interval: heatmapFilters.timeGranularity === 'week' ? 4 : 'auto'
      }
    },
    yAxis: {
      type: 'category',
      data: yAxisData,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: maxValue,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    series: [{
      name: '检测次数',
      type: 'heatmap',
      data: data,
      label: {
        show: data.length < 200, // 数据点太多时隐藏标签
        fontSize: 10
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  heatmapChart.setOption(option, true)
}

const updateAllCharts = () => {
  updateLineChart()
  updateBarChart()
  updatePieChart()
  updateHeatmapChart()
}

// 监听展开状态变化
watch(expandedChart, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    // 延迟执行resize，确保CSS动画完成
    setTimeout(() => {
      if (newValue) {
        resizeSpecificChart(newValue)
      }
      resizeAllCharts()
    }, 400) // 稍微增加延迟时间
  }
}, { immediate: false })

// ResizeObserver实例
let resizeObserver = null

// 初始化ResizeObserver
const initResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined') {
    resizeObserver = new ResizeObserver((entries) => {
      // 防抖处理，避免频繁调用
      clearTimeout(resizeObserver.timer)
      resizeObserver.timer = setTimeout(() => {
        resizeAllCharts()
      }, 100)
    })

    // 观察所有图表容器
    if (lineChartRef.value) resizeObserver.observe(lineChartRef.value)
    if (barChartRef.value) resizeObserver.observe(barChartRef.value)
    if (pieChartRef.value) resizeObserver.observe(pieChartRef.value)
    if (heatmapChartRef.value) resizeObserver.observe(heatmapChartRef.value)
  }
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initCharts()
    // 初始化ResizeObserver
    setTimeout(() => {
      initResizeObserver()
    }, 500)
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeAllCharts)
})

onBeforeUnmount(() => {
  // 销毁图表实例
  lineChart?.dispose()
  barChart?.dispose()
  pieChart?.dispose()
  heatmapChart?.dispose()

  // 清理ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
    clearTimeout(resizeObserver.timer)
    resizeObserver = null
  }

  // 移除事件监听
  window.removeEventListener('resize', resizeAllCharts)
})
</script>

<style lang="scss" scoped>
.result-statistics {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .filter-section {
    margin-bottom: 20px;
    
    .filter-card {
      .card-header {
        font-weight: bold;
        color: #303133;
      }
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    
    .chart-item {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &.expanded {
        grid-column: 1 / -1;
        grid-row: 1 / -1;
        z-index: 10;
      }

      .chart-card {
        height: 400px;
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        :deep(.el-card__body) {
          height: calc(100% - 80px);
          transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: bold;
          color: #303133;

          .header-actions {
            display: flex;
            gap: 8px;
          }
        }

        .chart-filters {
          margin-bottom: 16px;
          padding: 0 20px;
        }

        .chart-container {
          height: calc(100% - 80px);
          width: 100%;
          transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }

      &.expanded .chart-card {
        height: 80vh;

        :deep(.el-card__body) {
          height: calc(100% - 80px);
        }

        .chart-container {
          height: calc(100% - 120px);
        }
      }
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
