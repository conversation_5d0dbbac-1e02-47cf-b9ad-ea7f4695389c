<template>
  <div class="result-statistics">
    <!-- 全局筛选控件 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <template #header>
          <div class="card-header">
            <span>数据筛选</span>
          </div>
        </template>
        <el-form :model="globalFilters" inline>
          <el-form-item label="时间范围">
            <date-range-picker v-model="globalFilters.featureCreateTime" @change="handleGlobalFilterChange"/>
          </el-form-item>
          <el-form-item label="检测方法">
            
            <el-select v-model="globalFilters.methodName" filterable clearable
              placeholder="请选择检测方法" style="width: 120px;" @change="handleGlobalFilterChange">
              <el-option v-for="item in methodOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item label="样品分类">
            <el-select v-model="globalFilters.sampleCategory" value-key="name" filterable clearable
              placeholder="请选择分类" style="width: 120px;" @change="handleGlobalFilterChange">
              <el-option v-for="item in categoryOptions" :key="item.name" :label="item.name" :value="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="refreshAllCharts">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计图表网格布局 -->
    <div class="charts-grid">
      <!-- 检测值折线图 -->
      <div class="chart-item" :class="{ 'expanded': expandedChart === 'line' }">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>检测值趋势图</span>
              <div class="header-actions">
                <el-button
                  size="small"
                  text
                  @click="toggleExpand('line')"
                  :icon="expandedChart === 'line' ? FullScreen : ZoomIn"
                />
              </div>
            </div>
          </template>
          <!--
          <div class="chart-filters">
            <el-select
              v-model="lineChartFilters.detectionItem"
              placeholder="选择检测物"
              size="small"
              @change="updateLineChart"
            >
              <el-option
                v-for="item in detectionItemOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        -->
          <div ref="lineChartRef" class="chart-container"></div>
        </el-card>
      </div>

      <!-- 检测方法统计柱状图 -->
      <div class="chart-item" :class="{ 'expanded': expandedChart === 'bar' }">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>检测方法统计</span>
              <div class="header-actions">
                <el-button
                  size="small"
                  text
                  @click="toggleExpand('bar')"
                  :icon="expandedChart === 'bar' ? FullScreen : ZoomIn"
                />
              </div>
            </div>
          </template>
          <div class="chart-filters">
            <el-space>
              <el-tag type="success" size="small">
                <el-icon><Check /></el-icon>
                成功次数
              </el-tag>
              <el-tag type="danger" size="small">
                <el-icon><Close /></el-icon>
                失败次数
              </el-tag>
              <el-button
                size="small"
                type="primary"
                text
                @click="updateBarChart"
                :icon="Refresh"
              >
                刷新
              </el-button>
            </el-space>
          </div>
          <div ref="barChartRef" class="chart-container"></div>
        </el-card>
      </div>

      <!-- 检测结论饼图 -->
      <div class="chart-item" :class="{ 'expanded': expandedChart === 'pie' }">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>检测结论分布</span>
              <div class="header-actions">
                <el-button
                  size="small"
                  text
                  @click="toggleExpand('pie')"
                  :icon="expandedChart === 'pie' ? FullScreen : ZoomIn"
                />
              </div>
            </div>
          </template>
          <div ref="pieChartRef" class="chart-container"></div>
        </el-card>
      </div>

      <!-- 样品检测热力图 -->
      <div class="chart-item" :class="{ 'expanded': expandedChart === 'heatmap' }">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>样品检测热力图</span>
              <div class="header-actions">
                <el-button
                  size="small"
                  text
                  @click="toggleExpand('heatmap')"
                  :icon="expandedChart === 'heatmap' ? FullScreen : ZoomIn"
                />
              </div>
            </div>
          </template>
          <div class="chart-filters">
            <el-select
              v-model="heatmapFilters.timeGranularity"
              placeholder="时间粒度"
              size="small"
              @change="updateHeatmapChart"
            >
              <el-option label="按小时" value="hour" />
              <el-option label="按天" value="day" />
              <el-option label="按周" value="week" />
            </el-select>
          </div>
          <div ref="heatmapChartRef" class="chart-container"></div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import { Refresh, ZoomIn, FullScreen, Check, Close } from '@element-plus/icons-vue'
import { debounce } from '@/utils'
import moment from 'moment'
import DateRangePicker from '@/components/DateRangePicker'

import crudStatsResultFeature from '@/api/statsResultFeature'

import crudSampleCategory from '@/api/sampleCategory'
import crudProcedure from '@/api/procedure'



// 响应式数据
const expandedChart = ref(null)
const lineChartRef = ref(null)
const barChartRef = ref(null)
const pieChartRef = ref(null)
const heatmapChartRef = ref(null)

const categoryOptions = ref(null)
const methodOptions = ref(null)

// 图表实例
let lineChart = null
let barChart = null
let pieChart = null
let heatmapChart = null

const end = new Date()
const start = new Date()
      start.setDate(start.getDate() - 7)
// 全局筛选条件
const globalFilters = reactive({
  featureCreateTime:  [moment(start).format('YYYY-MM-DD HH:mm:ss') , moment(end).format('YYYY-MM-DD HH:mm:ss')],
  methodName: null,
  sampleCategory: null
})

// 各图表筛选条件
const lineChartFilters = reactive({
  detectionItem: null
})

const barChartFilters = reactive({
  statisticType: 'count'
})

const heatmapFilters = reactive({
  timeGranularity: 'day'
})

// 选项数据

const sampleTypeOptions = ref([
  { label: '水样', value: 'water' },
  { label: '土壤', value: 'soil' },
  { label: '空气', value: 'air' }
])

const detectionItemOptions = ref([
  { label: 'pH值', value: 'ph' },
  { label: '溶解氧', value: 'oxygen' },
  { label: '浊度', value: 'turbidity' },
  { label: '温度', value: 'temperature' }
])

// 数据加载方法
async function loadCategories() {
  categoryOptions.value = await crudSampleCategory.selectSampleCategories()
}
async function loadMethods() {
  methodOptions.value = await crudProcedure.queryMainMethodNamesForSelection()
}
// 方法定义
const toggleExpand = (chartType) => {
  if (expandedChart.value === chartType) {
    expandedChart.value = null
  } else {
    expandedChart.value = chartType
  }

  // 等待DOM更新和CSS动画完成后再调整图表大小
  nextTick(() => {
    // 使用setTimeout等待CSS transition动画完成 (300ms + 50ms缓冲)
    setTimeout(() => {
      resizeSpecificChart(chartType)
      // 也调整其他图表，因为网格布局可能影响其他图表
      resizeAllCharts()
    }, 350)
  })
}

// 调整特定图表大小
const resizeSpecificChart = (chartType) => {
  try {
    switch (chartType) {
      case 'line':
        if (lineChart && lineChartRef.value) {
          lineChart.resize()
        }
        break
      case 'bar':
        if (barChart && barChartRef.value) {
          barChart.resize()
        }
        break
      case 'pie':
        if (pieChart && pieChartRef.value) {
          pieChart.resize()
        }
        break
      case 'heatmap':
        if (heatmapChart && heatmapChartRef.value) {
          heatmapChart.resize()
        }
        break
    }
  } catch (error) {
    console.warn(`图表${chartType} resize失败:`, error)
  }
}

const handleGlobalFilterChange = () => {
  // 全局筛选变化时，更新所有图表
  updateAllCharts()
}

const refreshAllCharts = () => {
  updateAllCharts()
}

const resizeAllCharts = () => {
  // 确保图表实例存在且容器已渲染
  try {
    if (lineChart && lineChartRef.value) {
      lineChart.resize()
    }
    if (barChart && barChartRef.value) {
      barChart.resize()
    }
    if (pieChart && pieChartRef.value) {
      pieChart.resize()
    }
    if (heatmapChart && heatmapChartRef.value) {
      heatmapChart.resize()
    }
  } catch (error) {
    console.warn('图表resize失败:', error)
  }
}

// 初始化图表
const initCharts = () => {
  initLineChart()
  initBarChart()
  initPieChart()
  initHeatmapChart()
}

const initLineChart = () => {
  if (!lineChartRef.value) return
  debugger
  lineChart = echarts.init(lineChartRef.value)
  updateLineChart()
}

const initBarChart = () => {
  if (!barChartRef.value) return
  
  barChart = echarts.init(barChartRef.value)
  updateBarChart()
}

const initPieChart = () => {
  if (!pieChartRef.value) return
  
  pieChart = echarts.init(pieChartRef.value)
  updatePieChart()
}

const initHeatmapChart = () => {
  if (!heatmapChartRef.value) return
  
  heatmapChart = echarts.init(heatmapChartRef.value)
  updateHeatmapChart()
}

// 更新图表数据
const updateLineChart = async () => {
  if (!lineChart) return

  try {
    // 构建查询参数，合并全局筛选和图表级筛选
    const queryParams = {
      ...globalFilters,
      featureName: lineChartFilters.detectionItem
    }

    // 从API获取真实数据, 原始数据结构List = [{featureName:'A',fCreateTime:'2023-01-01 00:12:11',cnt:1,avgValue:9.2},...]
    const res = await crudStatsResultFeature.queryStatsResultFeatureValuesByHour(queryParams)
debugger
    // 数据格式验证
    if (!res || !Array.isArray(res)) {
      console.warn('API返回数据格式不正确:', res)
      return
    }

    // 处理原始数据，转换为图表所需格式
    const processedData = processRawDataForChart(res)

    // 如果没有数据，显示空状态
    if (!processedData.timeAxis.length || processedData.features.length === 0) {
      const emptyOption = {
        title: {
          text: '检测值趋势图',
          left: 'center',
          textStyle: { fontSize: 14 }
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无数据',
            fontSize: 16,
            fill: '#999'
          }
        }
      }
      lineChart.setOption(emptyOption, true)
      return
    }

    // 数据处理函数：将原始数据转换为图表格式
    function processRawDataForChart(rawData) {
      // 按时间排序
      const sortedData = rawData.sort((a, b) => new Date(a.fCreateTime) - new Date(b.fCreateTime))

      // 获取所有唯一的时间点和特征名称
      const timeSet = new Set()
      const featureSet = new Set()

      sortedData.forEach(item => {
        timeSet.add(item.fCreateTime)
        featureSet.add(item.featureName)
      })

      const timeAxis = Array.from(timeSet).sort()
      const features = Array.from(featureSet)

      // 为每个特征构建数据映射
      const featureDataMap = {}
      features.forEach(feature => {
        featureDataMap[feature] = {
          cntData: new Array(timeAxis.length).fill(0),
          avgValueData: new Array(timeAxis.length).fill(null)
        }
      })

      // 填充数据
      sortedData.forEach(item => {
        const timeIndex = timeAxis.indexOf(item.fCreateTime)
        if (timeIndex !== -1 && featureDataMap[item.featureName]) {
          featureDataMap[item.featureName].cntData[timeIndex] = item.cnt || 0
          featureDataMap[item.featureName].avgValueData[timeIndex] = item.avgValue || null
        }
      })

      return {
        timeAxis,
        features,
        featureDataMap
      }
    }

    // 生成颜色数组
    const barColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
    const lineColors = ['#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f', '#3ba272']

    // 构建混合图表序列数据
    const seriesData = []
    const legendData = []

    // 为每个特征添加柱状图（统计数量）和折线图（平均值）
    processedData.features.forEach((feature, index) => {
      const featureData = processedData.featureDataMap[feature]

      // 柱状图序列 - 统计数量
      seriesData.push({
        name: `${feature}-数量`,
        type: 'bar',
        yAxisIndex: 0,
        data: featureData.cntData,
        itemStyle: {
          color: barColors[index % barColors.length]
        },
        barMaxWidth: 30,
        label: {
          show: false
        }
      })

      // 折线图序列 - 平均值
      seriesData.push({
        name: `${feature}-平均值`,
        type: 'line',
        yAxisIndex: 1,
        data: featureData.avgValueData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: lineColors[index % lineColors.length]
        },
        itemStyle: {
          color: lineColors[index % lineColors.length]
        }
      })

      legendData.push(`${feature}-数量`, `${feature}-平均值`)
    })

    // 构建混合图表配置
    const option = {
      title: {
        text: '检测值趋势统计图',
        left: 'center',
        textStyle: {
          fontSize: 14,
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: (params) => {
          let tooltipText = `<strong>${params[0].axisValue}</strong><br/>`
          params.forEach(param => {
            const value = param.value !== null ? param.value : '无数据'
            tooltipText += `${param.marker}${param.seriesName}: ${value}<br/>`
          })
          return tooltipText
        }
      },
      legend: {
        top: 30,
        type: 'scroll',
        orient: 'horizontal',
        data: legendData
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '12%',
        top: '18%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: processedData.timeAxis,
        axisLabel: {
          formatter: (value) => {
            // 格式化时间显示，只显示日期和时间
            if (value.includes(' ')) {
              const [date, time] = value.split(' ')
              return `${date}\n${time.substring(0, 5)}`
            }
            return value
          },
          rotate: 45,
          interval: 0
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '统计数量',
          position: 'left',
          nameTextStyle: {
            color: '#5470c6'
          },
          axisLabel: {
            formatter: '{value}',
            color: '#5470c6'
          },
          axisLine: {
            lineStyle: {
              color: '#5470c6'
            }
          },
          splitLine: {
            show: false
          }
        },
        {
          type: 'value',
          name: '平均值',
          position: 'right',
          nameTextStyle: {
            color: '#fc8452'
          },
          axisLabel: {
            formatter: '{value}',
            color: '#fc8452'
          },
          axisLine: {
            lineStyle: {
              color: '#fc8452'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              opacity: 0.3
            }
          }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          start: 0,
          end: 100,
          height: 30,
          bottom: 30
        }
      ],
      series: seriesData
    }

    // 更新图表
    lineChart.setOption(option, true)

  } catch (error) {
    console.error('更新检测值趋势图失败:', error)

    // 显示错误状态
    const errorOption = {
      title: {
        text: '检测值趋势统计图',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '数据加载失败，请稍后重试',
          fontSize: 16,
          fill: '#f56c6c'
        }
      }
    }
    lineChart.setOption(errorOption, true)
  }
}

const updateBarChart = async () => {
  if (!barChart) return

  try {
    // 构建查询参数，合并全局筛选和图表级筛选
    const queryParams = {
      ...globalFilters
    }

    // 从API获取真实数据, 原始数据结构List = [{methodName:'A',successCount:100,failedCount:0},...]
    const res = await crudStatsResultFeature.queryStatsMethods(queryParams)

    // 数据格式验证
    if (!res || !Array.isArray(res)) {
      console.warn('检测方法统计API返回数据格式不正确:', res)
      return
    }

    // 处理原始数据，转换为bar图数据格式
    const processedData = processMethodStatsData(res)

    // 如果没有数据，显示空状态
    if (!processedData.methods.length) {
      const emptyOption = {
        title: {
          text: '检测方法统计',
          left: 'center',
          textStyle: { fontSize: 14 }
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无数据',
            fontSize: 16,
            fill: '#999'
          }
        }
      }
      barChart.setOption(emptyOption, true)
      return
    }

    // 数据处理函数：将原始数据转换为图表格式
    function processMethodStatsData(rawData) {
      // 提取方法名称和统计数据
      const methods = rawData.map(item => item.methodName || '未知方法')
      const successData = rawData.map(item => item.successCount || 0)
      const failedData = rawData.map(item => item.failedCount || 0)
      const totalData = rawData.map(item => (item.successCount || 0) + (item.failedCount || 0))

      return {
        methods,
        successData,
        failedData,
        totalData
      }
    }

    // 构建多维柱状图配置
    const option = {
      title: {
        text: '检测方法统计',
        left: 'center',
        textStyle: {
          fontSize: 14,
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params) => {
          let tooltipText = `<strong>${params[0].axisValue}</strong><br/>`
          params.forEach(param => {
            tooltipText += `${param.marker}${param.seriesName}: ${param.value}<br/>`
          })
          // 计算总计和成功率
          if (params.length >= 2) {
            const success = params.find(p => p.seriesName === '成功次数')?.value || 0
            const failed = params.find(p => p.seriesName === '失败次数')?.value || 0
            const total = success + failed
            const successRate = total > 0 ? ((success / total) * 100).toFixed(1) : 0
            tooltipText += `<hr/>总计: ${total}<br/>成功率: ${successRate}%`
          }
          return tooltipText
        }
      },
      legend: {
        top: 30,
        data: ['成功次数', '失败次数']
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '8%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: processedData.methods,
        axisLabel: {
          rotate: 45,
          interval: 0,
          formatter: (value) => {
            // 如果方法名过长，进行截断
            return value.length > 8 ? value.substring(0, 8) + '...' : value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '检测次数',
        nameTextStyle: {
          color: '#666'
        },
        axisLabel: {
          formatter: '{value}'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            opacity: 0.3
          }
        }
      },
      series: [
        {
          name: '成功次数',
          type: 'bar',
          stack: 'total',
          data: processedData.successData,
          itemStyle: {
            color: '#67C23A',
            borderRadius: [0, 0, 0, 0]
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              return params.value > 0 ? params.value : ''
            },
            color: '#fff',
            fontSize: 12
          }
        },
        {
          name: '失败次数',
          type: 'bar',
          stack: 'total',
          data: processedData.failedData,
          itemStyle: {
            color: '#F56C6C',
            borderRadius: [4, 4, 0, 0]
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              return params.value > 0 ? params.value : ''
            },
            color: '#fff',
            fontSize: 12
          }
        }
      ]
    }

    // 更新图表
    barChart.setOption(option, true)

  } catch (error) {
    console.error('更新检测方法统计图失败:', error)

    // 显示错误状态
    const errorOption = {
      title: {
        text: '检测方法统计',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '数据加载失败，请稍后重试',
          fontSize: 16,
          fill: '#f56c6c'
        }
      }
    }
    barChart.setOption(errorOption, true)
  }
}

const updatePieChart = async () => {
  if (!pieChart) return

    // 构建查询参数，合并全局筛选和图表级筛选
    const queryParams = {
      ...globalFilters
    }

    // 从API获取真实数据, 原始数据结构List = [{conclusion:'NORMAL',cnt:100},{conclusion:'ABNORMAL',cnt:10},...]
    const res = await crudStatsResultFeature.queryStatsConclusions(queryParams)
    // 转换为pie图数据格式
    
  // 生成模拟结论分布数据
  const conclusionData = [
    { value: 850, name: '正常', itemStyle: { color: '#67C23A' } },
    { value: 120, name: '轻微异常', itemStyle: { color: '#E6A23C' } },
    { value: 45, name: '严重异常', itemStyle: { color: '#F56C6C' } },
    { value: 15, name: '需复检', itemStyle: { color: '#909399' } }
  ]

  const option = {
    title: {
      text: '检测结论分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [{
      name: '检测结论',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: conclusionData
    }]
  }

  pieChart.setOption(option, true)
}

const updateHeatmapChart = async () => {
  if (!heatmapChart) return

    // 构建查询参数，合并全局筛选和图表级筛选
    const queryParams = {
      ...globalFilters
    }

    // 从API获取真实数据, 原始数据结构List = [{fCreateTime:'2025-01-01 12:00:00',cnt:100},{fCreateTime:'2025-01-02 14:00:00',cnt:10},...]
    const res = await crudStatsResultFeature.queryStatsDateTime(queryParams)
    // 转换为热力图数据格式
    
    
  // 根据时间粒度生成不同的数据
  const generateHeatmapData = () => {
    let xAxisData, yAxisData, data = []

    switch (heatmapFilters.timeGranularity) {
      case 'hour':
        xAxisData = Array.from({ length: 24 }, (_, i) => String(i).padStart(2, '0') + ':00')
        yAxisData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        for (let i = 0; i < yAxisData.length; i++) {
          for (let j = 0; j < xAxisData.length; j++) {
            // 工作时间检测频率更高
            const isWorkTime = j >= 8 && j <= 18
            const baseValue = isWorkTime ? 5 : 1
            const value = baseValue + Math.floor(Math.random() * 8)
            data.push([j, i, value])
          }
        }
        break
      case 'day':
        xAxisData = Array.from({ length: 31 }, (_, i) => `${i + 1}日`)
        yAxisData = ['1月', '2月', '3月', '4月', '5月', '6月']
        for (let i = 0; i < yAxisData.length; i++) {
          for (let j = 0; j < xAxisData.length; j++) {
            const value = Math.floor(Math.random() * 20) + 5
            data.push([j, i, value])
          }
        }
        break
      case 'week':
        xAxisData = Array.from({ length: 52 }, (_, i) => `第${i + 1}周`)
        yAxisData = ['2023年', '2024年']
        for (let i = 0; i < yAxisData.length; i++) {
          for (let j = 0; j < xAxisData.length; j++) {
            const value = Math.floor(Math.random() * 50) + 10
            data.push([j, i, value])
          }
        }
        break
      default:
        xAxisData = []
        yAxisData = []
    }

    return { xAxisData, yAxisData, data }
  }

  const { xAxisData, yAxisData, data } = generateHeatmapData()
  const maxValue = Math.max(...data.map(item => item[2]))

  const option = {
    title: {
      text: `样品检测热力图 (${heatmapFilters.timeGranularity === 'hour' ? '按小时' : heatmapFilters.timeGranularity === 'day' ? '按天' : '按周'})`,
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      position: 'top',
      formatter: (params) => {
        return `${yAxisData[params.data[1]]}<br/>${xAxisData[params.data[0]]}<br/>检测次数: ${params.data[2]}`
      }
    },
    grid: {
      height: '60%',
      top: '15%'
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      splitArea: {
        show: true
      },
      axisLabel: {
        rotate: heatmapFilters.timeGranularity === 'week' ? 45 : 0,
        interval: heatmapFilters.timeGranularity === 'week' ? 4 : 'auto'
      }
    },
    yAxis: {
      type: 'category',
      data: yAxisData,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: maxValue,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '5%',
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    series: [{
      name: '检测次数',
      type: 'heatmap',
      data: data,
      label: {
        show: data.length < 200, // 数据点太多时隐藏标签
        fontSize: 10
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  heatmapChart.setOption(option, true)
}

const updateAllCharts = () => {
  updateLineChart()
  updateBarChart()
  updatePieChart()
  updateHeatmapChart()
}

// 监听展开状态变化
watch(expandedChart, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    // 延迟执行resize，确保CSS动画完成
    setTimeout(() => {
      if (newValue) {
        resizeSpecificChart(newValue)
      }
      resizeAllCharts()
    }, 400) // 稍微增加延迟时间
  }
}, { immediate: false })

// ResizeObserver实例
let resizeObserver = null

// 初始化ResizeObserver
const initResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined') {
    resizeObserver = new ResizeObserver((entries) => {
      // 防抖处理，避免频繁调用
      clearTimeout(resizeObserver.timer)
      resizeObserver.timer = setTimeout(() => {
        resizeAllCharts()
      }, 100)
    })

    // 观察所有图表容器
    if (lineChartRef.value) resizeObserver.observe(lineChartRef.value)
    if (barChartRef.value) resizeObserver.observe(barChartRef.value)
    if (pieChartRef.value) resizeObserver.observe(pieChartRef.value)
    if (heatmapChartRef.value) resizeObserver.observe(heatmapChartRef.value)
  }
}

// 生命周期
onMounted(() => {
  loadCategories()
  loadMethods()

  nextTick(() => {
    initCharts()
    // 初始化ResizeObserver
    setTimeout(() => {
      initResizeObserver()
    }, 500)
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeAllCharts)
})

onBeforeUnmount(() => {
  // 销毁图表实例
  lineChart?.dispose()
  barChart?.dispose()
  pieChart?.dispose()
  heatmapChart?.dispose()

  // 清理ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
    clearTimeout(resizeObserver.timer)
    resizeObserver = null
  }

  // 移除事件监听
  window.removeEventListener('resize', resizeAllCharts)
})
</script>

<style lang="scss" scoped>
.result-statistics {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  .filter-section {
    margin-bottom: 20px;
    
    .filter-card {
      .card-header {
        font-weight: bold;
        color: #303133;
      }
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    
    .chart-item {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &.expanded {
        grid-column: 1 / -1;
        grid-row: 1 / -1;
        z-index: 10;
      }

      .chart-card {
        height: 400px;
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        :deep(.el-card__body) {
          height: calc(100% - 80px);
          transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: bold;
          color: #303133;

          .header-actions {
            display: flex;
            gap: 8px;
          }
        }

        .chart-filters {
          margin-bottom: 16px;
          padding: 0 20px;
        }

        .chart-container {
          height: calc(100% - 10px);
          width: 100%;
          transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }

      &.expanded .chart-card {
        height: 80vh;

        :deep(.el-card__body) {
          height: calc(100% - 80px);
        }

        .chart-container {
          height: calc(100% - 120px);
        }
      }
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
