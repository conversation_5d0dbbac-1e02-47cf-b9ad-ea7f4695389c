<template>
  <div class="property-container">

    <!--表单组件-->
    <el-dialog :close-on-click-modal="false" v-model="commandEditDialogVisible" :title="'设备命令编辑'" width="500px" @open="openCommandEditDialog()">
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="代理设备指令">
          <el-select v-model="form.proxyInstanceCmd.id" filterable size="small" placeholder="请选择" style="width: 370px" @change="onProxyCmdChange">
            <el-option v-for="item in candidateCommands" :key="item.id" :label="item.deviceInstanceName+' '+item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="命令名称" prop="name">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="命令描述">
          <el-input v-model="form.description" style="width: 370px;" />
        </el-form-item>
        <el-divider content-position="left">指令参数</el-divider>
        <el-form-item v-for="item in form.parameters" :key="item.name" :label="item.name" prop="arg1">
          <el-input v-model="item.value" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="text" @click="commandEditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCommandEdit()">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <el-table ref="tableRef" :data="commands" size="small">
      <el-table-column prop="name" label="指令名" />
      <el-table-column prop="parameter" label="参数">
        <template #default="scope">
          <el-input
            v-if="scope.row.parameterEdit"
            v-model="scope.row.testParameter"
            @blur="scope.row.parameterEdit = false"
            type="textarea"
          />
          <div v-else>
            <span>{{ scope.row.testParameter }}</span>
            <el-button :icon="Edit" class="operation" plain @click="openParameterEditor(scope.row)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="checkPer(['admin','deviceDebug:edit'])" label="操作" width="72px" align="center">
        <template #default="scope">
          <div class="operation-container">
            <el-button :type="scope.row.buttonType" plain class="operation" @click="executeCommand(scope.row)">{{ scope.row.status }}</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, inject } from 'vue'
import { Node } from '@antv/x6'

import { queryNoneProxyCmdForSection } from '@/api/deviceLayout'
import deviceInstanceCmd from '@/api/deviceInstanceCmd'
import {
  Edit
} from '@element-plus/icons-vue';


const checkPer = inject('checkPer')

const props = defineProps({
  curNode: {
    type: Node,
    default: () => ({})
  }
})

const emit = defineEmits(['update-device-instance'])

// 响应式状态
const formRef = ref(null)
const tableRef = ref(null)
const commandEditDialogVisible = ref(false)
const candidateCommands = ref([])
const initing = ref(false)
const configs = ref([])
const mockConfigs = ref([])
const commands = ref([])

const defaultForm = { 
  id: null,
  name: '',
  description: '',
  proxyInstanceCmd: { id: '' },
  parameters: [],
  commandType: 'PROXY',
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const form = reactive({ ...defaultForm })

const rules = reactive({
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
})

// 监听器
watch(() => props.curNode, (newVal, oldVal) => {
  transformNodeToFormData()
  console.log(`curNode changed from ${oldVal} to ${newVal}`)
})

watch(form, (newVal) => {
  transformFormDataToNode()
}, { deep: true })

// 生命周期
onMounted(() => {
  console.log('CommandProperty created..')
  transformNodeToFormData()
})

// 方法
const transformNodeToFormData = () => {
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) return

  commands.value = deviceInst.commands.map(cmd => ({
    ...cmd,
    status: cmd.status || 'TEST',
    buttonType: getButtonType(cmd.status),
    parameterEdit: false
  }))

  commands.value.forEach(cmd => {
    cmd.testParameter = cmd.testParameter || cmd.parameter
  })

  form.name = deviceInst.name
  form.description = deviceInst.description
}

const getButtonType = (status) => {
  switch(status) {
    case 'TEST': return 'primary'
    case 'FAILED': return 'danger'
    case 'SUCCESS': return 'success'
    default: return 'primary'
  }
}

const transformFormDataToNode = () => {
  const deviceInst = getCurDeviceInstance()
  if (!deviceInst) return

  deviceInst.commands = commands.value
  const curNode = props.curNode
  curNode.setData({ ...curNode.getData(), changeState: 'changed from property' })
  emit('update-device-instance', deviceInst)
}

const openCommandEditDialog = () => {
  const _this = this
  queryNoneProxyCmdForSection().then(res => {
    candidateCommands.value.splice(0, candidateCommands.value.length)
    for (const r of res) {
      candidateCommands.value.push(r)
    }
  }).catch(err => {
    console.log('error=' + err)
  })
}

const submitCommandEdit = () => {
  // 命令参数显示控件转换为数值
  if (form.proxyInstanceCmd.command.parameterTemplate) {
    const configTemplate = JSON.parse(form.proxyInstanceCmd.command.parameterTemplate)
    if (configTemplate.children) {
      const instConfigObj = {}
      form.parameters.forEach(item => {
        instConfigObj[item.key] = item.value
      })
      form.parameter = JSON.stringify(instConfigObj)
    } else {
      form.parameter = form.parameters[0].value
    }
  }

  commands.value.push({ ...form })
  commandEditDialogVisible.value = false
}

const onProxyCmdChange = (id) => {
  let selectedItem = null
  for (const item of candidateCommands.value) {
    if (item.id === id) {
      selectedItem = item
      break
    }
  }
  if (selectedItem) {
    form.proxyInstanceCmd = selectedItem
    form.name = selectedItem.name
    form.description = selectedItem.description

    // 命令参数转换为显示控件
    trannsformParameterToDisplay()
  }
}

const trannsformParameterToDisplay = () => {
  const selectedItem = form.proxyInstanceCmd
  if (!selectedItem) {
    return
  }
  // 命令参数转换为显示控件
  if (selectedItem.command.parameterTemplate) {
    const configTemplate = JSON.parse(selectedItem.command.parameterTemplate)
    let instConfig = form.parameter
    if (instConfig) {
      if (configTemplate.children) {
        instConfig = JSON.parse(instConfig)
      }
    }
    debugger
    form.parameters.splice(0, form.parameters.length)
    if (configTemplate.children) {
      configTemplate.children.forEach(item => {
        form.parameters.push({
          key: item.key,
          name: item.name,
          value: (!instConfig) ? '' : instConfig[item.key]
        })
      })
    } else {
      form.parameters.push({
        name: configTemplate.name,
        value: instConfig
      })
    }
  }
}

const getCurDeviceInstance = () => {
  const curNode = props.curNode
  const { graph } = curNode.model || {}

  if (!graph) {
    return null
  }

  if (graph.dataStore.deviceInstanceList === undefined || graph.dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in graph.dataStore.deviceInstanceList) {
    const ins = graph.dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === curNode.id) {
      return ins
    }
  }
  return null
}

// 新增命令
const addCommand = () => {
  form.parameter = null
  commandEditDialogVisible.value = true
}

// 编辑命令
const executeCommand = (cmd) => {
  
  cmd.buttonType = 'primary'
  cmd.status = 'RUNNING'
  deviceInstanceCmd.executeCommand(cmd).then(res => {
    
    cmd.status = res.status
        if (cmd.status === 'TEST') {
          cmd.buttonType = 'primary'
        } else if (cmd.status === 'FAILED') {
          cmd.buttonType = 'danger'
        } else if (cmd.status === 'SUCCESS') {
          cmd.buttonType = 'success'
        }
        cmd.parameterEdit = false

  })
}

// 测试参数编辑器开关
const openParameterEditor = (row) => {
  row.parameterEdit = true
}

</script>

<style lang="scss" scoped>
.property-container {
  width: 100%;
}

.property-container label {
  font-weight: 400;
}

.operation {
 // width:18px;
  padding: 2px 2px;
  margin-left: 4px;
}
.operation-container {
  display: flex;
}
</style>
