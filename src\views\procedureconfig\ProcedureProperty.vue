<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="流程名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="流程类型" prop="type">
        <el-select v-model="form.type" >
          <el-option
            v-for="item in  dict.data.proc_types"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="派生于流程" prop="deriveProcNames">
        <el-input v-model="form.deriveProcNames" readonly />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, reactive, watch, onMounted } from 'vue'
import { Node } from '@antv/x6'
import { debounce } from '@/utils'
import { useDict } from '@/hooks/useDict'

// Props 定义
const props = defineProps({
  curNode: {
    type: Object as () => Node,
    default: () => ({})
  }
})

// 注入
const getDataStore = inject('getDataStore')
const dataStore = getDataStore()

// 使用字典
const { dict } = useDict(['proc_types'])

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  type: null,
  deriveProcNames: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const formRef = ref(null)
const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '方法名称不能为空', trigger: 'blur' }
  ]
}

const initing = ref(false)

// 方法定义
const init = () => {
  initing.value = true
}

const transformNodeToFormData = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  form.name = dataStore.name
  form.description = dataStore.description
  form.type = dataStore.type
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  
  if (!Array.isArray(dataStore.methods)) {
    return null
  }

  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    if (!Array.isArray(methodObj.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      if (!Array.isArray(stepObj.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }
  return null
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  
  curNode.getData().name = form.name
  curNode.getData().description = form.description
  dataStore.name = form.name
  dataStore.description = form.description
  dataStore.type = form.type
  
  const storeObj = getStoreObjectByNodeId(curNode.id)
  if (storeObj) {
    storeObj.name = form.name
    storeObj.description = form.description
  }

  const changeState = 'changed from property'
  curNode.setData({ ...curNode.getData(), changeState })
}
// 创建防抖的更新函数
const debouncedTransformFormDataToNode = debounce(() => {
  transformFormDataToNode()
}, 300)
// 监听器
watch(dataStore, () => {
  transformNodeToFormData()
}, { deep: true })

watch(() => props.curNode, () => {
  transformNodeToFormData()
})

watch(form, () => {
  debouncedTransformFormDataToNode()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  transformNodeToFormData()
})
</script>

<style lang="scss" scoped>
</style>
