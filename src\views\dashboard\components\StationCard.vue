<template>
  <div class="station-card">
    <div class="station-header">
      <div class="station-logo">
        <i 
          v-for="(img, index) in station.deviceImgList" 
          :key="index" 
          class="device-image" 
          :class="devicesClass"
          :style="{ backgroundImage: `url(${getIconUrl(img)})` }"
        />
      </div>
      <div class="station-title">
        <span class="station-title-name">{{ station.name }}</span>
        <span class="status">{{ station.status }}</span>
      </div>
    </div>
    <div class="station-body">
      <template v-if="station.actionQueueSize > 0">
        <div 
          v-for="act in station.taskActions" 
          :key="act.id" 
          class="task-action-item"
        >
          <el-tooltip placement="top">
            <template #content>
              {{ act.taskName }}<br>{{ act.taskMethodName }}
            </template>
            <div class="task-action-item-inner">
              <span class="text">{{ act.name }}</span>
              <span class="status">{{ getDictLabel('task_status',act.status) }}</span>
            </div>
          </el-tooltip>
        </div>
      </template>
      <div v-else>无运行动作</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useDict } from '@/hooks/useDict'

const props = defineProps({
  station: {
    type: Object,
    required: true
  }
})

const store = useStore()
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

const { dict, getDictLabel } = useDict(['task_status'])

const getIconUrl = (imagUrl) => {
  return imageAccessUrl.value + imagUrl
}

const devicesClass = computed(() => {
  const devs = props.station.deviceImgList
  let clz = 'device-image '
  if (devs && devs.length <=1) {
    return clz + 'device-image-size-1'
  }else if (devs && devs.length <=4) {
    return clz + 'device-image-size-2'
  }else if (devs && devs.length >4) {
    return clz + 'device-image-size-3'
  } else {
    return clz + 'device-image-size-1'
  }
})

</script>

<style lang="scss" scoped>
.station-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.station-header {
  width: 100%;
  display: flex;
  flex-direction: row;
  background-color: #4689b7;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;

  .station-logo {
    border-top-left-radius: 8px;
    background-color: #528fb7;
    width: 48px;
    height: 48px;
    padding: 4px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    .device-image {
      display: block;
      width: 20px;
      height: 20px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    }
    .device-image-size-1 {
      width: 40px;
      height: 40px;
    }
    .device-image-size-2 {
      width: 20px;
      height: 20px;
    }
    .device-image-size-3 {
      width: 14px;
      height: 14px;
    }
  }

  .station-title {
    flex: 1;
    display: flex;
    flex-direction: column;

    .station-title-name {
      font-size: 12px;
      color: white;
      padding: 4px;
      font-weight: 600;
    }

    .status {
      font-size: 10px;
      color: bisque;
      padding: 4px;
    }
  }
}

.station-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  padding: 4px;

  .task-action-item {
    flex: 1;
    width: 100%;

    .task-action-item-inner {
      margin: 2px 4px;
      display: flex;
      justify-content: space-between;
      .status {
        color: #158CBE;
      }
    }
  }
  
  .task-action-item:hover {
    cursor: pointer;
    background-color: white;
  }
}
</style> 