<template>
  <div class="navbar" :style="{ backgroundColor: variables.menuBg }">
    <Logo v-if="showLogo" style="width:200px;"/>
    <!--<div class="left-panel"> -->
      <el-scrollbar >
        
        <el-menu
          :default-active="activeMenu"
          
          :background-color="variables.menuBg"
          :text-color="variables.menuText"
          :unique-opened="false"
          :active-text-color="variables.menuActiveText"
          :collapse="false" 
          :collapse-transition="false"
          :mode="'horizontal'"
        
        >
          <SidebarItem v-for="route in sidebarRouters" :key="route.path" :item="route" :base-path="route.path" />
        </el-menu>
        
      </el-scrollbar>
    <!--</div>-->

    <div class="right-menu">
      <Notification />
      <LangSelect />
      <span class="span-username">{{ user.roles[0].name + ': ' + user.username }}</span>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <template #default>
          <div class="avatar-wrapper">
            <img :src="user.avatarName ? baseApi + '/avatar/' + user.avatarName : Avatar" class="user-avatar" />
            <el-icon><CaretBottom /></el-icon>
          </div>
        </template>
        <template #dropdown>
          <el-dropdown-menu>
            <router-link to="/user/center">
              <el-dropdown-item>{{ $t('sys.personalCenter') }}</el-dropdown-item>
            </router-link>
            
            <el-dropdown-item divided @click="openLogoutDialog">
              {{ $t('sys.logout') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { CaretBottom } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import Cookies from 'js-cookie';

import Logo from './Sidebar/Logo.vue';
import SidebarItem from './Sidebar/SidebarItem.vue';
import Hamburger from '@/components/Hamburger';
import AvatarImage from '@/assets/images/avatar.png';
import LangSelect from '@/components/LangSelect'
import Notification from './Notification.vue'
import * as variables from '@/assets/styles/variables.module.scss';


// Vuex store
const store = useStore();
const route = useRoute();

const showLanguagePopover = ref(false);
const Avatar = ref(AvatarImage);

 const langPopoverRef = ref(null)

// 计算属性
const sidebarRouters = computed(() => store.getters.sidebarRouters);
const sidebar = computed(() => store.getters.sidebar);
const user = computed(() => store.getters.user);
const baseApi = computed(() => store.getters.baseApi);
const isCollapse = computed(() => !sidebar.value.opened);
const showLogo = computed(() => store.state.settings.sidebarLogo);

// 计算当前菜单的激活路径
const activeMenu = computed(() => {
  const { meta, path } = route;
  return meta.activeMenu || path;
});

// 侧边栏切换
const toggleSideBar = () => {
  store.dispatch('app/toggleSideBar');
};

// 退出系统
const openLogoutDialog = () => {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    logout();
  });
};

// 执行注销
const logout = () => {
  store.dispatch('LogOut').then(() => {
    location.reload();
  });
};

// 语言切换
const changeLang = (locale) => {
  Cookies.set('language', locale);
  location.reload();
};
</script>

<style lang="scss" scoped >
.navbar {
  height: 50px;
  width: 100%;
  overflow: hidden;
  position: sticky;
  top:0;
  left:0;
  bottom: 0;
  z-index: 999;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;

  :deep(.el-scrollbar) {
    flex: 1;
    height: 50px;
    
  }
  :deep(.scrollbar__view) {
    overflow: hidden !important;
  }
  
  .left-panel {
    display: inline-block;

    flex:1;
    :deep(.sidebar-logo-container) {
      padding-left: 10px;
      padding-right: 10px;
      background-color: rgb(48, 65, 86);
    }
    .menu-container {
      display: flex;
      :deep(.svg-icon) {
        margin-right: 6px;
      }
    }
    
  }

  .right-menu {
    height: 100%;
    line-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;

    & > * {
      display: inline-block;
      min-width: 40px;
      color: var(--el-text-color);
      text-align: center;
      cursor: pointer;

      &:hover {
        background: rgb(0 0 0 / 10%);
      }
    }

    .span-username {
      color: #97a8be;
      margin-right: 10px;
      font-size: small;
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
        }

        .el-icon {
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }
  }
}
:deep(.language-popper) {
  // 解决z-index层级问题
  z-index: calc(var(--el-dropdown-menu-index) + 10) !important;
  
  // 调整位置偏移
  margin-left: -80px !important;
  
  // 适应dark主题
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  
  .el-dropdown-menu {
    // 清除默认边距
    margin: -6px -16px;
  }
}

:deep(.el-scrollbar__view){
  overflow:hidden;
}
</style>
