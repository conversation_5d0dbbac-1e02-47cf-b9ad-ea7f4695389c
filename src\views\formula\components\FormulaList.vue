<template>
  <div class="formula-list">
    <div class="function-search">
      <el-input
        v-model="searchTreeVariable"
        placeholder="搜索函数"
        clearable
      >
        <template #prefix>
          <el-icon><search /></el-icon>
        </template>
      </el-input>
    </div>
    <div class="treeContainer" @mouseleave="leaveTree">
      <el-tree
        :data="filterData"
        :props="treeProps"
        :highlight-current="true"
        :default-expanded-keys="['frequentlyUse']"
        :current-node-key="currentKeyNode?.enCode"
        node-key="enCode"
        @node-click="nodeClick"
      >
        <template #default="{ data }">
          <span 
            class="info-container" 
            @mouseenter="enterInfo(data)"
          >
            <span>{{ data.name }}</span>
            <span v-if="data.tip" class="tip">
              {{ data.tip }}
            </span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  nodes: {
    type: Array,
    default: () => []
  },
  treeProps: {
    type: Object,
    default: () => ({
      children: 'formula',
      label: 'name'
    })
  }
})

const emit = defineEmits(['formulaClick', 'enterInfo'])

// 响应式数据
const searchTreeVariable = ref('')
const currentKeyNode = ref({})

// 计算属性
const filterData = computed(() => {
  return searchTreeVariable.value 
    ? filterTreeData(props.nodes) 
    : props.nodes
})

// 方法
const nodeClick = (data) => {
  if (data.formula) {
    if (currentKeyNode.value?.enCode) {
      currentKeyNode.value = {}
    }
    return
  }
  currentKeyNode.value = data
  emit('formulaClick', data)
}

const filterTreeData = (nodes) => {
  return nodes.reduce((pre, cur) => {
    if (cur.name.toLowerCase().includes(searchTreeVariable.value.toLowerCase())) {
      pre.push({ ...cur })
    }
    if (cur.formula) {
      pre.push(...filterTreeData(cur.formula))
    }
    return pre
  }, [])
}

const enterInfo = (data) => {
  if (data.formula) return
  emit('enterInfo', data)
}

const leaveTree = () => {
  if (currentKeyNode.value?.enCode) {
    emit('enterInfo', currentKeyNode.value)
  }
}
</script>

<style lang="scss" scoped>
.formula-list {
  padding: 0 10px;
  height: 100%;
  border-right: 1px solid var(--el-border-color);
  overflow: auto;
  .info-container {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-tree-node__content) {
    height: 100%;
    padding: 6px 0;
    user-select: none;
  }

  .tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 1px;
    line-height: 1.2;
  }

  .function-search {
    padding-top: 10px;
  }
}
</style>
