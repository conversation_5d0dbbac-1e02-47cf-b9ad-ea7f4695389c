import request from '@/utils/request'


export function createTaskDebug(data) {
  return request({
    url: 'api/taskDebug/createTaskDebug',
    method: 'post',
    data
  })
}

export function loadTaskDebug(data) {
  return request({
    url: 'api/taskDebug/loadTaskDebug',
    method: 'post',
    data
  })
}

export function setDebugMode(data) {
  return request({
    url: 'api/taskDebug/setDebugMode',
    method: 'post',
    data
  })
}

export function setBreakPoint(data) {
  return request({
    url: 'api/taskDebug/setBreakPoint',
    method: 'post',
    data
  })
}

export function doRetryCommand(data) {
  return request({
    url: 'api/taskDebug/doRetryCommand',
    method: 'post',
    data
  })
}
export function doNextCommand(data) {
  return request({
    url: 'api/taskDebug/doNextCommand',
    method: 'post',
    data
  })
}
export function doSkipCommand(data) {
  return request({
    url: 'api/taskDebug/doSkipCommand',
    method: 'post',
    data
  })
}


export default {  createTaskDebug, loadTaskDebug, doRetryCommand, doNextCommand, doSkipCommand, setDebugMode, setBreakPoint }
