import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/formula',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/formula/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/formula',
    method: 'put',
    data
  })
}

export function selectVariablesAndFunctions(params) {
  return request({
    url: 'api/formula/selectVariablesAndFunctions',
    method: 'get',
    params
  })
}

export function queryForSelection(params) {
  return request({
    url: 'api/formula/queryForSelection',
    method: 'get',
    params
  })
}

export function queryByName(params) {
  return request({
    url: 'api/formula/queryByName',
    method: 'get',
    params
  })
}

export default { add, edit, del, selectVariablesAndFunctions, queryForSelection, queryByName }
