<template>
  <div class="button-wrapper">
    <el-dialog
      title="启动样本分析"
      align-center
      append-to-body
      v-model="startAnalyseDialogVisible"
      width="800px"
      body-class="fixed-dialog-body"
      draggable
    >
      <StarterAsBoardTabs v-if="taskStarterMode==='BOARD'" @onTaskSubmitted="onTaskSubmittedInStarterCtrl" />
      <StarterAsDefault v-else-if="taskStarterMode==='DEFAULT'"/>
      <el-tabs v-else v-model="activeMode" class="tab-starter" @tab-click="handleTabClick">
        <el-tab-pane label="试验模式" name="TRIAL">
          <TrialStarter />
        </el-tab-pane>
        <el-tab-pane label="工厂模式" name="FACTORY">
          <FactoryStarter />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <div :style="backgroundStyle" class="background-container" @click="startAnalyse">
      <div :style="buttonIconStyle" class="button-icon" />
    </div>
    <div class="status-text"><span>启动分析</span></div>
  </div>

</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElDialog, ElTabs, ElTabPane } from 'element-plus'
import TrialStarter from '@/views/taskprepare/TrialStarter.vue'
import FactoryStarter from '@/views/taskprepare/FactoryStarter.vue'
import StarterAsBoardTabs from '@/views/taskprepare/components/StarterAsBoardTabs.vue'
import StarterAsDefault from '@/views/taskprepare/components/StarterAsDefault.vue'

const store = useStore()

// 响应式状态
const taskStarterMode = ref('BOARD')
const activeMode = ref('TRIAL')
const startAnalyseDialogVisible = ref(false)
const sampleOptions = ref([])
const procedureOptions = ref([{ label: '新建流程', value: 0 }])

// 计算属性
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)
const backgroundStyle = computed(() => ({
  backgroundImage: `url(${import('@/assets/icons/svg/status-btn-bg.svg')})`
}))
const buttonIconStyle = computed(() => ({
  backgroundImage: `url(${imageAccessUrl.value}%E5%90%AF%E5%8A%A8%E5%88%86%E6%9E%90-20240620031632806.svg)`
}))

const onTaskSubmittedInStarterCtrl = () => {
  startAnalyseDialogVisible.value = false
}

// 生命周期
onMounted(async () => {
  console.log('analyseStart activeMode:', store.state.uiSetting?.analyseStart?.activeMode)
  activeMode.value = store.state.uiSetting?.analyseStart?.activeMode || 'TRIAL'

  // 加载系统设置
  await store.dispatch('setGlobalSettingLatestOne')
  taskStarterMode.value = store.state.globalSetting.globalSetting.taskStarterMode

})

// 方法
const handleTabClick = async (tab) => {
  debugger
  if (store.state.uiSetting?.analyseStart?.activeMode !== activeMode.value) {
    await store.dispatch('saveUiSetting', { 
      analyseStart: { activeMode: activeMode.value }
    })
  }
}

const startAnalyse = () => {
  startAnalyseDialogVisible.value = true
}
</script>

<style lang="scss" scoped>

:deep(.fixed-dialog-body) {
    height: 600px;
  }

  .button-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center; /* 水平居中 */
    gap: 10px; /* 按钮和文本之间的间距 */

    :deep( .el-dialog__body ) {
      padding:10px 20px;
      height: 400px;
    }
  }

  .background-container {
    width: 60px; /* 宽度 */
    height: 60px; /* 高度 */
    border-radius: 50%; /* 圆形 */
    background-size: cover; /* 背景图片适应按钮 */
    background-position: center; /* 背景图片居中 */
    background-image:none;
    border: 2px solid #59AED2; /* 边框颜色和样式 */
    display: flex;
    align-items: center; /* 内部内容垂直居中 */
    justify-content: center; /* 内部内容水平居中 */
    cursor: pointer; /* 鼠标移到按钮上变为手指 */
  }
  .background-container:hover {
    background-image: radial-gradient(circle at bottom, #FFFFFF,#399AC3 75%, #0580B4 125%,);
  }

  .button-icon {
    width: 40px;
    height: 40px;
    background-size: contain;
  }

  .status-text {
    font-size: 16px;
    font-weight: bold;
    color:#4f96c5;
  }
  .img{
    width:40px;
    height:40px;
  }

</style>
