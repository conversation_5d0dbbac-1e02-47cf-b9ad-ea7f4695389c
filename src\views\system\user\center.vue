<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="5" style="margin-bottom: 10px">
        <el-card class="box-card">
          <template #header>
            <div class="clearfix">
              <span>个人信息</span>
            </div>
          </template>
          <div>
            <div style="text-align: center">
              <div class="el-upload">
                <img 
                  :src="user.avatarName ? baseApi + '/avatar/' + user.avatarName : Avatar" 
                  title="点击上传头像" 
                  class="avatar" 
                  @click="openUploadDialog"
                >
                
                <!-- 替换原来的 myUpload 组件为新的上传对话框 -->
                <el-dialog v-model="dialogVisible" title="更换头像" width="600px">
                  <el-upload
                    class="avatar-uploader"
                    :show-file-list="false"
                    :auto-upload="false"
                    :on-change="handleFileChange"
                    accept="image/*"
                  >
                    <img v-if="tempImageUrl" :src="tempImageUrl" class="temp-avatar" />
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                  </el-upload>

                  <div v-if="tempImageUrl" class="cropper-container">
                    <el-image-crop
                      ref="cropRef"
                      :src="tempImageUrl"
                      :rotate="true"
                      :zoom="true"
                      :aspect-ratio="1"
                    />
                  </div>

                  <template #footer>
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" :loading="uploading" @click="uploadAvatar">确认上传</el-button>
                  </template>
                </el-dialog>
              </div>
            </div>
            <ul class="user-info">
              <li><div style="height: 100%"><svg-icon icon-class="login" /> 登录账号<div class="user-right">{{ user.username }}</div></div></li>
              <li><svg-icon icon-class="user1" /> 用户昵称 <div class="user-right">{{ user.nickName }}</div></li>
              <li><svg-icon icon-class="dept" /> 所属部门 <div class="user-right"> {{ user.dept.name }}</div></li>
              <li><svg-icon icon-class="phone" /> 手机号码 <div class="user-right">{{ user.phone }}</div></li>
              <li><svg-icon icon-class="email" /> 用户邮箱 <div class="user-right">{{ user.email }}</div></li>
              <li>
                <svg-icon icon-class="anq" /> 安全设置
                <div class="user-right">
                  <a @click="passRef.dialog = true">修改密码</a>
                  <a @click="emailRef.dialog = true">修改邮箱</a>
                </div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="16" :lg="18" :xl="19">
        <el-card class="box-card">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="用户资料" name="first">
              <el-form ref="formRef" :model="form" :rules="rules" style="margin-top: 10px;" size="small" label-width="65px">
                <el-form-item label="昵称" prop="nickName">
                  <el-input v-model="form.nickName" style="width: 35%" />
                  <span style="color: #C0C0C0;margin-left: 10px;">用户昵称不作为登录使用</span>
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="form.phone" style="width: 35%;" />
                  <span style="color: #C0C0C0;margin-left: 10px;">手机号码不能重复</span>
                </el-form-item>
                <el-form-item label="性别">
                  <el-radio-group v-model="form.gender" style="width: 178px">
                    <el-radio label="男">男</el-radio>
                    <el-radio label="女">女</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="">
                  <el-button :loading="saveLoading" size="small" type="primary" @click="doSubmit">保存配置</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="操作日志" name="second">
              <el-table v-loading="loading" :data="data" style="width: 100%;">
                <el-table-column prop="description" label="行为" />
                <el-table-column prop="requestIp" label="IP" />
                <el-table-column :show-overflow-tooltip="true" prop="address" label="IP来源" />
                <el-table-column prop="browser" label="浏览器" />
                <el-table-column prop="time" label="请求耗时" align="center">
                  <template #default="{ row }">
                    <el-tag v-if="row.time <= 300">{{ row.time }}ms</el-tag>
                    <el-tag v-else-if="row.time <= 1000" type="warning">{{ row.time }}ms</el-tag>
                    <el-tag v-else type="danger">{{ row.time }}ms</el-tag>
                  </template>
                </el-table-column>
                <el-table-column align="right">
                  <template #header>
                    <div style="display:inline-block; float: right; cursor: pointer" @click="init">
                      创建日期<el-icon class="el-icon--right" />
                    </div>
                  </template>
                  <template #default="{ row }">
                    <span>{{ row.createTime }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                v-model:current-page="page"
                v-model:page-size="size"
                :total="total"
                style="margin-top: 8px;"
                layout="total, prev, pager, next, sizes"
                @size-change="sizeChange"
                @current-change="pageChange"
              />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
    <update-email ref="emailRef" :email="user.email" />
    <update-pass ref="passRef" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, provide } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useStore } from 'vuex'
import myUpload from 'vue-image-crop-upload'
import updatePass from './center/updatePass.vue'
import updateEmail from './center/updateEmail.vue'
import { getToken } from '@/utils/auth'
import { isvalidPhone } from '@/utils/validate'
import { useCrud } from '@/hooks/useCrud'
import { editUser } from '@/api/system/user'
import Avatar from '@/assets/images/avatar.png'
//import { User, Office, Phone, Message, Lock, Refresh } from '@element-plus/icons-vue'

// refs
const formRef = ref(null)
const emailRef = ref(null)
const passRef = ref(null)

// store
const store = useStore()
const user = computed(() => store.getters.user)
const updateAvatarApi = computed(() => store.getters.updateAvatarApi)
const baseApi = computed(() => store.getters.baseApi)

// data
const show = ref(false)
const activeName = ref('first')
const saveLoading = ref(false)
const headers = reactive({
  'Authorization': getToken()
})

// form
const form = reactive({
  id: user.value.id,
  nickName: user.value.nickName,
  gender: user.value.gender,
  phone: user.value.phone
})

// validation
const validPhone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入电话号码'))
  } else if (!isvalidPhone(value)) {
    callback(new Error('请输入正确的11位手机号码'))
  } else {
    callback()
  }
}

const rules = {
  nickName: [
    { required: true, message: '请输入用户昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, trigger: 'blur', validator: validPhone }
  ]
}


// 替换原来的 show ref
const dialogVisible = ref(false)
const tempImageUrl = ref('')
const cropRef = ref(null)
const uploading = ref(false)

// 新增的方法
const openUploadDialog = () => {
  dialogVisible.value = true
  tempImageUrl.value = ''
}

const handleFileChange = (file) => {
  const isImage = file.raw.type.startsWith('image/')
  const isLt2M = file.raw.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('请上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }

  const reader = new FileReader()
  reader.readAsDataURL(file.raw)
  reader.onload = (e) => {
    tempImageUrl.value = e.target.result
  }
}

const uploadAvatar = async () => {
  if (!cropRef.value || !tempImageUrl.value) {
    ElMessage.warning('请先选择图片')
    return
  }

  try {
    uploading.value = true
    // 获取裁剪后的图片 blob 数据
    const canvas = await cropRef.value.getCroppedCanvas()
    const blob = await new Promise(resolve => canvas.toBlob(resolve))
    
    // 构建 FormData
    const formData = new FormData()
    formData.append('avatar', blob, 'avatar.png')

    // 发送请求
    const response = await fetch(updateAvatarApi.value, {
      method: 'POST',
      headers: headers,
      body: formData
    })

    if (response.ok) {
      ElMessage.success('头像更新成功')
      await store.dispatch('GetInfo')
      dialogVisible.value = false
    } else {
      throw new Error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('头像上传失败')
  } finally {
    uploading.value = false
  }
}

// crud mixin equivalent
const loading = ref(false)
const data = ref([])
const page = ref(1)
const size = ref(20)
const total = ref(0)

// 使用 crud hook
const { crud, query } = useCrud({
  title: '操作日志',
  url: 'api/logs/user',
  sort: 'createTime,desc',
  query: {
    page: 1,
    size: 20,
    blurry: null,
    createTime: null
  }
})
provide('crud', crud)

// methods
const toggleShow = () => {
  show.value = !show.value
}

const handleClick = (tab) => {
  if (tab.props.name === 'second') {
    init()
  }
}

const beforeInit = () => {
  const url = 'api/logs/user'
  return true
}

const init = async () => {
  // Implement your init logic here
}

const cropUploadSuccess = async () => {
  await store.dispatch('GetInfo')
}

const doSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saveLoading.value = true
    await editUser(form)
    ElMessage.success('保存成功')
    await store.dispatch('GetInfo')
  } catch (error) {
    console.error(error)
  } finally {
    saveLoading.value = false
  }
}

const sizeChange = (val) => {
  size.value = val
  init()
}

const pageChange = (val) => {
  page.value = val
  init()
}

// lifecycle
onMounted(async () => {
  await store.dispatch('GetInfo')
})
</script>

<style lang="scss" scoped>
.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
}

.user-info {
  padding-left: 0;
  list-style: none;
  li {
    border-bottom: 1px solid #F0F3F4;
    padding: 11px 0;
    font-size: 13px;
  }
  .user-right {
    float: right;
    a {
      color: #317EF3;
    }
  }
}

.avatar-uploader {
  text-align: center;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  
  &:hover {
    border-color: var(--el-color-primary);
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.temp-avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.cropper-container {
  margin-top: 20px;
  height: 400px;
}
</style>
