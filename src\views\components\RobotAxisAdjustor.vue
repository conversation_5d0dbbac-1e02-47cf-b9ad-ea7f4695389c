<template>

  <div class="robot-axis-container">

    <div class="robot-axis-config-wrapper">
      <div class="left">
        <span>步长</span> <el-input-number v-model="motion.stepSize" size="mini" style="width:80px" :precision="2" :step="0.01" :max="500" controls-position="right" /><span> mm</span>
      </div>
      <div class="right">
        <span>速度</span> <el-input-number v-model="motion.speed" size="mini" style="width:80px" :precision="2" :step="0.01" :max="500" controls-position="right" /><span> mm/s</span>
      </div>
    </div>

    <div class="robot-status-wrapper">
      <div class="left">当前轴位置</div>
      <div class="right" style="width:200px">
        <div class="axis-container" :style="'background-image:url('+ imageAccessUrl +'%E5%9D%90%E6%A0%87_%E5%9D%90%E6%A0%87%E7%B3%BB-20240621125030697.svg' +');'">
          <span class="z-axis">{{ motion.xpos }}</span>
          <span class="y-axis">{{ motion.ypos }}</span>
          <span class="x-axis">{{ motion.zpos }}</span>
        </div>
      </div>
    </div>
    <div class="robot-axis-main-bg" />
    <div class="robot-axis-main-wrapper">
      <span class="axis-x-left-label">-X: {{ motion.stepSize }}</span><el-button type="primary" icon="el-icon-back" circle class="axis-x-left axis-button" />
      <span class="axis-x-right-label">+X: {{ motion.stepSize }}</span><el-button type="primary" icon="el-icon-right" circle class="axis-x-right axis-button" />
      <span class="axis-y-up-label">+Y: {{ motion.stepSize }}</span><el-button type="primary" icon="el-icon-top" circle class="axis-y-up axis-button" />
      <span class="axis-y-down-label">-Y: {{ motion.stepSize }}</span><el-button type="primary" icon="el-icon-bottom" circle class="axis-y-down axis-button" />
      <span class="axis-z-up-label">+Z: {{ motion.stepSize }}</span><el-button type="primary" icon="el-icon-top-right" circle class="axis-z-up axis-button" />
      <span class="axis-z-down-label">-Z: {{ motion.stepSize }}</span><el-button type="primary" icon="el-icon-bottom-left" circle class="axis-z-down axis-button" />
      <el-button type="primary" icon="el-icon-aim" circle class="axis-datum axis-button" />
    </div>

    <div class="robot-axis-footer-wrapper">
      <div class="left" />

      <div class="right"> <el-button type="normal">移动至配置点位</el-button> <el-button type="primary">变更点位轴参数</el-button></div>

    </div>
  </div>

</template>

<script>
import { mapGetters } from 'vuex'

import crudMotion from '@/api/motion'

export default {

  data() {
    return {
      chart: null,
      robotSpeed: 50//,
      // motion: { status: 'IDLE', speed: 0.00, speedFactor: 0.00, xpos: 0.00, ypos: 0.00, zpos: 0.00, stepSize: 0.01 }
    }
  },
  watch: {

  },
  computed: {
    ...mapGetters([
      'imagesUploadApi',
      'imageAccessUrl',
      'baseApi',
      'motion',
      'task'
    ])
  },
  mounted() {
    this.timer = setInterval(this.autoUpdateMotionInfo, 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    autoUpdateMotionInfo() {
      const _this = this
      this.$store.dispatch('getCurrentMotionInfo').then(() => {
        console.log('_this.task.started=' + _this.task.started)
        if (_this.motion.doorStatus !== 0 && _this.task.started) {
          debugger
          _this.$store.dispatch('setStarted', false)
        }
      })
    },
    formatSpeedTooltip(val) {
      return '速度值(mm/sec):' + this.motion.speed + ', 速度控制因子:' + this.motion.speedFactor + '/100'
    },
    changeSpeed(val) {
      const _this = this
      crudMotion.changeSpeed(this.motion).then(res => {
        _this.transferData(res)
      }).catch((err) => {
        console.log('changeSpeed error: ' + err)
      })
    },
    transferData(res) {
      res.xpos = parseFloat(res.xpos).toFixed(2)
      res.ypos = parseFloat(res.ypos).toFixed(2)
      res.zpos = parseFloat(res.zpos).toFixed(2)
      this.motion = res
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .robot-axis-container {
    padding: 20px;
  }

  .robot-axis-main-bg {
    background-color: antiquewhite;
    width: 170px;
    height: 170px;
    position: absolute;
    left: 110px;
    top: 120px;
    transform: rotateZ(45deg);
    border-radius: 20px;
  }
  .robot-axis-main-wrapper {
    width: 100%;
    height: 200px;
    position: relative;
    top: -40px;
   // background-color: antiquewhite;
    .axis-button {
      font-size: 20px;
      font-weight: bold;
    }
    .axis-x-left {
      position: absolute;
      top: 90px;
      left: 70px;
    }
    .axis-x-left-label {
      position: absolute;
      top: 100px;
      left: 4px;
    }

    .axis-x-right {
      position: absolute;
      top: 90px;
      left: 214px;
    }
    .axis-x-right-label {
      position: absolute;
      top: 100px;
      left: 260px;
    }

    .axis-y-up {
      position: absolute;
      top: 20px;
      left: 140px;
    }
    .axis-y-up-label {
      position: absolute;
      top: 0px;
      left: 160px;
    }

    .axis-y-down {
      position: absolute;
      top: 160px;
      left: 140px;
    }
    .axis-y-down-label {
      position: absolute;
      top: 200px;
      left: 114px;
    }

    .axis-z-up {
      position: absolute;
      top: 56px;
      left: 180px;
    }
    .axis-z-up-label {
      position: absolute;
      top: 50px;
      left: 220px;
    }

    .axis-z-down {
      position: absolute;
      top: 128px;
      left: 104px;
    }
    .axis-z-down-label {
      position: absolute;
      top: 160px;
      left: 54px;
    }

    .axis-datum {
      position: absolute;
      top: 90px;
      left: 140px;
      margin-left: 0px;
    }

  }

  .robot-axis-config-wrapper {
    display: flex;
    width: 100%;
    .right{
      margin-left: 20px;
    }
    >>> .el-input__inner {
      padding-left: 4px;
      padding-right: 38px;
    }
  }

  .axis-container {
    margin-left: 20px;
    width:200px;
    height:60px;
    background-size: 40px 40px; /* 背景图片适应按钮 */
    background-repeat: no-repeat;
      background-position: left; /* 背景图片居中 */
      background-image: url('http://localhost:8000/file/%E5%85%B6%E4%BB%96/%E5%9D%90%E6%A0%87_%E5%9D%90%E6%A0%87%E7%B3%BB-20240621125030697.svg');
      display: flex;
      flex-direction: column;
      .z-axis {
        padding-left: 26px;
      }
      .y-axis {
        padding-left: 50px;
        padding-top: 6px;
      }
      .x-axis {
        padding-left: 6px;
        padding-top: 10px;
      }
  }

  .robot-axis-footer-wrapper {
    display: flex;
    justify-content: right;
  }

</style>
