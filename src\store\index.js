import {createStore} from 'vuex'
import getters from './getters'

// 使用 import.meta.glob 动态导入模块
const modulesFiles = import.meta.glob('./modules/*.js', { eager: true });

const modules = Object.keys(modulesFiles).reduce((modules, modulePath) => {
  // 获取模块名称，例如 './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/modules\/(.*)\.\w+$/, '$1');
  modules[moduleName] = modulesFiles[modulePath].default;
  return modules;
}, {});

const store = createStore({
  modules,
  getters
})

export default store
