<template>
  <div class="main-form-container">
    <el-steps class="mb-4 custom-steps" :space="200" :active="form.currentActiveStepIndex" finish-status="success" simple>
      <el-step title="录入样品" :icon="Document" @click="changeStep(0)" :class="{cusor:pointer}"/>
      <el-step title="提交检测任务" :icon="VideoPlay" @click="changeStep(1)" />
      <el-step title="完成状态" :icon="Bell" @click="changeStep(2)" />
    </el-steps>

    <StepSampleEditorForPrinter v-if="form.currentActiveStepIndex === 0" v-model:containerForm="form"
      @on-success="onSampleEditSuccess" />
    <StepBatchSubmitTask v-if="form.currentActiveStepIndex === 1" v-model:containerForm="form"
      v-bind="batchSubmitLoading" @on-success="getCurrentTaskPrepareView" />
    <StepTaskStatus v-if="form.currentActiveStepIndex === 2" v-model:containerForm="form" />
  </div>
</template>
<script setup>
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, version, defineModel,defineEmits } from 'vue'
import { useStore } from 'vuex'
import { debounce } from '@/utils'
import { useCrud } from '@/hooks/useCrud'
import crudTaskPrepare from '@/api/taskPrepare'
import { queryProceduresForSelection } from '@/api/procedure'
import { querySamplesForSelection } from '@/api/sample'
import { ElMessage, ElNotification as Notification } from 'element-plus'
import { Delete, VideoPlay, Document, Bell } from '@element-plus/icons-vue'

import StepSampleEditorForPrinter from './StepSampleEditorForPrinter.vue'
import StepBatchSubmitTask from './StepBatchSubmitTask.vue'
import StepTaskStatus from './StepTaskStatus.vue'
import pagination from '@/components/crud/Pagination'
const emit = defineEmits(['on-success'])

// CRUD配置
const { crud, CRUD } = useCrud({
  title: '任务启动准备',
  url: 'api/taskPrepare/queryTaskPrepareForConveyor',
  idField: 'id',
  sort: 'id,desc',
  queryOnPresenterCreated: true,
  crudMethod: { ...crudTaskPrepare }
})

// 组件引用
const store = useStore()

const currentActiveStepIndex = ref(0)

const form = reactive({
  currentActiveStepIndex: 0,
  procedure: null,
  rfTag: null
}) 


const tableTaskPrepare = ref(null)
const formRef = ref(null)

// 响应式状态
const sampleEditorVisible = ref(false)
const procedureOptions = ref([{ label: '新建流程', value: 0 }])
const sampleOptions = ref([])
const message = ref(' ')
const messageClass = ref('')
const inputDisabled = ref(false)
const batchSubmitLoading = ref(false)
const taskPrepareView = ref({})
const currentDeletingRow = ref({})
const taskPrepareLoading = ref(false)

const sampleEditorSelected = ref('printer');

const changeStep = (step) => {
  form.currentActiveStepIndex = step
}

// 生命周期
onMounted(async () => {
  sampleOptions.value = await querySamplesForSelection({ name: null })
  procedureOptions.value = await queryProceduresForSelection()
  getCurrentTaskPrepareView()
})

// 方法
const isRowSelectable = (row) => !row.inQueue

const handleAnalyseStart = () => {
   doSubmitFactory()
}

const doSubmitFactory = async () => {
  if (crud.selections.length === 0) {
    ElMessage.error('请选择待进样样本标签')
    return
  }
  
  batchSubmitLoading.value = true
  inputDisabled.value = true
  taskPrepareView.value.taskPrepareDtos = crud.selections
  
  try {
    //await crudTaskPrepare.submitEntryConveyorAcceptingStep(taskPrepareView.value)
    await crudTaskPrepare.submitBatchTasks(taskPrepareView.value)
    
    setTimeout(getCurrentTaskPrepareView, 100)
  } finally {
    batchSubmitLoading.value = false
  }
}

const openSampleEditor = () => {
  debugger
  sampleEditorVisible.value = true
}

const onSampleEditSuccess = (form) => {
  sampleEditorVisible.value = false;
  crud.refresh();
};

const addTaskPrepare = (taskprepares) => {
  const defaultProc = procedureOptions.value[0] || { id: null, name: null }
  const newLine = { procedure: defaultProc, rfTag: null, status: 'DRAFT', selected: true }
  crud.data.push(newLine)
  tableTaskPrepare.value.setCurrentRow(newLine)
}

const handleCurrentChange = (currentRow, oldCurrentRow) => {
  // currentRow.selected = true
  // oldCurrentRow.selected = false
}

const deleteTaskPrepare = (curTaskPrepare) => {
  crud.toDelete(curTaskPrepare)
}

const cancelAnalyse = () => {
  message.value = ''
  inputDisabled.value = false
  taskPrepareView.value = {}
  crudTaskPrepare.cancelCurrentTaskPrepareView({ id: taskPrepareView.value.id }).then(res => {
    ElMessage.success('撤销任务成功')
    console.log(new Date() + ': TaskPrepare was cancelled done.')
    getCurrentTaskPrepareView()
  }).catch((error) => {
    console.log('doSubmitTrial error=' + error)
    ElMessage.error('撤销任务失败')
  })
}

const onSampleSelect = (keyword) => {
  sampleOptions.value = []
  sampleLoading.value = true
  debounce(() => {
    querySamplesForSelection({ name: keyword }).then(res => {
      sampleLoading.value = false
      sampleOptions.value = res
    })
  }, 200)()
}

// 获取任务准备状态标签类型
const getTaskPrepareStatusTagType = (taskPrepare) => {
  switch (taskPrepare.status) {
    case 'PENDING':
      return 'warning';
    case 'COMPLETED':
      return 'success';
    default:
      return 'info';
  }
};
const getCurrentTaskPrepareView = async (param) => {
  try {
    taskPrepareView.value = await crudTaskPrepare.getCurrentTaskPrepareView({ mode: 'FACTORY' })
    updateTaskPrepare()
    let finishedCount = 0
    for (const tp of taskPrepareView.value.taskPrepareDtos) {
      if (tp.status === 'PUSHED' || tp.status === 'SUCCESS' || tp.status === 'FAILED') {
        finishedCount++
      }
    }

    if (finishedCount !== crud.finishedCount) {
      crud.finishedCount = finishedCount
      crud.refresh()
    }

    if (taskPrepareView.value.status === 'DRAFT') {
      message.value = ''
    } else {
      message.value = '进样完成进度：' + finishedCount + '/' + taskPrepareView.value.taskPrepareDtos.length
    }

    if (finishedCount === taskPrepareView.value.taskPrepareDtos.length) {
      batchSubmitLoading.value = false
      inputDisabled.value = false
    } else {
      inputDisabled.value = true
      setTimeout(() => {
        getCurrentTaskPrepareView(param)
      }, 500)
    }
  } catch (error) {
    console.log('getCurrentTaskPrepareView error=' + error)
    ElMessage.error('获取任务准备视图失败')
  }
}

const updateTaskPrepare = () => {
  if (taskPrepareView.value.taskPrepareDtos && taskPrepareView.value.taskPrepareDtos.length > 0) {
    for (const respTpDto of taskPrepareView.value.taskPrepareDtos) {
      respTpDto.inQueue = true
      for (var i in crud.data) {
        const row = crud.data[i]
        if (row.id === respTpDto.id && (row.status !== respTpDto.status || !row.inQueue)) {
          crud.data.splice(i, 1, respTpDto)
        }
      }
    }
  }
}

// BEGIN: 处理删除操作
const doCancel = (row) => {
  row.deletePopVisible = false
  //crud.cancelDelete(this.data)
}

const toDelete = (row) => {
  currentDeletingRow.value = row
  for (let i = 0; i < crud.data.length; i++) {
    if (row.id === crud.data[i].id) {
      crud.data[i] = { ...crud.data[i], deletePopVisible: true }
      break
    }
  }
}

const onPopoverShow = (row) => {
  currentDeletingRow.value = row
  setTimeout(() => {
    document.addEventListener('click', handleDocumentClick)
  }, 0)
}

const onPopoverHide = (row) => {
  currentDeletingRow.value = row
  document.removeEventListener('click', handleDocumentClick)
}

const handleDocumentClick = (event) => {
  // currentDeletingRow.value.deletePopVisible = false
}
// END: 处理删除操作

// 添加watch来监听对话框关闭
watch(() => sampleEditorVisible.value, (newVal) => {
  console.log('Vue version:', version)
  debugger
  if (typeof defineModel === 'undefined') {
    console.warn('当前 Vue 版本不支持 defineModel，需要 Vue 3.4+ 版本')
  }

})

watch(() => form.currentActiveStepIndex, (newVal) => {
  console.log('currentActiveStepIndex changed:', newVal)
  if (newVal === 2) {
    emit('on-success')
  }
})

</script>

<style lang="scss" scoped>
.main-form-container {
  height:400px;
}
.main-form-body {
  text-align: left;
}
.message-container {
  width: 100%;
  padding-left: 80px;
}
.main-form-footer {
  margin-top: 10px;
  padding-left: 80px;
  display: flex;
  .btn-container {
    display: flex;
  }
}
/* 添加自定义步骤样式 */
.custom-steps {
  :deep(.el-step__title:hover) {
    cursor: pointer;
  }
  
  :deep(.el-step__head:hover) {
    cursor: pointer;
  }
  
}

</style>
