<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryForm" :model="query" :inline="true" size="default">
          <el-form-item label="检测方法">
            <el-select
                v-model="query.methodId"
                value-key="id"
                filterable
                default-first-option
                placeholder="请选择"
              >
                <el-option
                  v-for="item in methodOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
            </el-select>
          </el-form-item>
          <el-form-item label="检测名称">
            <el-input
              v-model="query.name"
              clearable
              placeholder="检测名称"
              style="width: 185px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item>
            <rr-operation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
    <crud-operation :permission="permission" :crud="crud"/>
    <!--表单组件-->
    <el-dialog
      align-center
      append-to-body
      v-model="crud.dialogVisible"
      :close-on-click-modal="false"
      :before-close="crud.cancelCU"
      :title="crud.status.title"
      width="600px"
    >
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="120px">
        <el-form-item label="检测方法">
          <el-select
              v-model="form.methodId"
              value-key="id"
              filterable
              default-first-option
              placeholder="请选择"
            >
              <el-option
                v-for="item in methodOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="检测项编码" prop="code">
          <el-input v-model="form.code" />
        </el-form-item>
        <el-form-item label="检测项名称">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="仪器对应字段名">
          <el-input v-model="form.instrumentColumnName"  />
        </el-form-item>
        <el-form-item label="检测项描述">
          <el-input v-model="form.description"  />
        </el-form-item>
        <el-form-item label="数值单位">
          <el-input v-model="form.unit" />
        </el-form-item>
        <el-form-item label="结论判定规则">
          <template #label>
            <div class="header-with-tooltip">
                <span>结论判定规则</span>
                <el-tooltip class="header-tooltip" effect="dark" content="当该设置的条件满足或者不设置该规则时，判定结论为正常，否则判定结论为异常"
                  placement="top">
                  <el-icon class="question-icon">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
          </template>
          <ConditionEditor v-model="form.checkRules"  />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </template>
    </el-dialog>
    <!--表格渲染-->
    <el-table
      ref="tableRef"
      v-loading="crud.loading"
      :data="crud.data"
      size="small"
      style="width: 100%;"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="methodId" label="检测方法" >
        <template #default="{ row }">
          {{ getMethodName(row.methodId) }}
        </template>
      </el-table-column>
      <el-table-column prop="code" label="检测项编码" />
      <el-table-column prop="name" label="检测项名称" />
      <el-table-column prop="instrumentColumnName" label="仪器对应字段名" />
      <el-table-column prop="description" label="检测项描述" />
      <el-table-column prop="unit" label="数值单位" />
      <el-table-column prop="checkRules" label="结论判定规则集" />
      <el-table-column prop="createBy" label="创建人" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="updateBy" label="更新人" />
      <el-table-column prop="updateTime" label="更新时间" />
      <el-table-column v-if="checkPer(['admin','featureConfig:edit','featureConfig:del'])" label="操作" width="150px" align="center">
        <template #default="scope">
          <ud-operation
            :data="scope.row"
            :permission="permission"
            :crud="crud"
          />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination :crud="crud"/>
  
  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { InfoFilled } from '@element-plus/icons-vue'

import crudFeatureConfig from '@/api/featureConfig'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import udOperation from '@/components/crud/UD.operation.vue'
import pagination from '@/components/crud/Pagination'

import ConditionEditor from './components/ConditionEditor.vue'

import { queryAllMethods } from '@/api/procedure'

// 定义组件名称
defineOptions({
  name: 'FeatureConfig'
})

// 初始化表单数据
const defaultForm = {
  id: null,
  methodId: null,
  code: null,
  name: null,
  instrumentColumnName: null,
  description: null,
  unit: null,
  checkRules: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

// 表单验证规则
const rules = reactive({
  code: [
    { required: true, message: '特性编码不能为空', trigger: 'blur' }
  ]
})

// 权限配置
const permission = {
  add: ['admin', 'featureConfig:add'],
  edit: ['admin', 'featureConfig:edit'],
  del: ['admin', 'featureConfig:del']
}

// refs
const formRef = ref(null)
const tableRef = ref(null)

// 权限校验
const checkPer = inject('checkPer')

const methodOptions = ref([]);

// 使用CRUD钩子
const { crud, CRUD, form, query } = useCrud({
  title: '检测特性配置',
  url: 'api/featureConfig',
  crudMethod: { ...crudFeatureConfig },
  defaultForm,
  rules,
  formRef,
  tableRef
})

const getMethodName = (methodId) => {
  const method = methodOptions.value.find(item => item.id === methodId)
  return method ? method.name : ''
}

const initMethodOptions = async () => {
  methodOptions.value = await queryAllMethods()
}

// 生命周期
onMounted(() => {
  initMethodOptions()
  crud.refresh()

})
</script>

<style scoped>

</style>
