import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/statsResultFeature',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/statsResultFeature/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/statsResultFeature',
    method: 'put',
    data
  })
}

export function queryStatsResultFeatureValues(params) {
  return request({
    url: 'api/statsResultFeature/queryStatsResultFeatureValues',
    method: 'get',
    params
  })
}

export default { add, edit, del, queryStatsResultFeatureValues }
