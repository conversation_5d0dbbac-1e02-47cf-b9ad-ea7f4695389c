import request from '@/utils/request'
import qs from 'qs'

export function add(data) {
  return request({
    url: 'api/statsResultFeature',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/statsResultFeature/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/statsResultFeature',
    method: 'put',
    data
  })
}

export function queryStatsResultFeatureValues(params) {
  return request({
    url: 'api/statsResultFeature/queryStatsResultFeatureValues',
    method: 'get',
    params
  })
}

export function queryStatsResultFeatureValuesByHour(params) {
  return request({
    url: 'api/statsResultFeature/queryStatsResultFeatureValuesByHour?'+ qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function queryStatsMethods(params) {
  return request({
    url: 'api/statsResultFeature/queryStatsMethods?'+ qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function queryStatsConclusions(params) {
  return request({
    url: 'api/statsResultFeature/queryStatsConclusions?'+ qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function queryStatsDateTime(params) {
  return request({
    url: 'api/statsResultFeature/queryStatsDateTime?'+ qs.stringify(params, { indices: false }),
    method: 'get'
  })
}


export default { add, edit, del, queryStatsResultFeatureValues, 
  queryStatsResultFeatureValuesByHour,queryStatsMethods,queryStatsConclusions,queryStatsDateTime }
