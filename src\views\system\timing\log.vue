<template>
  <el-dialog v-model="dialogVisible" append-to-body title="执行日志" width="88%">
    <!-- 搜索 -->
    <div class="head-container">
      <el-input
        v-model="crud.query.jobName"
        clearable
        size="small"
        placeholder="输入任务名称搜索"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="crud.toQuery"
      />
      <DateRangePicker v-model="crud.query.createTime" class="date-item" />
      <el-select
        v-model="crud.query.isSuccess"
        placeholder="日志状态"
        clearable
        size="small"
        class="filter-item"
        style="width: 110px"
        @change="crud.toQuery"
      >
        <el-option
          v-for="item in enabledTypeOptions"
          :key="item.key"
          :label="item.display_name"
          :value="item.key"
        />
      </el-select>
      <el-button
        class="filter-item"
        size="small"
        type="success"
        :icon="Search"
        @click="crud.toQuery"
      >
        搜索
      </el-button>
      <!-- 导出 -->
      <div style="display: inline-block;">
        <el-button
          :loading="crud.downloadLoading"
          size="small"
          class="filter-item"
          type="warning"
          :icon="Download"
          @click="handleDownload"
        >
          导出
        </el-button>
      </div>
    </div>

    <!--表格渲染-->
    <el-table
      v-loading="crud.loading"
      :data="crud.data"
      style="width: 100%;margin-top: -10px;"
    >
      <el-table-column :show-overflow-tooltip="true" prop="jobName" label="任务名称" />
      <el-table-column :show-overflow-tooltip="true" prop="beanName" label="Bean名称" />
      <el-table-column :show-overflow-tooltip="true" prop="methodName" label="执行方法" />
      <el-table-column :show-overflow-tooltip="true" prop="params" width="120px" label="参数" />
      <el-table-column :show-overflow-tooltip="true" prop="cronExpression" label="cron表达式" />
      <el-table-column prop="createTime" label="异常详情" width="110px">
        <template #default="{ row }">
          <el-button
            v-show="row.exceptionDetail"
            size="small"
            type="primary"
            link
            @click="showErrorInfo(row.exceptionDetail)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" align="center" prop="time" width="100px" label="耗时(毫秒)" />
      <el-table-column align="center" prop="isSuccess" width="80px" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.isSuccess ? 'success' : 'danger'">
            {{ row.isSuccess ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="createTime" label="创建日期" />
    </el-table>

    <!-- 异常详情弹窗 -->
    <el-dialog v-model="errorDialog" append-to-body title="异常详情" width="85%">
      <pre v-highlightjs="errorInfo"><code class="java">{{ errorInfo }}</code></pre>
    </el-dialog>

    <!--分页组件-->
    <pagination :crud="crud" />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, nextTick, defineProps, defineEmits } from 'vue'
import { Search, Download } from '@element-plus/icons-vue'
import { useCrud } from '@/hooks/useCrud'
import { download } from '@/api/data'
import { downloadFile } from '@/utils/index'
import DateRangePicker from '@/components/DateRangePicker'
import RrOperation from '@/components/crud/RR.operation.vue'
import CrudOperation from '@/components/crud/crud.operation.vue'
import Pagination from '@/components/crud/Pagination.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const dialogVisible = ref(props.modelValue)
const errorInfo = ref('')
const errorDialog = ref(false)

// 状态选项
const enabledTypeOptions = reactive([
  { key: 'true', display_name: '成功' },
  { key: 'false', display_name: '失败' }
])

// 使用 useCrud Hook
const { crud, CRUD, query, form } = useCrud({
  title: '任务日志',
  url: 'api/jobs/logs',
  query: {
    jobName: '',
    createTime: null,
    isSuccess: ''
  }
})

// 方法定义
const doInit = () => {
  nextTick(() => {
    crud.refresh()
  })
}

// 显示异常详情
const showErrorInfo = (errorDetail) => {
  errorInfo.value = errorDetail
  errorDialog.value = true
}

// 处理下载
const handleDownload = async () => {
  try {
    crud.downloadLoading = true
    const params = crud.getQueryParams()
    const result = await download(crud.url + '/download', params)
    downloadFile(result, crud.title + '数据', 'xlsx')
  } catch (error) {
    console.error('下载失败:', error)
  } finally {
    crud.downloadLoading = false
  }
}

// 分页处理
const handleSizeChange = (size) => {
  crud.page.page = 1
  crud.page.size = size
  crud.refresh()
}

const handlePageChange = (page) => {
  crud.page.page = page
  crud.refresh()
}

// 监听 modelValue 变化
import { watch } from 'vue'
watch(() => props.modelValue, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    doInit()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 暴露方法给父组件
defineExpose({
  doInit
})
</script>

<style scoped>
.java.hljs {
  color: #444;
  background: #ffffff !important;
}

:deep(.el-dialog__body) {
  padding: 0 20px 10px 20px !important;
}

.head-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.filter-item {
  margin-right: 8px;
}

.date-item {
  margin-right: 8px;
}
</style>
