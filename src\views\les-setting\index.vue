<template>
  <div class="app-container">
  <el-tabs
    v-model="activeName"
    type="border-card"
    tab-position="top"
    class="demo-tabs"
  >
    <el-tab-pane label="全局设置" name="globalSetting" >
      <GlobalSetting />
    </el-tab-pane>
    <el-tab-pane label="流程变量设置" name="procedureVariableSetting" >
      <ProcedureVariableSetting />
    </el-tab-pane>

  </el-tabs>

  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted, computed } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { useDict } from '@/hooks/useDict'
import { useStore } from 'vuex'
import crudSetting from '@/api/setting'

import GlobalSetting from './GlobalSetting.vue'
import ProcedureVariableSetting from './ProcedureVariableSetting.vue'

const activeName = ref('globalSetting')

// refs
const formRef = ref(null)
const tableRef = ref(null)

const taskStarterMode = ref(null)

// 初始化表单
const defaultForm = {
  id: null,
  name: null,
  description: null,
  globalSettings: null,
  globalSettingsObj: {
    taskStarterMode: 'DEFAULT',
    autoSubmitTask: false,
    debugMode: false
  }
}

const rules = {
  name: [
    { required: true, message: '设备名称不能为空', trigger: 'blur' }
  ]
}

// 使用CRUD钩子
const { crud, CRUD, query, form } = useCrud({
  title: '系统设置',
  url: 'api/setting',
  crudMethod: { ...crudSetting },
  defaultForm,
  rules,
  formRef,
  tableRef
})

// 初始化
const initForm = async () => {
  
  const res = await crudSetting.queryLatestOne()
  if (res && res.id) {
    //Object.assign(form, res)
    form.id = res.id
    const settingObj = JSON.parse(res.globalSettings)
    form.globalSettingsObj = settingObj
  }
  //form.value = { ...defaultForm }
}

onMounted(() => {
  initForm()
})


// Before Submit Validation
crud.hooks[CRUD.HOOK.afterValidateCU] = () => {
  
  form.globalSettings = JSON.stringify(form.globalSettingsObj)
  if (form.id) {
    crud.status.edit = CRUD.STATUS.PREPARED
  } else {
    crud.status.add = CRUD.STATUS.PREPARED
  }
  return true
}

crud.hooks[CRUD.HOOK.afterSubmit] = () => {
  initForm()
}

</script>


<style scoped>

</style>
