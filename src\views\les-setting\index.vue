<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="120px">
        <el-divider content-position="left">全局设置</el-divider>
        <el-form-item label="进样模式">
          <el-radio-group v-model="form.globalSettingsObj.taskStarterMode">
            <el-radio :value="'DEFAULT'">单品进样模式</el-radio>
            <el-radio :value="'BOARD'">板卡进样模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="自动提交任务">
          <el-switch v-model="form.globalSettingsObj.autoSubmitTask"  />
        </el-form-item>
        <el-form-item label="调试模式">
          <el-switch v-model="form.globalSettingsObj.debugMode"  />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted, computed } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import { useDict } from '@/hooks/useDict'
import { useStore } from 'vuex'
import crudSetting from '@/api/setting'

// refs
const formRef = ref(null)
const tableRef = ref(null)

const taskStarterMode = ref(null)

// 初始化表单
const defaultForm = {
  id: null,
  name: null,
  description: null,
  globalSettings: null,
  globalSettingsObj: {
    taskStarterMode: 'DEFAULT',
    autoSubmitTask: false,
    debugMode: false
  }
}

const rules = {
  name: [
    { required: true, message: '设备名称不能为空', trigger: 'blur' }
  ]
}

// 使用CRUD钩子
const { crud, CRUD, query, form } = useCrud({
  title: '系统设置',
  url: 'api/setting',
  crudMethod: { ...crudSetting },
  defaultForm,
  rules,
  formRef,
  tableRef
})

// 初始化
const initForm = async () => {
  
  const res = await crudSetting.queryLatestOne()
  if (res && res.id) {
    //Object.assign(form, res)
    form.id = res.id
    const settingObj = JSON.parse(res.globalSettings)
    form.globalSettingsObj = settingObj
  }
  //form.value = { ...defaultForm }
}

onMounted(() => {
  initForm()
})


// Before Submit Validation
crud.hooks[CRUD.HOOK.afterValidateCU] = () => {
  
  form.globalSettings = JSON.stringify(form.globalSettingsObj)
  if (form.id) {
    crud.status.edit = CRUD.STATUS.PREPARED
  } else {
    crud.status.add = CRUD.STATUS.PREPARED
  }
  return true
}

crud.hooks[CRUD.HOOK.afterSubmit] = () => {
  initForm()
}

</script>


<style scoped>

</style>
