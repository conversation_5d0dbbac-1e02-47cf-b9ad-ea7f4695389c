<template>
  <div class="navbar">
    <div>
      <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
      <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />
    </div>

    <div class="right-menu">
      <template v-if="device!=='mobile'" />

      <span class="span-username">{{ user.roles[0].name +': ' + user.username }}</span>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">

          <img :src="user.avatarName ? baseApi + '/avatar/' + user.avatarName : Avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <template #dropdown>
          <el-dropdown-menu slot="dropdown">
            <router-link to="/user/center">
              <el-dropdown-item>
                {{ $t('sys.personalCenter') }}
              </el-dropdown-item>
            </router-link>
            <el-popover
              placement="left-start"
              :title="$t('sys.chooseLanguage')"
              width="160"
              trigger="hover"
            >
              <div>
                <span style="display:block;" @click="changeLang('zh-CN')">
                  <el-dropdown-item>中文</el-dropdown-item>
                </span>
                <span style="display:block;" @click="changeLang('en-US')">
                  <el-dropdown-item>English</el-dropdown-item>
                </span>
              </div>
              <span slot="reference" style="display:block;">
                <el-dropdown-item divided>
                  {{ $t('sys.changeLanguage') }}
                </el-dropdown-item>
              </span>
            </el-popover>
            <span style="display:block;" @click="open">
              <el-dropdown-item divided>
                {{ $t('sys.logout') }}
              </el-dropdown-item>
            </span>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import Avatar from '@/assets/images/avatar.png'
import Cookies from 'js-cookie'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search
  },
  data() {
    return {
      Avatar: Avatar,
      dialogVisible: false,
      areaOptions: [{ value: '0', label: '总部' }]
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'device',
      'user',
      'roles',
      'baseApi'
    ]),
    show: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    }
  },
  mounted() {
    const vRoles = this.roles
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    open() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.logout()
      })
    },
    logout() {
      this.$store.dispatch('LogOut').then(() => {
        location.reload()
      })
    },
    changeLang(locale) {
      Cookies.set('language', locale)
      location.reload()
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  display:flex;
  justify-content: space-between;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    height: 100%;
    display: inline-block;
  }
   .area-label {
    color: #606266;
    font-size: 13px;
    padding-right: 4px;
   }

  .cur-area-container {
    float: left;
    display: flex;
    align-items:center;
    ::v-deep .el-input__inner {
      background-color: #f4f4f5;
      color: #606266;
    }
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .span-username {
      color: #97a8be;
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
