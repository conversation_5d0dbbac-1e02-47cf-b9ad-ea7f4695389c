import request from '@/utils/request'

export function del(keys) {
  return request({
    url: 'auth/online',
    method: 'delete',
    data: keys
  })
}

export function curOnlineUser(keys) {
  return request({
    url: 'auth/online/info',
    method: 'get',
    data: keys
  })
}

export function changeOnlineShop(onlineDto) {
  return request({
    url: 'auth/online/changeShop',
    method: 'put',
    data: onlineDto
  })
}
