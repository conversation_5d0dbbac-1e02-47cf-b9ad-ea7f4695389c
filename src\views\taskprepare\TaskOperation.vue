<template>
  <div>
    <el-button v-if="data.status === 'SUCCESS'" :disabled="disabledDle" type="success" :icon="Star" size="small" circle plain @click="toSetFavoriteScheduler(data)" />

    <el-popover v-model:visible="pop" v-permission="permission.del" placement="top" width="180" trigger="manual" @show="onPopoverShow" @hide="onPopoverHide">
      <p>{{ msg }}</p>
      <div style="text-align: right; margin: 0">
        <el-button size="small" type="text" @click="doCancel">取消</el-button>
        <el-button :loading="crud.dataStatus[crud.getDataId(data)]?.delete === 2" type="primary" size="small" @click="crud.doDelete(data)">确定</el-button>
      </div>
      <template #reference>
        <el-button v-if="showDeleteButton" :disabled="disabledDle" type="danger" :icon="Delete" size="small" circle plain @click="toDelete" />
      </template>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, computed } from 'vue'
import { Star, Delete } from '@element-plus/icons-vue'
import crudTask from '@/api/task'

import { ElMessage, ElNotification } from 'element-plus'

// Props定义
const props = defineProps({
  crud: {
    type: Object,
    default: null
  },
  data: {
    type: Object,
    required: true
  },
  permission: {
    type: Object,
    required: true
  },
  disabledDle: {
    type: Boolean,
    default: false
  },
  msg: {
    type: String,
    default: '请确保该任务已运行的硬件正确复位，已撤销的任务不可恢复，确定撤销该任务吗？'
  }
})

// 响应式状态
const pop = ref(false)
// 获取 crud 对象：优先使用 props 传入的，否则从 inject 获取
const crud = props.crud || inject('crud')
if (!crud) {
  throw new Error('`crud` 未提供，请确保在父组件中通过 provide 注入')
}
// 计算属性
const showDeleteButton = computed(() => 
  ['CANCELLED', 'SUCCESS', 'FAILED'].includes(props.data.status) === false
)

// 方法
const toSetFavoriteScheduler = async (data) => {
  try {
    await crudTask.setFavoriteScheduler(data)
    ElNotification.success({
      title: '设置评估调度时间成功！',
      duration: 2000
    })
  } catch (error) {
    ElNotification.error({
      title: '设置失败',
      message: error.message
    })
  }
}

const doCancel = () => {
  pop.value = false
  crud.cancelDelete(props.data)
}

const toDelete = () => {
  pop.value = true
}

const onPopoverShow = () => {
  setTimeout(() => {
    document.addEventListener('click', handleDocumentClick)
  }, 0)
}

const onPopoverHide = () => {
  document.removeEventListener('click', handleDocumentClick)
}

const handleDocumentClick = () => {
  pop.value = false
}

// 生命周期
onMounted(() => {
  const popover = document.querySelector('.el-popover')
  if (popover) {
    document.body.appendChild(popover)
  }
})
</script>
