<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="方法名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="方法类型" prop="type">
        <el-select v-model="form.method.type" clearable size="small" placeholder="请选择">
          <el-option v-for="item in dict.method_types" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { Node } from '@antv/x6'

const defaultForm = { id: null, name: null, description: null, method: {type:'MAIN'}, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }

export default {
  name: 'MethodProperty',
  props: {
    curNode: {
      default: () => {
        return {}
      },
      type: Node
    }
  },
  dicts: ['method_types'],
  inject: ['getDataStore'],
  data() {
    return {
      form: { ...defaultForm },
      rules: {
        name: [
          { required: true, message: '方法名称不能为空', trigger: 'blur' }
        ]
      },

      initing: false,
      dataStore: {}

    }
  },
  computed: {

  },
  watch: {
    dataStore: { handler(newVal, oldVal) {
      this.transformNodeToFormData()
    }, deep: true },
    curNode: { handler(newVal, oldVal) {
      this.transformNodeToFormData()
    }, deep: false },
    form: { handler(newVal, oldVal) {
      this.transformFormDataToNode()
    }, deep: true }
  },
  created() {
    this.transformNodeToFormData()
  },
  methods: {

    init() {
      this.initing = true
    },
    transformNodeToFormData() {
      const curNode = this.curNode
      debugger
      this.dataStore = this.getDataStore()
      this.form.name = curNode.getData().name
      this.form.description = curNode.getData().description
      if (curNode.getData().method) {
        this.form.method.type = curNode.getData().method.type
      }
    },
    transformFormDataToNode() {
      const curNode = this.curNode
      this.dataStore = this.getDataStore()
      debugger
      curNode.getData().name = this.form.name
      curNode.getData().description = this.form.description
      curNode.getData().method.type = this.form.method.type
      const methodData = this.getStoreObjectByNodeId(curNode.id)
      if (!methodData.method) {
        methodData.method = {}
      }

      methodData.method.name = this.form.name
      methodData.method.type = this.form.method.type
      methodData.method.description = this.form.description
      const changeState = 'changed from MethodProperty'
      curNode.setData({ ...this.curNode.getData(), changeState })
      // this.$emit('update-device-instance', deviceInst)
    },
    getStoreObjectByNodeId(nodeId) {
      if (this.dataStore.dagNodeId === nodeId) {
        return this.dataStore
      }
      if (!Array.isArray(this.dataStore.methods)) {
        return null
      }
      for (const methodObj of this.dataStore.methods) {
        if (methodObj.dagNodeId === nodeId) {
          return methodObj
        }
        if (!Array.isArray(methodObj.method.steps)) {
          continue
        }
        for (const stepObj of methodObj.method.steps) {
          if (stepObj.dagNodeId === nodeId) {
            return stepObj
          }
          if (!Array.isArray(stepObj.step.actions)) {
            continue
          }
          for (const actionObj of stepObj.step.actions) {
            if (actionObj.dagNodeId === nodeId) {
              return actionObj
            }
            if (!Array.isArray(actionObj.commands)) {
              continue
            }
            for (const cmdObj of actionObj.commands) {
              if (cmdObj.dagNodeId === nodeId) {
                return cmdObj
              }
            }
          }
        }
      }

      return null
    }
  }
}
</script>

<style lang="scss">

</style>
