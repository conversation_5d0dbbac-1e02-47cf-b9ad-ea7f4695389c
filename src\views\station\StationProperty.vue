<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="工作站名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="form.type" clearable size="small" placeholder="类型">
          <el-option 
            v-for="item in dict.data.device_types" 
            :key="item.value" 
            :label="item.label" 
            :value="item.value" 
          />
        </el-select>
      </el-form-item>
      <el-form-item label="线程数" prop="maxThreadSize">
        <el-input v-model="form.maxThreadSize" />
      </el-form-item>
    </el-form>
    <el-divider content-position="left">设备实例列表</el-divider>
    <div class="device-container">
      <div v-for="dev in dataStore.devices" :key="dev.id" class="device-area">
        <i 
          class="device-image" 
          :style="'background-image: url('+getIconUrl(dev.layoutImage||dev.device.layoutImage)+')'"
        />
        <span class="device-label">{{ dev.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed, inject } from 'vue'
import { useStore } from 'vuex'
import { Node } from '@antv/x6'
import { useDict } from '@/hooks/useDict'

// Props 定义
const props = defineProps({
  curNode: {
    type: Object,
    default: () => ({})
  }
})

// 注入
const getDataStore = inject('getDataStore')
const dataStore = getDataStore()

// Store
const store = useStore()

// 使用 useDict hook
const { dict, loading } = useDict(['device_types'])

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  type: 'PERIPHERAL',
  maxThreadSize: 1,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

const formRef = ref(null)
const initing = ref(false)
const devPosition = ref([{ xpos: 0.0, ypos: 0.0, zpos: 0.0 }])

// 计算属性
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// 方法定义
const init = () => {
  initing.value = true
}

const openStationDialog = () => {
  console.log('openStationDialog ...')
}

const getIconUrl = (imagUrl) => {
  return imageAccessUrl.value + imagUrl
}

const transformNodeToFormData = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  form.name = curNode.getData().name
  form.description = curNode.getData().description
  if (dataStore.type) {
    form.type = dataStore.type
  } else {
    form.type = 'PERIPHERAL'
  }
  form.maxThreadSize = dataStore.maxThreadSize
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())
  curNode.getData().name = form.name
  curNode.getData().description = form.description
  dataStore.name = form.name
  dataStore.description = form.description

  const changeState = 'changed from property'
  curNode.setData({ ...curNode.getData(), changeState })
}

// 监听器
watch(dataStore, () => {
  transformNodeToFormData()
}, { deep: true })

watch(() => form.type, (newVal) => {
  Object.assign(dataStore, getDataStore())
  dataStore.type = newVal
})

watch(() => form.maxThreadSize, (newVal) => {
  Object.assign(dataStore, getDataStore())
  dataStore.maxThreadSize = newVal
})

watch(() => form.name, (newValue, oldValue) => {
  console.log(`form changed from "${oldValue}" to "${newValue}"`)
  Object.assign(dataStore, getDataStore())
  dataStore.name = newValue
  props.curNode.getData().name = newValue
  const changeState = 'changed from property'
  props.curNode.setData({ ...props.curNode.getData(), changeState })
})

watch(() => form.description, (newValue, oldValue) => {
  console.log(`description changed from "${oldValue}" to "${newValue}"`)
  Object.assign(dataStore, getDataStore())
  dataStore.description = newValue
  props.curNode.getData().description = newValue
  const changeState = 'changed from property'
  props.curNode.setData({ ...props.curNode.getData(), changeState })
})

// 生命周期钩子
onMounted(() => {
  Object.assign(dataStore, getDataStore())
  form.name = props.curNode.getData().name
  form.description = props.curNode.getData().description
})
</script>

<style lang="scss" scoped>
.device-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.device-area {
  width: 100px;
  height: 120px;
}
.device-label {
  font-size: 12px;
}
.device-image {
  display: block;
  width: 80px;
  height: 80px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
</style>
