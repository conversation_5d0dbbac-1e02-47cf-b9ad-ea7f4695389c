<template>
  <div :class="tagsView ? 'app-container-tags' : 'app-container'">

    <div :class="tagsView ? 'flow-tags' : 'flow'">
      <div class="content">
        <div id="graph_panel" ref="graph_panel" class="panel" :style="{ width: `calc(100% - 4px - ${configWidth}px)` }">
          <!--流程图工具栏-->
          <div class="toolbar" :style="{ width: `calc(100% - 4px - ${configWidth}px)` }">
            <!-- <tool-bar v-if="isReady" /> -->
            <el-button-group class="toolbar-group">

              <el-tooltip class="item" effect="dark" content="放大" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomIn"
                  @click="graph.zoom(0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="缩小" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomOut"
                  @click="graph.zoom(-0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="调整到合适大小" placement="top">
                <el-button
                  size="small"
                  :icon="Aim"
                  @click="zoomToFit()"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="重置所有设备点位状态" placement="top">
                <el-button
                  size="small"
                  :icon="Coordinate"
                  @click="resetAllPositionsHandler()"
                />
              </el-tooltip>
              <el-popover
                placement="bottom-end"
                width="150"
                trigger="click"
              >
                <el-button
                  slot="reference"
                  size="small"
                  :icon="Grid"
                >
                  <i
                    class="fa fa-caret-down"
                    aria-hidden="true"
                  />
                </el-button>

              </el-popover>

            </el-button-group>
          </div>
          <!--流程图画板-->
          <div id="container"  :style="{'background': graphContainerBackgroundStyle}"  />
        </div>
        <!-- 可拖动分隔条 -->
        <div class="resizer" @mousedown="startResize"></div>

        <!--右侧工具栏-->
        <el-tabs type="border-card" class="config" :style="{ width: `${configWidth}px`}">
          <el-tab-pane class="tab-pane">
            <template #label>
              <span class="tab-label"><el-icon><Connection /></el-icon><span>调试指令</span></span>
            </template>
            <CommandDebugger v-if="selectedNode" :cur-node="selectedNode" />
          </el-tab-pane>
        </el-tabs>

      </div>
    </div>
  </div>

</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, markRaw } from 'vue'
import { useStore } from 'vuex'
import { ElNotification, ElMessageBox } from 'element-plus'
import { Graph } from '@antv/x6'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { register } from '@antv/x6-vue-shape'
import { stroke } from '@antv/x6/lib/registry/highlighter/stroke'
import { add, edit, getLatest, query, resetAllPositions } from '@/api/deviceLayout'
import { queryAllDevices } from '@/api/device'

import CommandDebugger from './CommandDebugger'
import DeviceNode from '@/views/devicelayout/DeviceNode.vue'
import useWebSocket from '@/hooks/useWebSocket'
import {
  ZoomIn,
  ZoomOut,
  Aim,
  Coordinate,
  Grid,
  Connection
} from '@element-plus/icons-vue';

// 注册X6 Vue组件（Vue3需要markRaw）
register({
  shape: 'device-node',
  width: 212,
  height: 48,
  component: markRaw(DeviceNode)
})

// 响应式状态
const store = useStore()
const graph = ref(null)
const selectedNode = ref(null)
const dataStore = reactive({
  deviceInstanceList: [],
  layoutSvg: null,
  status: null
})
const resizeObserver = ref(null)

const graphContainerBackgroundStyle = ref(null)

// 计算属性
const userInfo = computed(() => store.getters.user)
const baseApi = computed(() => store.getters.baseApi)
const tagsView = computed(() => store.state.settings.tagsView)

const configWidth = ref(360) // 右侧属性面板宽度
const isResizing = ref(false)

// 方法实现
const initGraph = () => {
  const container = document.getElementById('container')
  graph.value = new Graph({
    container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    autoResize: true,
    panning: { enabled: true },
    background: false,
    grid: false,
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    },
    // 在初始化时就禁用节点移动
    interacting: {
      nodeMovable: false
    },
    selecting: {
      enabled: true,
      movable: false,
      showNodeSelectionBox: true
    }
  })

  // 初始化插件
  graph.value.use(
    new Selection({
      rubberband: true,
      showNodeSelectionBox: true,
      multiple: false,
      movable: false,
      filter: cell => cell.shape === 'device-node'
    })
  )
    // 额外设置全局节点不可移动
    graph.value.options.selecting = {
    enabled: true,
    movable: false,
    showNodeSelectionBox: true
  }
// 禁用节点拖拽
graph.value.disableSelectionMovable()
  
  // 加载初始数据
  loadInitialData()
}

const loadInitialData = async () => {
  try {
    const res = await getLatest(null)
    if (res) {
      Object.assign(dataStore, res)
      graph.value.dataStore = res
      graph.value.fromJSON(JSON.parse(dataStore.layoutSvg))
      if (dataStore.styleSetting) {
        const styleSettingObj = JSON.parse(dataStore.styleSetting)
        graphContainerBackgroundStyle.value = styleSettingObj.background
      }
      zoomToFit()
    }
  } catch (error) {
    console.error('Failed to load initial data:', error)
  }
}

const zoomToFit = () => {
  graph.value?.zoomToFit({ 
    padding: 48, 
    minScale: 0.8,
    maxScale: 4 
  })
  graph.value?.centerContent()
}

const resetAllPositionsHandler = async () => {
  try {
    const res = await resetAllPositions(dataStore)
    const updatedDevInsts = res
    for (const devInst of updatedDevInsts) {
      const nodeId = devInst.layoutNodeId
      const storeDevInst = getDeviceInstanceByNodeId(nodeId)
      if (storeDevInst) {
        storeDevInst.positions = devInst.positions
        const node = graph.value.getCellById(nodeId)
        if (node) {
          node.setData({ ... node.getData(), changeState: 'POSITION_CHANGE' })
        }
      }
    }
    ElNotification({
      title: '重置点位成功',
      type: 'success',
      duration: 2500
    })
    console.log('重置点位成功')
  } catch (error) {
    console.error('Reset positions failed:', error)
  }
}

// 节点事件处理
const initNodeEvents = () => {
  graph.value?.on('node:click', ({ node }) => {
    // 根据条件设置 preserveAspectRatio
    const shouldPreserveAspectRatio = node.shape !== 'rect'
    if (node.shape === 'rect') {
      node.setZIndex(0)
    }
  })

  graph.value?.on('cell:selected', ({ cell }) => {
    if (cell.isNode()) {
      selectedNode.value = cell
    }
  })

  graph.value?.on('cell:unselected', () => {
    selectedNode.value = null
  })

  graph.value?.on('node:added', ({ node }) => {
    const nodeData = node.getData()
    nodeData.layoutNodeId = node.id
    if (nodeData.device) {
      if (nodeData.name === undefined || nodeData.name === null) {
        nodeData.name = nodeData.device.name
      }
      if (nodeData.description === undefined || nodeData.description === null) {
        nodeData.description = nodeData.device.description
      }
      if (nodeData.type === undefined || nodeData.type === null) {
        nodeData.type = nodeData.device.type
      }
    }
    dataStore.deviceInstanceList.push(nodeData)
  })
}

// 响应式布局
const observeResize = () => {
  const element = document.querySelector('#graph_panel')
  resizeObserver.value = new ResizeObserver(entries => {
    for (const entry of entries) {
      console.log('Element size changed:', entry.contentRect)
      graph.value?.resize(entry.contentRect.width, entry.contentRect.height)
      zoomToFit()
    }
  })
  resizeObserver.value.observe(element)
}

function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

const onTaskResponse = (data) => {
  const dto = JSON.parse(data.msg)
  if (dataStore.deviceInstanceList === undefined || dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in dataStore.deviceInstanceList) {
    const ins = dataStore.deviceInstanceList[i]
    if (ins.positions) {
      for (var posIdx in ins.positions) {
        const pos = ins.positions[posIdx]
        if (pos.id === dto.id) {
          ins.positions.splice(posIdx, 1, dto)
          const node = graph.value.getCellById(ins.layoutNodeId)
          if (node) {
            node.setData({ ... node.getData(), changeState: 'POSITION_CHANGE' })
          }
          break
        }
      }
    }
  }
}

const getDeviceInstanceByNodeId = (nodeId) => {
  if (dataStore.deviceInstanceList === undefined || dataStore.deviceInstanceList.length === 0) {
    return null
  }
  for (var i in dataStore.deviceInstanceList) {
    const ins = dataStore.deviceInstanceList[i]
    if (ins.layoutNodeId === nodeId) {
      return ins
    }
  }
  return null
}

// 添加拖动相关的方法
const startResize = (e) => {
  isResizing.value = true
  
  const handleMouseMove = (moveEvent) => {
    if (isResizing.value) {
      // 计算新的右侧面板宽度
      const containerWidth = document.querySelector('.content').offsetWidth
      const newWidth = containerWidth - moveEvent.clientX
      
      // 设置最小和最大宽度限制
      if (newWidth >= 200 && newWidth <= 500) {
        configWidth.value = newWidth
      }
    }
  }
  
  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    
    // 调整图表大小以适应新的布局
   // if (graph.value) {
   //   const container = document.getElementById('container')
   //   graph.value.resize(container.offsetWidth, container.offsetHeight)
   // }
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  
  // 阻止默认行为和冒泡
  e.preventDefault()
  e.stopPropagation()
}

// 替换原有的websocket相关代码
const { send, status } = useWebSocket({
  url: (id) => `${import.meta.env.VITE_APP_WS_API}/webSocket/positionStateChange_${id}`,
  onMessage: onTaskResponse,
  reconnectInterval: 5000,
  autoReconnect: true,
  debug: process.env.NODE_ENV === 'development'
})

// 生命周期
onMounted(() => {
  initGraph()
  initNodeEvents()
  observeResize()
})

const clearDeviceNodes = () => {
  for (const node of graph.value.getNodes()){
    node.setData({ ... node.getData(), changeState: 'NODE_UNMOUNT' })
  } 
}

onBeforeUnmount(() => {
  resizeObserver.value?.disconnect()
  clearDeviceNodes()
})

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container {
  width: 100%;
  height: calc(100vh - 50px);
  padding:0px;
}
.app-container-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

.flow {
  /* width: 100vw; */
  width: 100%;
  height: calc(100vh - 50px);
}
.flow-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

  .content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  #stencil {
    width: 290px;
    height: 100%;
    position: relative;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
  }

  .panel {
    width: calc(100% - 360px);
    height: 100%;
  }

  .panel .toolbar {
    width: calc(100% - 360px);
    height: 38px;
    padding-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f7f9fb80;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position:absolute;
    z-index: 900;
  }
  .toolbar-group {
    border-radius: 20px;
    background-color: #FFFFFF;
    padding: 0px 20px;
  }
  .panel #container {
    width: 100%;
    height: calc(100% - 38px);
    background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
    background-size: cover;
  }

// 添加分隔条样式
.resizer {
  width: 4px;
  height: 100%;
  background-color: #e0e0e0;
  cursor: col-resize;
  transition: background-color 0.2s;
  z-index: 1000;
  
  &:hover, &:active {
    background-color: #1890ff;
  }
}

  .config {
    width: 360px;
    height: 100%;
    padding: 0px 0px;
    border-left: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    
    :deep(.el-tabs__content) {
      overflow-y: auto;
    }
  }

  .node_icon {
    color: darkcyan;
  }

.config .tab-label .el-icon {
  vertical-align: middle;
}
.config .tab-label span {
  vertical-align: middle;
  margin-left: 4px;
}

</style>
