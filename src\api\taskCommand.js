import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/taskCommand',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/taskCommand/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/taskCommand',
    method: 'put',
    data
  })
}

export function updateFailureFixInfo(data) {
  return request({
    url: 'api/taskCommand/updateFailureFixInfo',
    method: 'put',
    data
  })
}

export default { add, edit, del, updateFailureFixInfo }
