import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/station',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/station/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/station',
    method: 'put',
    data
  })
}

export function queryStationById(params) {
  return request({
    url: 'api/station/queryStationById',
    method: 'get',
    params
  })
}
export function queryActionQueueByStationId(params) {
  return request({
    url: 'api/station/queryActionQueueByStationId',
    method: 'get',
    params
  })
}
export function queryAllStationsInRunning(params) {
  return request({
    url: 'api/station/queryAllStationsInRunning',
    method: 'get',
    params
  })
}


export default { add, edit, del, queryStationById, queryActionQueueByStationId, queryAllStationsInRunning }
