<template>
  <div :class="tagsView ? 'app-container-tags' : 'app-container'">
    <div :class="tagsView ? 'flow-tags' : 'flow'">
      <div class="content">
        <div id="stencil" />
        <div class="panel" :style="{ width: `calc(100% - ${stencilWidth}px - ${configWidth}px)` }">
          <div class="toolbar" :style="{ width: `calc(100% - ${stencilWidth}px - ${configWidth}px)` }">
            <el-button-group class="toolbar-group">
              <el-button
                :loading="configLoading"
                type="primary"
                size="small"
                plain
                :icon="Finished"
                @click="doSubmit('DRAFT')"
              >保存</el-button>
              <el-tooltip class="item" effect="dark" content="放大" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomIn"
                  @click="graph.zoom(0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="缩小" placement="top">
                <el-button
                  size="small"
                  :icon="ZoomOut"
                  @click="graph.zoom(-0.2)"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="调整到合适大小" placement="top">
                <el-button
                  size="small"
                  :icon="Aim"
                  @click="zoomToFit"
                />
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除选中的节点" placement="top">
                <el-button
                  size="small"
                  :icon="Delete"
                  @click="removeNodes()"
                />
              </el-tooltip>
            </el-button-group>
          </div>
          <div id="container" :style="{'background': graphContainerBackgroundStyle}" />
        </div>
        <!-- 可拖动分隔条 -->
        <Resizer v-model:width="configWidth" @onSizeChange="()=>{zoomToFit()}"/>

        <el-tabs type="border-card" class="config"  :style="{ width: `${configWidth}px`}">
          <el-tab-pane>
            <template #label>
              <span class="tab-label"><el-icon><Crop /></el-icon><span>布局属性</span></span>
            </template>
            <RectangleProperty v-if="selectedNode && selectedNode.shape === 'rect'" :cur-node="selectedNode" />
            <ImageProperty v-if="selectedNode && selectedNode.shape === 'image'" :cur-node="selectedNode" />
            
            <LayoutProperty v-if="selectedNode && selectedNode.shape === 'device-node'" :cur-node="selectedNode" />
            <GraphProperty v-if="!selectedNode" :graph="graph" :dataStore="dataStore" @update:modelValue="changeBackgroundColor" />
          </el-tab-pane>
          <el-tab-pane v-if="selectedNode && selectedNode.shape === 'device-node'" >
            <template #label>
              <span class="tab-label"><el-icon><Document /></el-icon><span>配置属性</span></span>
            </template>
            <ConfigProperty v-if="selectedNode" :cur-node="selectedNode" />
          </el-tab-pane>
          <el-tab-pane v-if="selectedNode && selectedNode.shape === 'device-node'" >
            <template #label>
              <span class="tab-label"><el-icon><Connection /></el-icon><span>设备指令</span></span>
            </template>
            <CommandProperty v-if="selectedNode" :cur-node="selectedNode" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, provide, onMounted, onBeforeUnmount, computed, markRaw } from 'vue'
import { useStore } from 'vuex'
import { 
  ZoomIn,
  ZoomOut,
  Aim,
  Delete,
  Finished,
  Crop,
  Document,
  Connection 
} from '@element-plus/icons-vue'
import { ElMessage,ElNotification } from 'element-plus'

import { Graph, Shape } from '@antv/x6'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'

import { register } from '@antv/x6-vue-shape'
// 导入图像资源
import defaultImage from '@/assets/images/default_image.svg'

import { stroke } from '@antv/x6/lib/registry/highlighter/stroke'
import { add, edit, getLatest, query } from '@/api/deviceLayout'
import { queryAllDevices } from '@/api/device'

import { getDeviceInstanceByNode } from './commonFunction'
import { debounce } from '@/utils'

import LayoutProperty from './LayoutProperty'
import ConfigProperty from './ConfigProperty'
import CommandProperty from './CommandProperty'
import GraphProperty from './GraphProperty'
import RectangleProperty from './RectangleProperty.vue'
import ImageProperty from './ImageProperty.vue'
import DeviceNode from './DeviceNode.vue'
import Resizer from '@/components/Resizer'

register({
  shape: 'device-node',
  width: 212,
  height: 48,
  component: markRaw(DeviceNode)
})

const common_ports = {
  groups: {
    top: {
      position: 'top',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden'
          }
        }
      }
    },
    right: {
      position: 'right',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden'
          }
        }
      }
    },
    bottom: {
      position: 'bottom',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden'
          }
        }
      }
    },
    left: {
      position: 'left',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden'
          }
        }
      }
    }
  },
  items: [
    {
      group: 'top'
    },
    {
      group: 'right'
    },
    {
      group: 'bottom'
    },
    {
      group: 'left'
    }
  ]
}
// 设备类型分组
const deviceGroups = [
  {
    name: 'PERIPHERAL',
    title: '外围设备',
    collapsable: true
  },
  {
    name: 'STORAGE',
    title: '存储设备',
    collapsable: true
  },
  {
    name: 'ROBOT',
    title: '机器人',
    collapsable: true
  },
  {
    name: 'CONTROL',
    title: '控制设备',
    collapsable: true
  },
  {
    name: 'SYS',
    title: '系统',
    collapsable: true
  },
  {
    name: '其他',
    collapsable: true
  }

]

const store = useStore()
const graph = ref(null)
const stencil = ref(null)
const selectedNode = ref(null)
const configLoading = ref(false)
const dataStore = reactive({
  deviceInstanceList: [],
  layoutSvg: null,
  styleSetting: null,
  status: null
})

const graphContainerBackgroundStyle = ref(null)

const tagsView = computed(() => store.state.settings.tagsView)
const userInfo = computed(() => store.getters.user)
const baseApi = computed(() => store.getters.baseApi)

const stencilWidth = ref(290) // 左侧工具栏宽度
const configWidth = ref(360) // 右侧属性面板宽度

onMounted(() => {
  initGraph()
  initData()
  initStencil()
  initNodeEvents()
})

const initGraph = async () => {
  const container = document.getElementById('container')
  graph.value = new Graph({
    container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    autoResize: true,
    panning: {
      enabled: true,
      eventTypes: ['rightMouseDown']
    },
    background: false,
    // 修改网格配置
    grid: {
      type: 'dot',
      size: 2,
      visible: true,
      args: {
        color: '#909090',  // 网格点的颜色
        thickness: 1       // 网格点的大小
      }
    },
    // 添加网格对齐配置
    snapline: {
      enabled: false,
      sharp: true
    },
    // 添加移动和调整大小时的网格对齐配置
    resizing: {
      enabled: true,
      snap: false,        // 禁用大小调整时的网格对齐
      restricted: false   // 不限制在网格内调整大小
    },
    translating: {
      restrict: false     // 不限制在网格内移动
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3
    },
    connecting: {
      router: 'manhattan',
      connector: {
        name: 'rounded',
        args: {
          radius: 8
        }
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false,
      snap: {
        radius: 20
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8
              }
            }
          },
          zIndex: 0
        })
      },
      validateConnection({ targetMagnet }) {
        return !!targetMagnet
      }
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF'
          }
        }
      }
    },
    interacting: {
      nodeMovable: (view) => {
        const node = view.cell

        return !node.getData()?.locked
      }
    }
  })

  const selection = new Selection({
    rubberband: true,
    showNodeSelectionBox: true,
    // 添加过滤器，排除锁定的节点
    filter: (cell) => {
      // 如果是节点且被锁定，则不允许选择
      if (cell.isNode() && cell.getData()?.locked === true) {
        return false
      }
      return true
    }
  })
  const transform = new Transform({
    resizing: {
      //enabled: true,
      // 添加锁定检查
      enabled: (node) => {
        // 如果节点被锁定，则禁止调整大小
        if (node.getData()?.locked === true) {
          return false
        }
        return true
      },
      preserveAspectRatio: (node) => {
        if (node.shape === 'device-node') {
          return true
        } else {
          return false
        }
      }
    },
      // 添加translating配置，禁止锁定节点移动
    translating: {
      enabled: (node) => {
        // 如果节点被锁定，则禁止移动
        if (node.getData()?.locked === true) {
          
          return false
        }
        return true
      }
    },
    rotating: {
      enabled: (node) => {
        // 如果节点被锁定，则禁止旋转
        if (node.getData()?.locked === true) {
          return false
        }
        return true
      }
    }
  })

  graph.value
    .use(transform)
    .use(selection)
    //.use(new Snapline())
    .use(new Keyboard())
    .use(new Clipboard())
    .use(new History())

}

const initData = async () =>{

  const res = await getLatest(null)
  if (res) {
    Object.assign(dataStore, res)
    graph.value.dataStore = res
    if (dataStore.layoutSvg) {
      const layoutSvgObj = JSON.parse(dataStore.layoutSvg)
      graph.value.fromJSON(layoutSvgObj)
      if (dataStore.styleSetting) {
        const styleSettingObj = JSON.parse(dataStore.styleSetting)
        graphContainerBackgroundStyle.value = styleSettingObj.background
      }
      
    }
    zoomToFit()
  }
}

const zoomToFit = () => {
  graph.value?.zoomToFit({ 
    padding: 48,
    minScale: 0.8,
    maxScale: 4 
  })
  graph.value?.centerContent()
}

const doSubmit = async (status) => {
  configLoading.value = true
  try {
    
    const result = graph.value.toJSON()
    dataStore.layoutSvg = JSON.stringify(result)
    if (dataStore.styleSetting) {
      const styleSettingObj = JSON.parse(dataStore.styleSetting)
      styleSettingObj.background = graphContainerBackgroundStyle.value
      dataStore.styleSetting = JSON.stringify(styleSettingObj)
    }else{
      dataStore.styleSetting = JSON.stringify({
        background: graphContainerBackgroundStyle.value
      })
    }
    dataStore.status = status

    graph.value.getNodes().forEach(node => {
      if (node.getData()) {
        node.getData().layoutNodeId = node.id
      }
    })
debugger
    sortDataStore()
debugger
    const apiMethod = dataStore.id ? edit : add
    const res = await apiMethod(dataStore)
    
    // 返回失败
    debugger
      if (res.status && res.status =='400' && res.data ) {      
        // 获取工作站名称
        const resp = res
        
        // 构建引用列表HTML
        let referencesHtml = ''
        if (resp && resp.data && Array.isArray(resp.data)) {
          referencesHtml = '<ul style="padding-left: 20px; margin: 5px 0;">'
          resp.data.forEach(item => {
            referencesHtml += `<li>${item.devInstName} <--> ${item.stationName} </li>`
          })
          referencesHtml += '</ul>'
        }
        
        // 显示错误通知
        ElNotification({
          title: '更新失败',
          message: `待删除设备实例 已被如下工作站引用，请删除对应引用后再删除设备实例 ${referencesHtml}`,
          type: 'error',
          dangerouslyUseHTMLString: true,
          duration: 5000
        })
        initData()
        return    
      }


    Object.assign(dataStore, res)
    graph.value.dataStore = res
    // 显示成功通知...
    ElNotification({
          title: '保存成功',
          type: 'success',
          duration: 5000
        })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    configLoading.value = false
  }
}


const sortDataStore = () => {
  if (!Array.isArray(dataStore.deviceInstanceList)) return
  // 过滤掉 dagNodeId 不存在于图表中的方法
  
  dataStore.deviceInstanceList = dataStore.deviceInstanceList.filter(devInst => {
    return devInst.layoutNodeId && graph.value.getCellById(devInst.layoutNodeId);
  });
  
}


const initStencil = () => {
  // 创建Stencil
  stencil.value = new Stencil({
    title: '设备面板',
    search(cell, keyword) {
      return cell.shape.indexOf(keyword) !== -1
    },
    placeholder: '搜索设备名',
    notFoundText: 'Not Found',
    collapsable: false,
    stencilGraphHeight: 0,
    target: graph.value,
    groups: deviceGroups,
    graphPadding: 10,
    graphWidth: '100%',
    layoutOptions: {
      columns: 2,
      rowHeight: 180,
      columnWidth: 130,
      marginY: 0,
      dy: -20
    }
  })
  var stencilTopContainer = document.getElementById('stencil')
  stencilTopContainer.appendChild(stencil.value.container)

  const commonAttrs = {
    body: {
      fill: '#f0fe00',
      stroke: '#8f8f23',
      strokeWidth: 1,
      class: 'node_icon'
    },
    label: {
      refX: '50%',
      refY: '100%',
      refY2: 14,
      height: 24,
      textAnchor: 'middle',
      textVerticalAnchor: 'middle'
    },
    image: {
      fill: '#ff0000',
      stroke: '#ffff00'
    }
  }

  queryAllDevices(null).then(res => {
    console.log('queryAllDevices.res=' + res)
    if (res == null) {
      dataStore.allDevices = {}
    } else {
      dataStore.allDevices = res

      deviceGroups.forEach((devGrp, idx) => {
        devGrp.devices = []
        devGrp.nodes = []
        res.forEach(dev => {
          if (dev.type === devGrp.name) {
            devGrp.devices.push(dev)
            devGrp.nodes.push(
              graph.value.createNode({
                shape: 'device-node',
                width: dev.layoutWidth,
                height: dev.layoutHeight,
                label: dev.name,
                ports: { ...common_ports },
                attrs: commonAttrs,
                transforming: {
                  scale: true
                },
                data: { 
                  layoutImage: dev.layoutImage, 
                  layoutWidth: dev.layoutWidth,
                  layoutHeight: dev.layoutHeight,
                  positionConfig: dev.positionConfig,
                  device: {
                    id: dev.id,
                    name: dev.name,
                    layoutWidth: dev.layoutWidth,
                    layoutHeight: dev.layoutHeight,
                    description: dev.description,
                    type: dev.type
                  }
                }
              })
            )
          }
        })
        if (devGrp.nodes.length > 0) {
          stencil.value.load(devGrp.nodes, devGrp.name)
        }
      })
    }
  })

  const nodeRect = graph.value.createNode({
    shape: 'rect',
    width: 100,
    height: 100,
    label: '',
    zIndex: 0,
    transform: {
      resizing: {
        preserveAspectRatio: false
      }
    },
    attrs: { ... commonAttrs, ...{ body: { fill: '#ffffff1f', stroke: '#FFFBFB' }}}
  })

  const nodeImage = graph.value.createNode({
    shape: 'image',
    width: 100,
    height: 100,
    label: '',
    zIndex: 0,
    imageUrl: defaultImage,
    image: {
          'xlink:href': defaultImage,
          width: '100%',
          height: '100%',
          refWidth: 1,
          refHeight: 1,
          preserveAspectRatio: 'none' // 关键属性：不保持宽高比，实现平铺效果
        },
    transform: {
      resizing: {
        preserveAspectRatio: false
      }
    },
    attrs: { ... commonAttrs, ...{ body: { fill: '#ffffff1f', stroke: '#FFFBFB' }}}
  })

  console.log('nodeImage=' + JSON.stringify(nodeImage.prop()))

  stencil.value.load([nodeRect, nodeImage], '其他')
}

const removeNodes = () => {
  if (!graph.value) return
  const nodes = graph.value.getSelectedCells()
  
  for (const node of nodes) {
    graph.value.removeCell(node)
    graph.value.removeConnectedEdges(node)
    
    //dataStore.deviceInstanceList.push(nodeData)

  }
}

const initNodeEvents = () => {
  graph.value.on('node:click', ({ node }) => {
    const shouldPreserveAspectRatio = node.shape !== 'rect'
    //if (node.shape === 'rect') {
    //  node.setZIndex(0)
    //}
  })

  graph.value.on('cell:selected', ({ cell }) => {
    if (cell.isNode()) {
      selectedNode.value = cell
    }
  })

  graph.value.on('cell:unselected', ({ cell }) => {
    selectedNode.value = null
  })

  graph.value.on('node:added', ({ node }) => {
    const nodeData = node.getData()
    if (!nodeData) return
    nodeData.layoutNodeId = node.id
    if (nodeData.device) {
      if (nodeData.name === undefined || nodeData.name === null) {
        nodeData.name = nodeData.device.name
      }
      if (nodeData.description === undefined || nodeData.description === null) {
        nodeData.description = nodeData.device.description
      }
      if (nodeData.type === undefined || nodeData.type === null) {
        nodeData.type = nodeData.device.type
      }
    }

    dataStore.deviceInstanceList.push(nodeData)
  })

  graph.value.on('node:change:size', ({ node }) => {
    
    debounce( ()=>{
      const devInst = getDeviceInstanceByNode(node)
      if (devInst){
        if (devInst.layoutWidth !== node.getSize().width){
          devInst.layoutWidth = node.getSize().width
        }
        if (devInst.layoutHeight!== node.getSize().height){
          devInst.layoutHeight = node.getSize().height
        }
        
      }
    }, 100)
    
  })

  // 添加节点移动前的检查
  graph.value.on('node:move', ({ node, e }) => {
    // 如果节点被锁定，则阻止移动
    
    if (node.getData()?.locked === true) {
      e.preventDefault()
      e.stopPropagation()
      return false
    }
  })
  graph.value.on('node:moving', ({ node, e }) => {
    // 如果节点被锁定，则阻止移动
    
    if (node.getData()?.locked === true) {
      e.preventDefault()
      e.stopPropagation()
      return false
    }
  })
  
  // 绑定键盘方向键，实现节点平移
  graph.value.bindKey(['up', 'down', 'left', 'right'], (e) => {
    const cells = graph.value.getSelectedCells()
    debugger
    if (cells.length === 0) return
    
    e.preventDefault() // 阻止默认行为
    
    const key = e.key.toLowerCase()
    const dx = key === 'arrowleft' ? -0.5 : key === 'arrowright' ? 0.5 : 0
    const dy = key === 'arrowup' ? -0.5 : key === 'arrowdown' ? 0.5 : 0
    
    cells.forEach(cell => {
      if (cell.isNode()) {
        const position = cell.getPosition()
        cell.setPosition(position.x + dx, position.y + dy)
      }
    })
    
    return false
  })
}

const changeBackgroundColor = (newVal)=> {
  graphContainerBackgroundStyle.value = newVal
}

provide('getCurNode', () => {
  return selectedNode.value
})

onMounted(()=>{
  
})

const clearDeviceNodes = () => {
  for (const node of graph.value.getNodes()){
    node.setData({ ... node.getData(), changeState: 'NODE_UNMOUNT' })
  } 
}

onBeforeUnmount(() => {
  clearDeviceNodes()
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container {
  width: 100%;
  height: calc(100vh - 50px);
  padding:0px;
}
.app-container-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

.flow {
  /* width: 100vw; */
  width: 100%;
  height: calc(100vh - 50px);
}
.flow-tags {
  width: 100%;
  height: calc(100vh - 84px);
  padding:0px;
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
}

#stencil {
  width: 290px;
  height: 100%;
  position: relative;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.panel {
  width: calc(100% - 580px);
  height: 100%;
}

.panel .toolbar {
  width: calc(100% - 618px);
  height: 38px;
  padding-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  //background-color: #f7f9fb80;
  //border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  position:absolute;
  z-index: 900;
}
.toolbar-group {
  border-radius: 20px;
  background-color: #FFFFFF;
  padding: 0px 20px;
}
.panel #container {
  width: 100%;
  height: calc(100% - 38px);
  //background: linear-gradient(to bottom, #062D5B, #2799C9 80%, #027DB2 100%);
  background-size: cover;
}

.config {
  width: 360px;
  height: 100%;
  padding: 0px 0px;
  border-left: 1px solid rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  flex: 0 0 auto;
  
  :deep(.el-tabs__content) {
      overflow-y: auto;
    }
}

.node_icon {
  color: darkcyan;
}
</style>
