import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/depotCategory',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/depotCategory/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/depotCategory',
    method: 'put',
    data
  })
}

export function get(params) {
  return request({
    url: 'api/depotCategory',
    method: 'get',
    params
  })
}

export function getTree(params) {
  return request({
    url: 'api/depotCategory/tree',
    method: 'get',
    params
  })
}

export default { add, edit, del, get, getTree }
