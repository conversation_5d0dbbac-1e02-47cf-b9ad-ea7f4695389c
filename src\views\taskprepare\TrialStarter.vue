<template>
  <div class="main-form-container">
    <div class="main-form-body">
      <el-form ref="formMain" :model="form" :rules="rules" size="small" label-position="left" label-width="80">
        <el-form-item label="样本名称" prop="sample.name">
          <el-select
            v-model="form.sample.name"
            filterable
            remote
            reserve-keyword
            allow-create
            placeholder="请输入样本名称"
            :remote-method="onSampleSelect"
            :loading="sampleLoading"
            style="width: 80%"
          >
            <el-option
              v-for="item in sampleOptions"
              :key="item.name"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检测流程" prop="procedure.id">
          <el-radio-group v-model="form.procedure.id" class="checked-radio" @input="procedureSelectChange" style="width: 80%">
            <el-radio
              v-for="item in state.procedureOptions"
              :key="item.value"
              :label="item.value"
              border
            >{{ item.label }}</el-radio>
            <el-button>新建流程</el-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="message-container"><span :class="'span-message '+ messageClass "> {{ message }} </span></div>
    <div class="main-form-footer">
      <el-button size="medium" type="text" @click="cancelAnalyse">取 消</el-button>
      <el-button
        size="medium"
        type="primary"
        icon="el-icon-video-play"
        :disabled="inputDisabled"
        @click="handleAnalyseStart"
      >
        {{ $t('提交任务') }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { debounce } from '@/utils'
import crudTaskPrepare from '@/api/taskPrepare'
import { queryProceduresForSelection } from '@/api/procedure'
import { querySamplesForSelection } from '@/api/sample'

const store = useStore()

// 响应式状态
const formMain = ref(null)
const form = reactive({
  id: null,
  procedure: { id: null, name: null },
  sample: { id: null, name: null },
  // ... other fields
})

const state = reactive({
  sampleLoading: false,
  sampleOptions: [],
  procedureOptions: [{ label: '新建流程', value: 0 }],
  inputDisabled: false,
  message: ' ',
  messageClass: '',
  taskPrepareView: { mode: 'TRIAL', taskPrepareDtos: [] }
})

// 计算属性
const backgroundStyle = computed(() => ({
  backgroundImage: `url(${require('@/assets/icons/svg/status-btn-bg.svg')})`
}))

const rules = reactive({
  'sample.name': [
    { required: true, message: '样品名称不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  'procedure.id': [
    { required: true, message: '请选择一个检测流程', trigger: 'change' }
  ]
})

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadSamples(),
    loadProcedures(),
    getCurrentTaskPrepareView()
  ])
})

// 方法
const loadSamples = async (keyword) => {
  state.sampleLoading = true
  try {
    const res = await querySamplesForSelection({ name: keyword })
    state.sampleOptions = res
  } finally {
    state.sampleLoading = false
  }
}

const loadProcedures = async () => {
  try {
    const res = await queryProceduresForSelection()
    // 合并默认的"新建流程"选项和获取到的流程列表
    state.procedureOptions = [
      { label: '新建流程', value: 0 },
      ...(res?.map(p => ({ label: p.name, value: p.id })) || [])
    ]
  } catch (error) {
    console.error('加载检测流程失败:', error)
    state.procedureOptions = [{ label: '新建流程', value: 0 }]
  }
}

const handleAnalyseStart = async () => {
  try {
    await formMain.value.validate()
    await doSubmitTrial()
  } catch (error) {
    console.error('Validation failed:', error)
  }
}

const procedureSelectChange = (val) => {
  console.log('procedureSelectChange, val=' + val + ', form.procedure.id=' + form.procedure.id + ', form.procedure.name=' + form.procedure.name )
}

const cancelAnalyse = async () => {
  state.message = ''
  state.inputDisabled = false
  const param = { id: state.taskPrepareView.id }

  try {
    const res = await crudTaskPrepare.cancelCurrentTaskPrepareView(param)
    store.dispatch('notify', {
      title: '撤销任务成功',
      type: 'success',
      duration: 2000
    })
    console.log(new Date()+': TaskPrepare was cancelled done.')
    await getCurrentTaskPrepareView()
  } catch (error) {
    console.log('doSubmitTrial error=' + error)
    store.dispatch('notify', {
      title: '撤销任务失败',
      type: 'error',
      duration: 2000
    })
  }
}

const onSampleSelect = async (keyword) => {
  await loadSamples(keyword)
}

const doSubmitTrial = async () => {
  form.status = 'DRAFT'
  console.log( new Date() + ': crudTaskPrepare.add before: sample.name=' + form.sample.name + ', procedure.name=' + form.procedure.name )
  try {
    const res = await crudTaskPrepare.add(form)
    state.inputDisabled = true
    state.message = '已提交，等待任务调度，请稍候 ...'
    store.dispatch('notify', {
      title: '提交等待任务成功',
      type: 'success',
      duration: 2000
    })
    console.log( new Date() + ': crudTaskPrepare.add response sample.name=' + res.sample.name + ', procedure.name=' + res.procedure.name )
    form.id = res.id
    form.taskNumber = res.taskNumber
    state.form = res
    if (!state.taskPrepareView.taskPrepareDtos) {
      state.taskPrepareView.taskPrepareDtos = []
    }
    state.taskPrepareView.taskPrepareDtos.push(res)
    state.taskPrepareView.curTaskPrepareDto = res

    setTimeout(readRf, 100)
  } catch (error) {
    console.log('doSubmitTrial error=' + error)
    store.dispatch('notify', {
      title: '提交等待任务失败',
      type: 'error',
      duration: 2000
    })
  }
}

const readRf = async () => {
  const param = state.taskPrepareView

  try {
    const res = await crudTaskPrepare.submitTagReadingStep(param)
    const param = { taskId: res.taskId }
    await getCurrentTaskPrepareView(param)
  } catch (error) {
    console.log('readRf error=' + error)
    store.dispatch('notify', {
      title: '新增标签失败',
      type: 'error',
      duration: 2000
    })
  }
}

const getCurrentTaskPrepareView = async (param) => {
  try {
    const res = await crudTaskPrepare.getCurrentTaskPrepareView({ mode: 'TRIAL' })
    state.taskPrepareView = res
    if (state.taskPrepareView.taskPrepareDtos && state.taskPrepareView.taskPrepareDtos.length > 0) {
      state.form = state.taskPrepareView.taskPrepareDtos[0]
    }
    state.message = ''
    state.messageClass = ''
    if (state.form.status === 'WAIT_TAG_INPUT') {
      state.message = '请扫描RF标签'
      state.messageClass = 'span-message-RUNNING'
    } else if (state.form.status === 'WAIT_CONVEYOR_INPUT') {
      state.message = '请放置样品至传送带'
      state.messageClass = 'span-message-RUNNING'
    } else if (state.form.status === 'PUSHED') {
      state.message = '提交完成'
      state.messageClass = ''
      state.form.id = null
    }

    console.log( new Date() + ': _this.taskPrepareView.status=' + state.taskPrepareView.status
     + ', _this.form.status=' + state.form.status + ', form.sample.name=' + state.form.sample.name + ', form.procedure.name=' + state.form.procedure.name )

    if (state.taskPrepareView.status === 'DRAFT' ||
        state.taskPrepareView.status === 'SUCCESS' ||
        state.taskPrepareView.status === 'PUSHED' ||
        state.taskPrepareView.status === 'CANCELLED') {
      // Do nothing.
      state.inputDisabled = false
    } else {
      state.inputDisabled = true

      if (state.form.status === 'READY' && state.taskPrepareView.status !== 'CONVEYOR_SUBMITTED') {
        await crudTaskPrepare.submitEntryConveyorAcceptingStep(state.taskPrepareView)
      }

      setTimeout(async () => {
        await getCurrentTaskPrepareView(param)
      }, 500)
    }
  } catch (error) {
    console.log('getCurrentTaskPrepareView error=' + error)
    state.inputDisabled = true
  }
}
</script>

<style lang="scss" scoped>
.message-container {
  width: 100%;
  padding-left: 80px;
  height: 34px;
}
.main-form-footer {
  margin-top: 10px;
  padding-left: 80px;
}
.span-message {
  height: 24px;
  font-size: 24px;
  font-weight: bold;
}
.span-message-FAILED {
  color: red;
}
.span-message-SUCCESS {
  color: green;
}
.span-message-RUNNING {
  color: #0580B4;
}

.checked-radio{

  text-align: left;
}
.checked-radio :deep( .el-radio ) {
  margin-right: 10px;
  margin-left: 0px;
  margin-bottom: 4px;
}
.checked-radio :deep( .el-radio.is-bordered+.el-radio.is-bordered ){
  margin-left: 0px;
}

.checked-radio :deep( .el-radio__input ){
  display: none;
}
.checked-radio :deep( .el-radio__label ){
  padding-left: 4px;
}

.checked-radio :deep( .el-radio.is-checked ){

      border-radius:4px ;
      position: relative;
      text-align: center;
      color: #29679A;
     // box-shadow: 0px 2px 7px 0px rgba(85, 110, 97, 0.35);
  }

  .checked-radio :deep(  .el-radio.is-checked:before ) {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      border: 10px solid #13ce66;
      border-top-right-radius: 4px;
      border-bottom-color: transparent;
      border-left-color: transparent;
  }

 .checked-radio :deep(  .el-radio.is-checked:after ) {
      content: '';
      width: 6px;
      height: 10px;
      position: absolute;
      right: 2px;
      top: 0px;
      border: 2px solid #fff;
      border-top-color: transparent;
      border-left-color: transparent;
      transform: rotate(35deg);
  }

  .span-message-RUNNING {
    position: relative;
    padding: 10px 20px;
    font-size: 24px;
    color: #fff;
    text-align: center;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
  }

  /* 动态边框效果 */
  .span-message-RUNNING::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    padding: 2px; /* 边框宽度 */
    background: linear-gradient(90deg,
      #ff0000, #00ff00, #0000ff, #ff0000); /* 渐变色条 */
    background-size: 300% 100%;
    animation: flowing 3s linear infinite;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude; /* 仅保留边框部分 */
  }

  @keyframes flowing {
    from {
      background-position: 0% 50%;
    }
    to {
      background-position: 100% 50%;
    }
  }

</style>
