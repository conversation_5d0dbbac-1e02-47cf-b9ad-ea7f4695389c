<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryFormRef" :model="query" :inline="true" size="default">
          <el-form-item label="部门名称" prop="name">
            <el-input v-model="query.name" clearable placeholder="输入部门名称搜索" style="width: 200px;" @keyup.enter="crud.toQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="enabled">
            <el-select v-model="query.enabled" clearable placeholder="状态" style="width: 90px" @change="crud.toQuery">
              <el-option v-for="item in enabledTypeOptions" :key="item.key" :label="item.display_name" :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" >
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rrOperation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <crudOperation :permission="permission" :crud="crud" />

    <!--表单组件-->
    <el-dialog align-center append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU" :model-value="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form ref="formRef" inline :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="form.name" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="部门排序" prop="deptSort">
          <el-input-number
            v-model.number="form.deptSort"
            :min="0"
            :max="999"
            controls-position="right"
            style="width: 370px;"
          />
        </el-form-item>
        <el-form-item label="顶级部门">
          <el-radio-group v-model="form.isTop" style="width: 140px">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-radio-group v-model="form.enabled">
            <el-radio
              v-for="item in dict.dept_status"
              :key="item.id"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.isTop === '0'" style="margin-bottom: 0;" label="上级部门" prop="pid">
          <el-tree-select
            v-model="form.pid"
            :data="depts"
            :props="{ label: 'name', value: 'id' }"
            style="width: 370px;"
            placeholder="选择上级部门"
            check-strictly
            :load="loadDepts"
            lazy
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table
      ref="tableRef"
      v-loading="crud.loading"
      lazy
      :load="getDeptDatas"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      :data="crud.data"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column :selectable="checkboxT" type="selection" width="55" />
      <el-table-column label="名称" prop="name" />
      <el-table-column label="排序" prop="deptSort" />
      <el-table-column label="状态" align="center" prop="enabled">
        <template #default="{ row }">
          <el-switch
            v-model="row.enabled"
            :disabled="row.id === 1"
            @change="changeEnabled(row, row.enabled)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建日期" />
      <el-table-column v-if="checkPer(['admin','dept:edit','dept:del'])" label="操作" width="130px" align="center" fixed="right">
        <template #default="{ row }">
          <udOperation
            :data="row"
            :permission="permission"
            :disabled-dle="row.id === 1"
            :crud="crud"
            msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, reactive, provide, inject, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useCrud } from '@/hooks/useCrud'
import crudDept from '@/api/system/dept'
import DateRangePicker from '@/components/DateRangePicker'
import rrOperation from '@/components/crud/RR.operation.vue'
import crudOperation from '@/components/crud/crud.operation.vue'
import udOperation from '@/components/crud/UD.operation.vue'

// 初始化表单
const defaultForm = {
  id: null,
  name: null,
  isTop: '1',
  subCount: 0,
  pid: null,
  deptSort: 999,
  enabled: 'true'
}

// 使用 crud hook
const { crud, CRUD, query, form, rules } = useCrud({
  title: '部门',
  url: 'api/dept',
  crudMethod: { ...crudDept },
  defaultForm
})
provide('crud', crud)

// refs
const formRef = ref(null)
const tableRef = ref(null)

// 状态数据
const depts = ref([])
const dict = reactive({
  dept_status: [
    { id: 1, value: 'true', label: '启用' },
    { id: 2, value: 'false', label: '禁用' }
  ]
})

// 配置数据
const permission = {
  add: ['admin', 'dept:add'],
  edit: ['admin', 'dept:edit'],
  del: ['admin', 'dept:del']
}

const enabledTypeOptions = [
  { key: 'true', display_name: '正常' },
  { key: 'false', display_name: '禁用' }
]

// 注入 checkPer 函数
const checkPer = inject('checkPer')

// 方法
const getDeptDatas = async (tree, treeNode, resolve) => {
  const params = { pid: tree.id }
  try {
    const res = await crudDept.getDepts(params)
    resolve(res.content)
  } catch (error) {
    console.error(error)
    resolve([])
  }
}

const loadDepts = async ({ node, resolve }) => {
  const params = { enabled: true, pid: node.data?.id || null }
  try {
    const res = await crudDept.getDepts(params)
    const data = res.content.map(item => {
      if (item.hasChildren) {
        item.children = null
      }
      return item
    })
    resolve(data)
  } catch (error) {
    console.error(error)
    resolve([])
  }
}

const getSupDepts = async (id) => {
  try {
    const res = await crudDept.getDeptSuperior(id)
    const data = res.content
    buildDepts(data)
    depts.value = data
  } catch (error) {
    console.error(error)
  }
}

const buildDepts = (deptList) => {
  deptList.forEach(data => {
    if (data.children) {
      buildDepts(data.children)
    }
    if (data.hasChildren && !data.children) {
      data.children = null
    }
  })
}

const getDepts = async () => {
  try {
    const res = await crudDept.getDepts({ enabled: true })
    depts.value = res.content.map(obj => {
      if (obj.hasChildren) {
        obj.children = null
      }
      return obj
    })
  } catch (error) {
    console.error(error)
  }
}

const changeEnabled = (data, val) => {
  ElMessageBox.confirm(
    `此操作将 "${dict.dept_status.find(item => item.value === String(val)).label}" ${data.name}部门, 是否继续？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await crudDept.edit(data)
      ElMessage.success(`${dict.dept_status.find(item => item.value === String(val)).label}成功`)
    } catch (error) {
      data.enabled = !data.enabled
      console.error(error)
    }
  }).catch(() => {
    data.enabled = !data.enabled
  })
}

const checkboxT = (row) => {
  return row.id !== 1
}

// CRUD 钩子
crud.hooks[CRUD.HOOK.afterToCU] = (crud, form) => {
  if (form.pid !== null) {
    form.isTop = '0'
  } else if (form.id !== null) {
    form.isTop = '1'
  }
  form.enabled = `${form.enabled}`
  if (form.id != null) {
    getSupDepts(form.id)
  } else {
    getDepts()
  }
}

crud.hooks[CRUD.HOOK.afterValidateCU] = () => {
  if (form.pid !== null && form.pid === form.id) {
    ElMessage.warning('上级部门不能为空')
    return false
  }
  if (form.isTop === '1') {
    form.pid = null
  }
  return true
}

// 生命周期
onMounted(() => {
  crud.refresh()
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .vue-treeselect__control,::v-deep .vue-treeselect__placeholder,::v-deep .vue-treeselect__single-value {
    height: 30px;
    line-height: 30px;
  }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
