<template>
<div class="board-container" :style="{width: board.width+'px', height: board.height+2+'px'}">
  <div
      v-for="(marker, index) in markers"
      :key="index"
      :class="['marker', 'marker-circle', 'marker-'+marker.state ]"
      :title="marker.tooltip"
      :style="{ top: marker.y + 'px', left: marker.x + 'px', width: marker.width + 'px', height: marker.height + 'px', 'line-height': marker.height+'px'}"
      @click="onMarkerClick(marker, index)"
      >
    {{ index + 1 }}
    </div>
</div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed, inject, defineModel, nextTick, defineEmits } from 'vue'

/**
 * board: {
 *   width: 84,
 *   height: 40,
 *   rows: 2,
 *   columns: 4,
 *   positions: [],
 *   selectedPositions: []
 * }
 */
const props = defineProps({
  board: {
    type: Object,
    default: {
      width: 84,
      height: 40,
      rows: 2,
      columns: 4,
      positions: [],
      selectedPositions: []
    }
  },
  selectOne: {
    type: Boolean,
    default: true
  }
})

const markers = reactive([
  {
    x: 13,y: 13,width: 25,height: 100,shape: 'circle',state: 'empty',tooltip: 'Position 1'
  },
  {
    x: 38,y: 13,width: 25,height: 100,shape: 'circle',state: 'empty',tooltip: 'Position 1'
  },
  {
    x: 63,y: 13,width: 25,height: 100,shape: 'circle',state: 'empty',tooltip: 'Position 1'
  },
  {
    x: 88,y: 13,width: 25,height: 100,shape: 'circle',state: 'empty',tooltip: 'Position 1'
  },
  {
    x: 13,y: 38,width: 25,height: 100,shape: 'circle',state: 'empty',tooltip: 'Position 1'
  },
  {
    x: 38,y: 38,width: 25,height: 100,shape: 'circle',state: 'empty',tooltip: 'Position 1'
  },
  {
    x: 63,y: 38,width: 25,height: 100,shape: 'circle',state: 'empty',tooltip: 'Position 1'
  },
  {
    x: 88,y: 38,width: 25,height: 100,shape: 'circle',state: 'empty',tooltip: 'Position 1'
  }
])

const emit = defineEmits(['update:board','update:selectedPositions'])

const onMarkerClick = (marker, index) => {
  if (props.positions && props.positions.includes(index)) {
    marker.state = 'selected'
    return
  }
  if (props.selectOne){
    markers.forEach((m, idx) => {
      if (m.state === 'selected') {
        if (props.board.positions && props.board.positions.includes(idx)) {
          m.state = 'hold'
        } else {
          m.state = 'empty'
        }
      }
    })
    marker.state = 'selected'
    props.board.selectedPositions[0] = index
    emit('update:selectedPositions', props.board.selectedPositions)
    return
  }
  if (marker.state === 'empty') {
    marker.state = 'selected'
    emit('update:board', {
      ...props.board,
      selectedPositions: [...props.board.selectedPositions, index]
    })
  } else {
    marker.state = 'empty'
    emit('update:board', {
      ...props.board,
      selectedPositions: props.board.selectedPositions.filter((p) => p !== index)
    })
  }
}

watch(() => props.board, (newBoard, oldBoard) => {
  if (newBoard && newBoard.rows && newBoard.columns ) {
    markers.splice(0, markers.length)
    const circleWidth = Math.min( newBoard.width / newBoard.columns, newBoard.height / newBoard.rows ) - 2
    for (let i = 0; i < newBoard.rows; i++) {
      for (let j = 0; j < newBoard.columns; j++) {
        markers.push({
          x: (newBoard.width /newBoard.columns) * (j + 1) - newBoard.width /newBoard.columns/2,
          y: (newBoard.height /newBoard.rows) * (i + 1) - newBoard.height /newBoard.rows/2,
          width: circleWidth,
          height: circleWidth,
          shape: 'circle',
          state: 'empty',
          tooltip: 'Position ' + (i * newBoard.columns + j + 1)
        })
      }
    }
    markers.forEach((marker, index) => {
      if (newBoard.selectedPositions && newBoard.selectedPositions.includes(index)) {
        marker.state = 'selected'
      }else if (newBoard.positions && newBoard.positions.includes(index)) {
        marker.state = 'hold'
      } else {
        marker.state = 'empty'
      }
    })
  }
}, { deep:true, immediate: true })

</script>

<style scoped>
.board-container {
  position: relative;
  width: 84px;
  height: 40px;
  background-color: #dddddd;
  border: 1px solid #ccc;
  line-height: 100%;
  margin: 4px 0px 4px 0px;
}

.marker {
  position: absolute;
  width: 20px;
  text-align: center;
  
  cursor: pointer;
  border: 1px solid rgb(173, 173, 173); /* Optional for better visibility */
}
.marker-circle {
  aspect-ratio: 1 / 1;

  transform: translate(-50%, -50%);
  border-radius: 50%;
}
.marker-rect {
  border-radius: 0;
}
.marker-empty {
  background-color: rgba(255, 255, 255, 0.733);
}
.marker-hold {
  background-color: rgba(185, 185, 185, 0.733);
}
.marker-selected {
  background-color: rgba(11, 66, 217, 0.733);
}

</style>
