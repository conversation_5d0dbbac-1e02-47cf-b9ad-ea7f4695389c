<template>
  <div class="robot-status-container">
    <div v-for="(motion,index) in motions" class="inner">
      <div class="robot-status-wrapper">
        <el-image
          :src="imageAccessUrl + 'robot-2024060710153230.svg'"
          fit="contain"
          class="img"
        />
        <div class="right">
          <div class="status">
            <span class="label">状态:</span>
            <span class="status-value">
              <el-tag :type="motion.status === 'IDLE' ? 'success' : 'danger'" color="#158CBE40" :title="getStatusTip(motion)">
                {{ motion.status }}
              </el-tag>
            </span>
          </div>
          <div class="status">
            <span class="label">
              速度:
            </span>
            <span class="status-value">
              <el-slider v-model="motion.speedFactor" :format-tooltip="formatSpeedTooltip(motion)" @change="changeSpeed(motion)" />
            </span>
          </div>
        </div>
      </div>

      <div class="robot-status-wrapper">
        <div class="axis-label" :title="motion.name">{{ motion.name }}</div>
        <div class="axis-value">
            <span class="axis-item">X: {{ motion.xpos }}</span>
            <span class="axis-item">Y: {{ motion.ypos }}</span>
            <span class="axis-item">Z: {{ motion.zpos }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import crudMotion from '@/api/motion'

// 使用store
const store = useStore()

// 从store中获取数据
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

const getStatusTip = (motion) => {
  if (motion.errorCode && motion.errorCode !== '0') {
    return motion.errorMsg + '(' + motion.errorCode + ')'
  }
  return null
}

const motions = computed(()=>store.state.motion.motions)

// 定时器
let timer = null

// 格式化速度提示
const formatSpeedTooltip =  (motion) => {
  
  return '速度值(mm/sec):' + motion.speed + ', 速度控制因子:' + motion.speedFactor + '/100'
}

// 更改速度
const changeSpeed = (motion) => {
  debugger
  crudMotion.changeSpeed(motion)
    .then(res => {
      transferData(res)
    })
    .catch((err) => {
      console.log('changeSpeed error: ' + err)
    })
}

// 转换数据
const transferData = (res) => {
  res.xpos = parseFloat(res.xpos).toFixed(2)
  res.ypos = parseFloat(res.ypos).toFixed(2)
  res.zpos = parseFloat(res.zpos).toFixed(2)
  // 注意：在Vue 3中，我们不能直接修改computed返回的值
  // 这里应该通过store的mutation来更新motion
  store.dispatch('updateMotionInfo', res)
}


// 组件挂载时
onMounted(() => {
  store.dispatch('getMotionInfos')
})

// 组件卸载前
onBeforeUnmount(() => {

})
</script>

<style lang="scss" scoped>
.flow {
  width: 100%;
  height: 100vh;
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
}

.control-board-container {
  width: 290px;
  height: 100%;
  z-index: 2000;
  border: 1px solid #238DBD;
  box-sizing: border-box;
  background-color: #158CBE40; /* 半透明底色，蓝色 */
  border-radius: 15px; /* 圆角矩形 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* 边框阴影 */
  padding: 20px; /* 内边距 */
  color: white; /* 文本颜色 */
  margin: 60px 20px 20px 10px;
}

.robot-status-container {
  margin-bottom: 20px;
  .inner {
    margin-bottom: 4px;
  }
}

.robot-status-wrapper {
  display: flex;
  align-items: center;
  .img {
    width: 60px;
  }
  .left {
    width: 60px;
    display: flex;
    align-items: center;
    font-size: 14px;
  }
  .right .status {
    flex: 1;
    padding-left: 10px;
    padding-top: 6px;
    display: flex;
    width:190px;
    align-items: center;
    font-size: 14px;
    .label {
      color: #f9f9f9;
      font-size: 14px;
      width: 60px;
    }
    .status-value {
      padding-left: 4px;
      flex: 1 1 auto;
    }
  }

  .axis-label {
    color: #f9f9f9;
    font-size: 14px;
    width: 70px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

  }
 .axis-value {
    padding-left: 4px;
    color: #f9f9f9;
    font-size: 12px;
    flex: 1 1 auto;
    .axis-item {
      margin-right: 4px;
      flex: 1 1 auto;
    }
  }
  
}

.axis-container {
  margin-left: 20px;
  width: 200px;
  height: 60px;
  background-size: 40px 40px; /* 背景图片适应按钮 */
  background-repeat: no-repeat;
  background-position: left; /* 背景图片居中 */
  display: flex;
  flex-direction: column;
  
  .z-axis {
    padding-left: 26px;
  }
  .y-axis {
    padding-left: 50px;
    padding-top: 6px;
  }
  .x-axis {
    padding-left: 6px;
    padding-top: 10px;
  }
}

.inner-container {
  background-color: #127FAC42;
  margin-bottom: 10px;
  .title {
    padding: 10px;
  }
}

#alert-board {
  width: 100%;
  height: 160px;
}

.status-detail {
  color: red;
  font-size: small;
  padding-left: 20px;
  padding-top: 8px;
}
</style>
