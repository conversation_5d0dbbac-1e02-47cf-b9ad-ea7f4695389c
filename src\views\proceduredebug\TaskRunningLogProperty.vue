<template>
  <div class="log-property-container">
    <TaskLogViewer :data-filter="dataFilter"/>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onCreated } from 'vue'
import { Node } from '@antv/x6'
import TaskLogViewer from '@/views/components/TaskLogViewer.vue'

// Props 定义
const props = defineProps({
  curNode: {
    type: Object as () => Node,
    default: () => ({})
  },
  dataFilter: {
    type: Object,
    default: () => ({
      nodeType: 'TASK',
      id: 0
    })
  }
})
// 默认表单数据
const defaultForm = {
  id: null,
  name: null,
  description: null,
  commandId: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

// 响应式状态
const form = reactive({ ...defaultForm })
const initing = ref(false)

const commandsOptions = ref([
  { label: '系统等待', value: 1 }
])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

// 监听 curNode 变化
watch(() => props.curNode, (newVal) => {
  if (!newVal) return
  
  const nodeData = newVal.getData()
  if (nodeData?.id) {
    props.dataFilter.id = nodeData.id
  }
}, { immediate: true })

// 方法定义
const init = () => {
  initing.value = true
}

const transformNodeToFormData = () => {
  if (!props.curNode) return
  
  const nodeData = props.curNode.getData()
  form.name = nodeData.name
  form.description = nodeData.description
}

const transformFormDataToNode = () => {
  if (!props.curNode) return
  
  const nodeData = props.curNode.getData()
  nodeData.name = form.name
  nodeData.description = form.description
  const changeState = 'changed from property'
  props.curNode.setData({ ...nodeData, changeState })
}

// 暴露方法（如果需要）
defineExpose({
  init,
  transformNodeToFormData,
  transformFormDataToNode
})
</script>

<style lang="scss" scoped>

</style>
