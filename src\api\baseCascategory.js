import request from '@/utils/request'

export function getCascategorys(params) {
  return request({
    url: 'api/baseCascategory',
    method: 'get',
    params
  })
}

export function getAllCascategory() {
  return request({
    url: 'api/baseCascategory/list',
    method: 'get'
  })
}

export function getCascategorySuperior(ids) {
  const data = ids.length || ids.length === 0 ? ids : Array.of(ids)
  return request({
    url: 'api/baseCascategory/superior',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: 'api/baseCascategory',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/baseCascategory/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/baseCascategory',
    method: 'put',
    data
  })
}

export function tree() {
  return request({
    url: 'api/baseCascategory/tree',
    method: 'get'
  })
}

export default { add, edit, del, getCascategorys, getCascategorySuperior, getAllCascategory, tree }
