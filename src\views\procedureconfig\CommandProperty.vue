<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="命令名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-divider content-position="left">参数设置</el-divider>
      <el-form-item label="设备指令" prop="arg1">
        <el-select
          v-model="form.commandId"
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in commandsOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="参数1" prop="arg1">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-divider content-position="left">选项</el-divider>
      <el-form-item label="执行失败动作" prop="arg1">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="重试次数" prop="arg1">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-divider content-position="left">所属设备</el-divider>

    </el-form>
  </div>
</template>

<script>
import { Node } from '@antv/x6'

const defaultForm = { id: null, name: null, description: null, commandId: null, deleteFlag: null, createBy: null, createTime: null, updateBy: null, updateTime: null }

export default {
  name: 'CommandProperty',
  props: {
    curNode: {
      default: () => {
        return {}
      },
      type: Node
    }
  },
  dicts: ['device_types'],
  data() {
    return {
      form: { ...defaultForm },
      rules: {
        name: [
          { required: true, message: '步骤名称不能为空', trigger: 'blur' }
        ]
      },

      initing: false,
      // 设备点位
      commandsOptions: [{ label:'系统等待', value:1}]

    }
  },
  computed: {

  },
  watch: {
    curNode(newVal, oldVal) {
      this.transformNodeToFormData()
      console.log(`curNode changed from ${oldVal} to ${newVal}`)
    }
  },
  created() {
    console.log('CommandProperty created..')
    this.transformNodeToFormData()

    this.$watch('form.name', (newValue, oldValue) => {
      console.log(`form.name changed from "${oldValue}" to "${newValue}"`)
      this.transformFormDataToNode()
    })

    this.$watch('form.description', (newValue, oldValue) => {
      console.log(`description changed from "${oldValue}" to "${newValue}"`)
      this.transformFormDataToNode()
    })

  },
  methods: {

    init() {
      this.initing = true
    },
    transformNodeToFormData() {
      const curNode = this.curNode
      this.form.name = curNode.getData().name
      this.form.description = curNode.getData().description
    },
    transformFormDataToNode() {
      const curNode = this.curNode
      curNode.getData().name = this.form.name
      curNode.getData().description = this.form.description
      const changeState = 'changed from property'
      curNode.setData({ ...this.curNode.getData(), changeState })
    }

  }
}
</script>

<style lang="scss">

</style>
