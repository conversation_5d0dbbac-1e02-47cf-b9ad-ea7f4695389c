<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
    </div>
  
    <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
    <crud-operation :crud="crud" :permission="permission" style="margin-left: 10px">
      <template #right>
        <el-button
          class="filter-item"
          size="small"
          :type="allRunningState ? 'danger' : 'primary'"
          :icon="SwitchButton"
          :loading="startAllLoading"
          @click="handleStartAll"
        >
          {{ allRunningState ? '一键停止' : '一键启动' }}
        </el-button>
      </template>
    </crud-operation>

      <!--表单组件-->
      <el-drawer
        :model-value="crud.status.cu > 0"
        :close-on-click-modal="false"
        :title="crud.status.title"
        width="500px"
        @close="crud.cancelCU"
      >
        <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="设备名称">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item label="设备描述">
            <el-input v-model="form.description" />
          </el-form-item>
          <el-form-item label="设备实例ID">
            <el-input v-model="form.deviceInstanceId" />
          </el-form-item>
          <el-form-item label="命令行">
            <el-input v-model="form.commandLine" />
          </el-form-item>
          <el-form-item label="该设备自定义的Java类名">
            <el-input v-model="form.javaClassName"  />
          </el-form-item>
          <el-form-item label="Java类路径">
            <el-input v-model="form.javaClasspath" type="textarea"
            resize="both"
            :rows="1" />
          </el-form-item>
          <el-form-item label="配置信息">
            <el-input v-model="form.config" type="textarea"
              resize="both"
              :rows="2" />
          </el-form-item>
          <el-form-item label="运行状态">
            <el-input v-model="form.status" readonly  />
          </el-form-item>

        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button text @click="crud.cancelCU">取消</el-button>
            <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
          </div>
        </template>
      </el-drawer>

      <!--DeviceMock List-->
      <div class="mock-container">
        <el-card v-for="(item, index) in crud.data" :key="index" class="mock-card">
          <div class="mock-header">
            <div class="mock-header-left">
              <el-image
                style="width: 54px; height: 54px"
                :src="imageAccessUrl+ item.image"
                fit="contain"
              />
            </div>
            <div class="mock-header-right">
              <div class="mock-header-title">{{ item.name }}</div>
              <div><span>状态:</span><el-button :type="item.status==='STOPPED'?'danger':'success'" @click="handleStatus(index)">{{ item.status }}</el-button></div>
            </div>

          </div>
          <el-divider content-position="left">配置</el-divider>
          <div class="config-container">
            <el-input
              v-model="item.config"
              type="textarea"
              :rows="1"
            />
          </div>
          <el-divider content-position="left">日志</el-divider>
          <div class="log-container">
            <el-input
              :ref="'input-log-'+item.id"
              v-model="item.log"
              v-scroll-to-bottom
              type="textarea"
              resize="both"
              :rows="2"
            />
          </div>
          <div class="mock-footer">
            <div>
              <InputScanner v-for="(prompt, index) in item.prompts" :cur-data="item" :prompt=" prompt " />
            </div>
            <el-button-group>
            <udOperation  
              :crud="crud"
              :data="item"
              :permission="permission"
              class="operation-buttons"
            />
          </el-button-group></div>
        </el-card>
      </div>

  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import { SwitchButton } from '@element-plus/icons-vue'
import crudDeviceMock from '@/api/deviceMock'
import { useCrud } from '@/hooks/useCrud'
import useWebSocket from '@/hooks/useWebSocket'
import CrudOperation from '@/components/crud/CRUD.operation.vue'
import udOperation from '@/components/crud/UD.operation.vue'
import InputScanner from './InputScanner.vue'
import {
  Edit,
  Delete
} from '@element-plus/icons-vue';

const store = useStore()

const allRunningState = ref(false)
const startAllLoading = ref(false)

// 初始化表单
const defaultForm = {
  id: null,
  name: null,
  description: null,
  deviceInstanceId: null,
  commandLine: null,
  javaClassName: null,
  javaClasspath: null,
  config: null,
  image: null,
  log: null,
  inputScanner: null,
  status: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}
const rules = {
  name: [
    { required: true, message: '设备名称不能为空', trigger: 'blur' }
  ]
}
const formRef = ref(null)

const permission = {
  add: ['admin', 'formula:add'],
  edit: ['admin', 'formula:edit'],
  del: ['admin', 'formula:del']
}

// 使用CRUD组合式API
const { crud, CRUD, form } = useCrud({
  title: '设备模拟',
  url: 'api/deviceMock',
  crudMethod: { ...crudDeviceMock },
  optShow: { add: true, edit: true, del: true, download: false },
  formRef,
  defaultForm
})

// 计算属性
const imageAccessUrl = computed(() => store.getters.imageAccessUrl)

// WebSocket处理
const { send: wsSend, status: wsStatus } = useWebSocket({
  url: () => {
    const wsUri = `${import.meta.env.VITE_APP_WS_API}/webSocket/deviceMockLog_${Date.now().toString(36)}`
    return wsUri
  },
  onMessage: handleWebSocketMessage,
  autoReconnect: true,
  maxReconnectAttempts: 5
})

// 初始化数据
onMounted(async () => {
  try {
    crud.refresh()
  //  allRunningState.value = checkAllDevicesStatus('RUNNING')
  } catch (error) {
    ElMessage.error('初始化数据失败')
  }
})

// 设备状态切换
const handleStatus = async (index) => {
  const item = crud.data[index]
  try {
    const res = item.status === 'STOPPED' 
      ? await crudDeviceMock.start(item)
      : await crudDeviceMock.stop(item)
    crud.data.splice(index, 1, res)
    allRunningState.value = checkAllDevicesStatus('RUNNING')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 一键启停
const handleStartAll = async () => {
  try {
    startAllLoading.value = true
    const targetStatus = !allRunningState.value
    const requests = crud.data.map(item => 
      targetStatus ? crudDeviceMock.start(item) : crudDeviceMock.stop(item)
    )
    
    const results = await Promise.all(requests)
    crud.refresh()
    allRunningState.value = targetStatus
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    startAllLoading.value = false
  }
}

// WebSocket消息处理
function handleWebSocketMessage(data) {
  try {
    const dto = JSON.parse(data.msg)
    const index = crud.data.findIndex(item => item.id === dto.id)
    if (index !== -1) {
      const newItem = { ...crud.data[index], log: dto.log, prompts: dto.prompts }
      crud.data.splice(index, 1, newItem)
      scrollDown(dto.id)
    }
  } catch (error) {
    console.error('WebSocket消息处理失败:', error)
  }
}

// 日志滚动
const logRefs = ref({})
const scrollDown = (id) => {
  nextTick(() => {
    const textarea = logRefs.value[`input-log-${id}`]?.querySelector('textarea')
    if (textarea) {
      textarea.scrollTop = textarea.scrollHeight
    }
  })
}

// 状态检查
const checkAllDevicesStatus = (expectedStatus) => {
  return crud.data.every(item => item.status === expectedStatus)
}

crud.hooks[CRUD.HOOK.afterRefresh] = () => {
  allRunningState.value = checkAllDevicesStatus('RUNNING')
}

</script>

<style lang="scss" scoped>
.mock-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  font-size: small;
}
.mock-card {
  // width: 300px;
  margin: 4px;
}
.mock-header {
  width: 100%;
  display: flex;
  flex-direction: row;
  .mock-header-right {
    flex: 1;
    .mock-header-title {
      font-weight: 600;
      line-height: 150%;
    }
    span {
      margin-right: 10px;
    }
  }
  .mock-header-left {
    width:54px;
    margin-right: 10px;
  }
}
.mock-footer{
  display: flex;
  flex-direction: row;
  align-items: end;
  margin-top: 10px;
}
.mock-footer :deep(.el-button-group) {
  margin-left: auto;
}
.mock-container :deep(.el-divider--horizontal) {
  margin-bottom: 10px;
}
.mock-container :deep(.el-card__body) {
  padding-bottom: 4px;
}
</style>
