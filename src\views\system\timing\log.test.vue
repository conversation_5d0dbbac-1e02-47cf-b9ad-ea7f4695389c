<template>
  <div class="log-test-container">
    <h1>定时任务日志组件测试</h1>
    
    <div class="test-section">
      <h2>基本功能测试</h2>
      <el-button type="primary" @click="openLogDialog">
        打开日志弹窗
      </el-button>
      
      <el-button type="success" @click="testWithRef" style="margin-left: 10px;">
        通过ref调用初始化
      </el-button>
    </div>
    
    <div class="test-section">
      <h2>状态显示</h2>
      <p>弹窗状态: {{ logVisible ? '打开' : '关闭' }}</p>
    </div>
    
    <!-- 日志组件 -->
    <TimingLog 
      ref="logRef"
      v-model="logVisible" 
      @update:modelValue="handleDialogChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TimingLog from './log.vue'

// 响应式数据
const logVisible = ref(false)
const logRef = ref(null)

// 方法
const openLogDialog = () => {
  logVisible.value = true
}

const testWithRef = () => {
  if (logRef.value) {
    logVisible.value = true
    // 使用ref调用组件方法
    logRef.value.doInit()
  }
}

const handleDialogChange = (visible) => {
  console.log('弹窗状态变化:', visible)
}
</script>

<style scoped>
.log-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.test-section h2 {
  margin-top: 0;
  color: #409eff;
}

.test-section p {
  margin: 10px 0;
  font-size: 14px;
  color: #606266;
}
</style>
