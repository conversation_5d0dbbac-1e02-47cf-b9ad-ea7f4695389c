<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" size="small" label-width="80px">
      <el-form-item label="步骤名称" prop="name">
        <el-input v-model="form.name" readonly/>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="5"
          readonly
        />
      </el-form-item>
      <el-form-item label="步骤调度模式" prop="scheduleMode">
        <el-input v-model="form.scheduleMode" readonly/>
      </el-form-item>
    </el-form>
    <el-divider content-position="left">预置条件</el-divider>
    <PredicatesEditor :cur-node="curNode" :content ="predicates" />
    <el-form-item label="条件不满足时" prop="unmatchedThen">
      <el-input v-model="form.unmatchedThen" readonly/>
    </el-form-item>
    <el-divider content-position="left">节点运行日志</el-divider>
    <TaskLogViewer :data-filter="dataFilter"/>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, inject, watch, onMounted } from 'vue'
import { Node } from '@antv/x6'
import { useDict } from '@/hooks/useDict'
import PredicatesEditor from '../procedureconfig/components/PredicatesEditor.vue'
import TaskLogViewer from '@/views/components/TaskLogViewer.vue'

// Props 定义
const props = defineProps({
  curNode: {
    type: Object as () => Node,
    default: () => ({})
  }
})

// 注入
const getDataStore = inject('getDataStore')

// 使用字典
const { dict } = useDict(['step_schedule_mode'])

// 响应式状态
const defaultForm = {
  id: null,
  name: null,
  description: null,
  scheduleMode: null,
  predicates: null,
  unmatchedThen: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const formRef = ref(null)
const form = reactive({ ...defaultForm })
const rules = {
  name: [
    { required: true, message: '步骤名称不能为空', trigger: 'blur' }
  ]
}

const initing = ref(false)
const dataStore = reactive({})

const predicates = ref(null)
const dataFilter = reactive({
                    nodeType: 'STEP',
                    id: 0
                  })
// 方法定义
const init = () => {
  initing.value = true
}

const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) {
    return dataStore
  }
  if (!Array.isArray(dataStore.methods)) {
    return null
  }
  
  for (const methodObj of dataStore.methods) {
    if (methodObj.dagNodeId === nodeId) {
      return methodObj
    }
    if (!Array.isArray(methodObj.method?.steps)) {
      continue
    }
    
    for (const stepObj of methodObj.method.steps) {
      if (stepObj.dagNodeId === nodeId) {
        return stepObj
      }
      if (!Array.isArray(stepObj.step?.actions)) {
        continue
      }
      
      for (const actionObj of stepObj.step.actions) {
        if (actionObj.dagNodeId === nodeId) {
          return actionObj
        }
        if (!Array.isArray(actionObj.commands)) {
          continue
        }
        
        for (const cmdObj of actionObj.commands) {
          if (cmdObj.dagNodeId === nodeId) {
            return cmdObj
          }
        }
      }
    }
  }
  return null
}

const transformNodeToFormData = () => {
  const curNode = props.curNode
  const stepData = getStoreObjectByNodeId(curNode.id)
  
  Object.assign(dataStore, getDataStore())
  
  const curNodeData = curNode.getData()
  form.name = curNodeData.name
  form.description = curNodeData.description
  form.scheduleMode = curNodeData.scheduleMode
  debugger
  predicates.value = curNodeData.predicates
  
  form.unmatchedThen = curNodeData.unmatchedThen
  
  dataFilter.id = curNodeData.id
}

const transformFormDataToNode = () => {
  const curNode = props.curNode
  Object.assign(dataStore, getDataStore())

  const curNodeData = curNode.getData()
  curNodeData.name = form.name
  curNodeData.description = form.description
  
  const stepData = getStoreObjectByNodeId(curNode.id)
  if (stepData) {
    if (!stepData.step) {
      stepData.step = {}
    }
    stepData.step.name = form.name
    stepData.step.description = form.description
    stepData.scheduleMode = form.scheduleMode
    stepData.unmatchedThen = form.unmatchedThen
  }

  const changeState = 'changed from property'
  curNode.setData({ ...curNodeData, changeState })
}

watch(() => props.curNode, () => {
  transformNodeToFormData()
})

watch(form, () => {
  transformFormDataToNode()
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  transformNodeToFormData()
})
</script>

<style lang="scss" scoped>
</style>
