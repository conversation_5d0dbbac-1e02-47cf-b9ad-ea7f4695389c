const plugins = ['@vue/babel-plugin-transform-vue-jsx',
                  '@babel/plugin-proposal-class-properties',
                  '@babel/plugin-proposal-optional-chaining']
// 生产环境移除console
if (process.env.NODE_ENV === 'production') {
  plugins.push('transform-remove-console')
}
module.exports = {
  plugins: plugins,
  'env': {
    'development': {
      'sourceMaps': true,
      'retainLines': true
    }
  },
  presets: [
    '@vue/app',
    '@babel/preset-env'
  ]
}
