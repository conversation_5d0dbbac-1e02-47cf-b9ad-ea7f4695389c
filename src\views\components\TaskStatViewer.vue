<template>
  <div :id="props.id" class="board-container"/>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, defineEmits } from 'vue'
import * as echarts from 'echarts'
import crudTask from '@/api/task'

// 定义 props
const props = defineProps({
  id: {
    type: String,
    default: 'task-stat-board'
  },
  theme: {
    type: String,
    default: 'dark'
  }
})

const emit = defineEmits(['onChange'])

// 响应式数据
const chart = ref(null)
const currerntProcedureName = ref('水分检测(Wet)')
const statistics = ref([
  { value: 1048, name: '待进样任务' },
  { value: 735, name: '运行中任务' },
  { value: 580, name: '执行成功数' },
  { value: 484, name: '执行失败数' }
])
// 添加定时器引用
const intervalId = ref(null)

// 初始化图表
const initTaskStatChart = () => {
  const chartDom = document.getElementById(props.id)
  chart.value = echarts.init(chartDom)
  makeOption()
}

// 初始化数据
const initData = () => {
  // 先获取一次数据
  fetchData()
  
  // 设置定时器，每5秒获取一次数据
  intervalId.value = setInterval(() => {
    fetchData()
  }, 5000)
}

// 新增获取数据的方法
const fetchData = async () => {
  await crudTask.getRunningStatistics().then(res => {
    statistics.value = res
    makeOption()
    emit('onChange', statistics.value)
  }).catch(error => {
    console.error('获取任务统计数据失败:', error)
  })
}

// 生成图表配置
const makeOption = () => {
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: 'center',
      left: 'right',
      orient: 'vertical',
      textStyle: {
        color: (props.theme === 'dark')? '#FFFFFF':'#333333'
      }
    },
    series: [
      {
        name: '任务统计',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: 4
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: statistics.value
      }
    ]
  }

  option && chart.value.setOption(option)
}

// 生命周期钩子
onMounted(() => {
  initTaskStatChart()
  initData()
})

onBeforeUnmount(() => {
  // 清除图表
  if (chart.value) {
    chart.value.dispose()
  }
  
  // 清除定时器
  if (intervalId.value) {
    clearInterval(intervalId.value)
    intervalId.value = null
  }
})
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.flow {
  /* width: 100vw; */
  width: 100%;
  height: 100vh;
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
}

.task-board-container {
  width: 390px;
  height: 100%;
  /*position: relative;*/
  display: flex;
  flex-direction: column;
  z-index: 2000;
  border: 1px solid #238DBD;
  box-sizing: border-box;
  background-color: #158CBE40; /* 半透明底色，蓝色 */
  border-radius: 15px; /* 圆角矩形 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* 边框阴影 */
  padding: 20px; /* 内边距 */
  color: white; /* 文本颜色 */
  margin: 60px 20px 20px 0px;
}

#task-stat-board {
  height: 100px;
  width: 100%;
}
.board-container {
  width: 100%;
  height: 100px;
}

</style>
