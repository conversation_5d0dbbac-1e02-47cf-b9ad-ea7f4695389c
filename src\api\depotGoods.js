import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/depotGoods',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/depotGoods/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/depotGoods',
    method: 'put',
    data
  })
}

export function queryGoodsByDepotCategoryId(params) {
  return request({
    url: 'api/depotGoods/queryGoodsByDepotCategoryId',
    method: 'get',
    params
  })
}

export default { add, edit, del, queryGoodsByDepotCategoryId }
