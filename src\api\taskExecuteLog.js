import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/taskExecuteLog',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/taskExecuteLog/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/taskExecuteLog',
    method: 'put',
    data
  })
}

export function queryLatestTaskExecuteLog(params) {
  return request({
    url: 'api/taskExecuteLog/queryLatestTaskExecuteLog',
    method: 'get',
    params
  })
}

export function queryTaskExecuteLogForNode(params) {
  return request({
    url: 'api/taskExecuteLog/queryTaskExecuteLogForNode',
    method: 'get',
    params
  })
}

export default { add, edit, del, queryLatestTaskExecuteLog, queryTaskExecuteLogForNode }
