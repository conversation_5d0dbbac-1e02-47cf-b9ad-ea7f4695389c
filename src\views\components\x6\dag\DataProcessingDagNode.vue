<template>
  <div class="data-processing-dag-node">
    <div
      :class="['main-area', isCmdProxy?'main-area-cmdproxy':'main-area-default']"
      @mouseenter="mouseEnter"
      @mouseleave="mouseLeave"
    >
      <div class="main-info">
        <!-- 节点类型icon -->
        <i
          :class="'node-logo' + nodeLogoClass "
          :style="'background-image: url('+getIconUrl()+')' "
        />

        <el-tooltip class="item" effect="dark" :content="nodeDescription" placement="bottom">
          <div class="ellipsis-row node-name">{{ nodeName }}</div>
        </el-tooltip>
      </div>

      <!-- 节点状态信息 -->
      <div class="status-action">
        <!-- 节点操作菜单 -->
        <div class="more-action-container" v-if="!isDebugging && nextNode">
          <el-dropdown trigger="hover" @command="onNewAction">
              <div>
                <i class="more-action" />
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>{{ nextNode?.name }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
        </div>
        <el-tooltip v-if="isDebugging" class="item" effect="dark" :content="dataStatus+':'+dataMessage" placement="top">
          <i :class="'status-icon'+' '+statusIconClass " />
        </el-tooltip>
      </div>
    </div>

    <!-- 添加下游节点 -->
    <NodeExpander />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, inject, nextTick } from 'vue'
import { Graph, Path, StringExt } from '@antv/x6'
import { useStore } from 'vuex'
import { ElDropdown, ElDropdownItem, ElDropdownMenu, ElTooltip } from 'element-plus'

import { useDagNode } from '@/hooks/useDagNode'
import commandIcon from '@/assets/images/command.svg'
import NodeExpander from '@/views/components/x6/dag/NodeExpander.vue'

// store
const store = useStore()
const imageAccessUrl = computed(() =>{
  const baseUrl = import.meta.env.VITE_BASE_API === '/' ? '' : import.meta.env.VITE_BASE_API
  return baseUrl + '/file/%E5%9B%BE%E7%89%87/'
})

// Props 定义
defineProps({
  type: {
    type: String,
    required: false
  },
  name: {
    type: String,
    required: false
  }
})

// 注入
const getNode = inject('getNode')

// 响应式状态
const nodeName = ref('')
const nodeType = ref('')
const nodeImage = ref(null)
const nodeDescription = ref('')
const selectedNextNodeId = ref('')
const plusActionSelected = ref(false)
const baseApi = ref(null)
//const imageAccessUrl = ref(null)
const dataStore = reactive({})

const isCmdProxy = ref(false)

const nextNode = ref(null)

// 节点类型常量
const NodeType = {
  PROCEDURE: 'PROCEDURE',
  METHOD: 'METHOD',
  STEP: 'STEP',
  ACTION: 'ACTION',
  COMMAND: 'COMMAND'
}

// 加工类型列表
const PROCESSING_TYPE_LIST = [
  { type: 'PROCEDURE', name: '新建流程' },
  { type: 'METHOD', name: '新建方法' },
  { type: 'STEP', name: '新建步骤' },
  { type: 'ACTION', name: '新建动作' },
  { type: 'COMMAND', name: '新增命令' }
]

// 节点类型图标
const NODE_TYPE_LOGO = {
  PROCEDURE: `url(${'@/assets/images/procedure.svg'})`,
  METHOD: `url(${'@/assets/images/method.svg'})`,
  STEP: `url(${'@/assets/images/step.svg'})`,
  ACTION: `url(${'@/assets/images/action.svg'})`,
  COMMAND: `url(${'@/assets/images/command.svg'})`
}

// 计算属性
const plusDropdown = computed(() => ({
  'plus-dropdown': true,
  'plus-dropdown-hide': !plusActionSelected.value,
  'plus-dropdown-visible': plusActionSelected.value
}))

// 工具函数
const getDownstreamNodePosition = (node, graph, dx = 250, dy = 100) => {
  const downstreamNodeIdList = []
  graph.getEdges().forEach((edge) => {
    const originEdge = edge.toJSON().data
    if (originEdge.source === node.id) {
      downstreamNodeIdList.push(originEdge.target)
    }
  })

  const position = node.getPosition()
  let minX = Infinity
  let maxY = -Infinity
  
  graph.getNodes().forEach((graphNode) => {
    if (downstreamNodeIdList.includes(graphNode.id)) {
      const nodePosition = graphNode.getPosition()
      if (nodePosition.x < minX) minX = nodePosition.x
      if (nodePosition.y > maxY) maxY = nodePosition.y
    }
  })

  return {
    x: minX !== Infinity ? minX : position.x + dx,
    y: maxY !== -Infinity ? maxY + dy : position.y
  }
}

const getPortsByType = (type, nodeId) => {
  switch (type) {
    case NodeType.PROCEDURE:
      return [{ id: `${nodeId}-out`, group: 'out' }]
    case NodeType.COMMAND:
      return [{ id: `${nodeId}-in`, group: 'in' }]
    default:
      return [
        { id: `${nodeId}-in`, group: 'in' },
        { id: `${nodeId}-out`, group: 'out' }
      ]
  }
}

// 计算属性方式
const iconStyle = computed(() => ({
  backgroundImage: `url(${commandIcon})`
}))

const nodeLogoClass = computed(() => {
  return ` node-logo-${nodeType.value}`
})

const nodeStatus = ref('')
const dataStatus = computed(() => {
  const node = getNode()
  return nodeStatus.value || node.getData().status
})
const dataMessage = computed(() => {
  const node = getNode()
  return node.getData().message
})

const statusIconClass = computed(() => {
  return ` status-icon-${dataStatus.value}`
})

// 是否处于调试模式
const isDebugging = computed(() => {
  const node = getNode()
  const { graph } = node.model || {}
  if (graph) {
    return graph.dataStore?.isDebugging
  }
  return false
})

const getIconUrl = () => {
  const node = getNode()
  if (node.getData().type === 'COMMAND') {
    //debugger
    return `url(${'@/assets/images/command.svg'})`
    //return imageAccessUrl.value + node.getData().layoutImage
  }else if (node.getData().type === 'ACTION') {
    
    let tmpNodeImage = nodeImage.value || node.getData().dagNodeImage
    if (tmpNodeImage){
      return imageAccessUrl.value + tmpNodeImage
    }
  }

  return NODE_TYPE_LOGO[node.getData().type]
}

const toggleSelect = () => {
  const node = getNode()
  const { graph } = node.model || {}
  showNodesRecursivelly(graph, node.id, plusActionSelected.value)
  plusActionSelected.value = !plusActionSelected.value
}

const showNodesRecursivelly = (graph, nodeId, bShow) => {
  const edges = graph.getEdges()
  edges.forEach(edge => {
    if (edge.source.cell === nodeId) {
      bShow ? edge.show() : edge.hide()
      const targetNode = graph.getCellById(edge.target.cell)
      bShow ? targetNode.show() : targetNode.hide()
      showNodesRecursivelly(graph, edge.target.cell, bShow)
    }
  })
}


const getStoreObjectByNodeId = (nodeId) => {
  if (dataStore.dagNodeId === nodeId) return dataStore
  
  for (const actionObj of dataStore.actions || []) {
    if (actionObj.dagNodeId === nodeId) return actionObj
    for (const cmdObj of actionObj.commands || []) {
      if (cmdObj.dagNodeId === nodeId) return cmdObj
    }
  }
  return null
}

const getStoreDeviceInstanceById = (id) => {
  if (!id || !dataStore.devices) return null
  return dataStore.devices.find(dev => dev.id === id)
}

const updateNodeDescription = () => {
  const node = getNode()
  const { graph } = node.model || {}
  
  nodeDescription.value = node.getData().name
  
  if (graph) {
    Object.assign(dataStore, graph.dataStore || {})
    const devIns = getStoreDeviceInstanceById(node.getData().deviceInstanceId)
    if (devIns) {
      nodeDescription.value = `${devIns.name}: ${nodeName.value}`
    }
  } else if (node.getData().deviceInstanceName) {
    nodeDescription.value = `${node.getData().deviceInstanceName}: ${nodeName.value}`
  }
}

const { createDownstream, getNextNodeTypeAndName, mouseEnter, mouseLeave } = useDagNode({node: getNode()})

const onNewAction = (value) =>{
  createDownstream(nextNode.value)
  plusActionSelected.value = false
  selectedNextNodeId.value = ''
}


// 生命周期钩子
onMounted(() => {
  const node = getNode()
  nodeName.value = node.getData().name
  nodeType.value = node.getData().type
  if (node.model?.graph) {
    nextNode.value = getNextNodeTypeAndName(node)
  }
    
  if (node.getData().commandType === 'PROXY'){
    isCmdProxy.value = true
  }
  
  updateNodeDescription()
  
  node.on('change:data', ({ cell, current }) => {
    nodeName.value = current.name
    nodeStatus.value = current.status
    nodeImage.value = current.dagNodeImage
   // dataStatus.value = current.status
  //  statusIconClass.value = ` status-icon-${dataStatus.value}`

    updateNodeDescription()
    delete cell.getData().changeState
  })
})
</script>

<style  rel="stylesheet/scss" lang="scss" scoped>

.data-processing-dag-node {
    display: flex;
    flex-direction: row;
    align-items: center;
    
  }

  .main-area {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 6px 4px;
    flex:1;
   // width: 180px;
    height: 32px;
    color: rgba(0, 0, 0, 65%);
    font-size: 12px;
    font-family: PingFangSC;
    line-height: 24px;
    background-color: #fff;
    box-shadow: 0 -1px 4px 0 rgba(209, 209, 209, 50%), 1px 1px 4px 0 rgba(217, 217, 217, 50%);
    border-radius: 2px;
    border: 1px solid transparent;
    border-left-width: 4px;
    
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    .main-info {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
      
  }
  .main-area-default, .main-area-default:hover {
    border-left-color: #67c23a;
    }
  .main-area-cmdproxy,.main-area-cmdproxy:hover {
      border-left-color: #427f24;
    }

  .main-area:hover {
    border: 1px solid rgba(0, 0, 0, 10%);
    border-left-width: 4px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    box-shadow: 0 -2px 4px 0 rgba(209, 209, 209, 50%), 2px 2px 4px 0 rgba(217, 217, 217, 50%);
  }

  .node-logo {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }

  .node-logo-COMMAND {
    background-image: url('@/assets/images/command.svg');
  }
  .node-logo-METHOD {
    background-image: url('@/assets/images/method.svg');
  }
  .node-logo-ACTION {
    background-image: url('@/assets/images/action.svg');
  }
  .node-logo-STEP {
    background-image: url('@/assets/images/step.svg');
  }
  .node-logo-PROCEDURE {
    background-image: url('@/assets/images/procedure.svg');
  }

  .node-name {
    overflow: hidden;
    display: inline-block;
    //flex:1;
    width: 150px;
    margin-left: 6px;
    color: rgba(0, 0, 0, 65%);
    font-size: 12px;
    font-family: PingFangSC;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  .status-action {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .more-action-container {
    margin-left: 12px;
    width: 15px;
    height: 100%;
    text-align: center;
    cursor: pointer;
  }

  .more-action {
    display: inline-block;
    width: 15px;
    height: 20px;
    background: none  no-repeat center center / 100% 100%;
    background-image: url('@/assets/images/more.svg');
  }
  .more-action:hover {
    background-color: #84cdf7;
  }

  .plus-dag {
    visibility: hidden;
    position: relative;
    margin-left: 4px;
    height: 32px;
  }

  .plus-action {
    position: absolute;
    top: calc(50% - 8px);
    left: 0;
    width: 16px;
    height: 16px;
    background: no-repeat center center / 100% 100%;
    cursor: pointer;
    color:#A2B1C3;
  }
  .plus-action:hover {
    color:#3471f9;
  }

  .plus-action:active,
  .plus-action-selected {
    background-image: url('https://mdn.alipayobjects.com/huamei_f4t1bn/afts/img/A*k9cnSaSmlw4AAAAAAAAAAAAADtOHAQ/original');
  }
  .plus-dropdown {
    position:absolute;
    z-index: 9998;
    padding: 2px 0;
    width: 105px;
    background-color: #fff;
    box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 5%), 0 6px 16px 0 rgba(0, 0, 0, 8%),
      0 3px 6px -4px rgba(0, 0, 0, 12%);
    border-radius: 2px;
  }
  .plus-dropdown-hide {
    visibility: hidden;
  }
  .plus-dropdown-visible {
    visibility: visible;
  }

  .x6-node-selected .main-area {
    border-color: #3471f9;
  }

  .x6-node-selected .plus-dag {
    visibility: visible;
  }

  .processing-node-menu {
    padding: 2px 0;
    width: 105px;
    background-color: #fff;
    box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 5%), 0 6px 16px 0 rgba(0, 0, 0, 8%),
      0 3px 6px -4px rgba(0, 0, 0, 12%);
    border-radius: 2px;
  }
  .processing-node-menu ul {
    margin: 0;
    padding: 0;
  }
  .processing-node-menu li {
    list-style:none;
  }

  .each-sub-menu {
    padding: 6px 12px;
    width: 100%;
  }

  .each-sub-menu:hover {
    background-color: rgba(0, 0, 0, 4%);
  }

  .each-sub-menu a {
    display: inline-block;
    width: 100%;
    height: 16px;
    font-family: PingFangSC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 65%);
  }

  .each-sub-menu span {
    margin-left: 8px;
    vertical-align: top;
  }

  .each-disabled-sub-menu a {
    cursor: not-allowed;
      color: rgba(0, 0, 0, 35%);
  }

  .node-mini-logo {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    vertical-align: top;
  }

  @keyframes running-line {
    to {
      stroke-dashoffset: -1000;
    }
  }

  .status-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: no-repeat center center / 100% 100%;
  }

  .status-icon-BREAK {
    background-image:   url('@/assets/images/icon_status_breakpoint.svg');
  }
  
  .status-icon-PAUSED {
    background-image:   url('@/assets/images/icon_status_pause.svg');
  }

  .status-icon-SUCCESS {
    background-image:   url('@/assets/images/icon_status_success.svg');
  }
  .status-icon-FAILED {
    background-image:   url('@/assets/images/icon_status_failed.svg');
  }
  .status-icon-CANCELLED {
    background-image:   url('@/assets/images/icon_status_failed.svg');
  }
  .status-icon-READY {
    background-image:   url('@/assets/images/icon_status_ready.svg');
  }
  .status-icon-IN_SCHEDULE_QUE {
    background-image:   url('@/assets/images/icon_status_waiting.svg');
  }
  .status-icon-SUSPEND {
    background-image:   url('@/assets/images/icon_status_waiting.svg');
  }
  .status-icon-SKIPPED {
    background-image:   url('@/assets/images/icon_status_skip.svg');
  }
  .status-icon-RUNNING{
  //position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid #E0E0E0;
  border-top-color:  #42A5F5;
  border-bottom-color:  #42A5F5;
  border-radius: 50%;
  animation: animation 1s linear infinite
}
@keyframes animation{
  0%{
    transform: rotate(0deg)
  }
  100%{
    transform: rotate(360deg)
  }
}

</style>
