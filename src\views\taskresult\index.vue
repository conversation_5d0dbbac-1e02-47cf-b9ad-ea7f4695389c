<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-form ref="queryForm" :model="query" :inline="true" size="default">
          <el-form-item label="检测编号">
            <el-input
              v-model="query.number"
              clearable
              placeholder="检测编号"
              style="width: 185px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="检测名称">
            <el-input
              v-model="query.name"
              clearable
              placeholder="检测名称"
              style="width: 185px;"
              @keyup.enter="crud.toQuery"
            />
          </el-form-item>
          <el-form-item label="创建时间">
            <date-range-picker v-model="query.createTime" />
          </el-form-item>
          <el-form-item>
            <rr-operation :crud="crud" />
          </el-form-item>
        </el-form>
      </div>
    </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crud-operation :permission="permission" :crud="crud" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="主键ID">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="检测编号">
            <el-input v-model="form.number" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="产品名称">
            <el-input v-model="form.name" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="产品描述">
            <el-input v-model="form.description" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="样品ID">
            <el-input v-model="form.sampleId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="生成该产品的进程实例ID">
            <el-input v-model="form.taskId" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="是否已被删除,Y/N">
            <el-input v-model="form.deleteFlag" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="form.createBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新人">
            <el-input v-model="form.updateBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="form.updateTime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="tableRef" v-loading="crud.loading" :data="crud.data" size="small" border style="width: 100%;"
        :span-method="objectSpanMethod"
        highlight-current-row
         @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="resultNumber" label="检测编号" width="120" />
        <el-table-column prop="resultName" label="检测名称" />
        <el-table-column prop="sampleTag" label="样品标签" />
        <el-table-column prop="sampleName" label="样品名称" />
        <el-table-column prop="taskMethodName" label="检测方法名" />
        <el-table-column prop="name" label="检测物" />
        <el-table-column prop="value" label="检测值" />
        <el-table-column prop="conclusion" label="结论"  width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.conclusion === 'NORMAL' ? 'success' : 'danger'"
              class="result-tag"
              disable-transitions
            >{{ row.conclusion }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="异常原因" />
        <el-table-column prop="rawValue" label="原始检测数据" align="center">
          <template #default="{ row }">
            <el-popover
            placement="left"
            width="400"
            :max-height="600"
            trigger="click">
              <VueJsonPretty 
                v-model:data="row.rawObjValue" 
                :editable="false"
                :showDoubleQuotes="true"
                :showLength="true"
              />
              <template #reference>
                <el-button size="mini" type="text">查看</el-button>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="140" />
        <el-table-column v-if="checkPer(['admin','result:edit','result:del'])" label="操作" width="150px" align="center">
          <template #default="{ row }">
            <el-button type="text" @click="downloadReport(row.id)">下载报告</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination :crud="crud" />
    
  </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
import { useCrud } from '@/hooks/useCrud'
import crudResult from '@/api/result'
import DateRangePicker from '@/components/DateRangePicker'
import RrOperation from '@/components/crud/RR.operation.vue'
import CrudOperation from '@/components/crud/crud.operation.vue'
import Pagination from '@/components/crud/Pagination.vue'
import { downloadFile, parseTime } from '@/utils/index'

import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'


// 初始化CRUD配置
const defaultForm = {
  id: null,
  number: null,
  name: null,
  description: null,
  sampleId: null,
  taskId: null,
  deleteFlag: null,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null
}

const { crud, CRUD, query, form } = useCrud({
  title: '检测结果管理',
  url: 'api/result',
  idField: 'id',
  sort: 'id,desc',
  crudMethod: { ...crudResult },
  defaultForm,
  optShow: { add: false, edit: false, del: false, reset: true, download: true }
})

// 权限配置
const permission = {
  add: ['admin', 'result:add'],
  edit: ['admin', 'result:edit'],
  del: ['admin', 'result:del']
}

// 注入权限检查方法
const checkPer = inject('checkPer')

const parseJson = (str) => {
  if (str === null || str === '') return {}
  return JSON.parse(str)
}

// 方法
const downloadReport = async (id) => {
  try {
    crud.downloadLoading = true
    const result = await crudResult.downloadReport(id)
    downloadFile(result, `${crud.title}报告`, 'pdf')
  } catch (error) {
    console.error('报告下载失败:', error)
  } finally {
    crud.downloadLoading = false
  }
}

const objectSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}) => {
  // 如下列值相同时，进行行合并: resultNumber resultName resultDescription sampleTag sampleName taskMethodName

  // 需要合并的列的属性名
  const mergeColumns = ['resultNumber', 'resultName', 'sampleTag', 'sampleName', 'taskMethodName']

  // 获取当前列的属性名
  const currentProp = column.property

  // 只对指定的列进行合并处理，selection列(columnIndex=0)和其他列不处理
  if (columnIndex === 0 || !mergeColumns.includes(currentProp)) {
    return [1, 1] // 不合并
  }

  const data = crud.data
  if (!data || data.length === 0) {
    return [1, 1]
  }

  // 计算当前行在相同组中的位置
  let rowspan = 1
  let colspan = 1

  // 向下查找相同的行
  for (let i = rowIndex + 1; i < data.length; i++) {
    // 检查所有需要合并的列是否都相同
    
    let isSameGroup = data[rowIndex][currentProp] === data[i][currentProp]
    if (currentProp === 'taskMethodName'){
      isSameGroup = (data[rowIndex][currentProp] === data[i][currentProp] && data[rowIndex]['resultNumber'] === data[i]['resultNumber'] )
    }

    if (isSameGroup) {
      rowspan++
    } else {
      break
    }
  }

  // 向上查找，如果当前行不是组的第一行，则隐藏当前单元格
  for (let i = rowIndex - 1; i >= 0; i--) {
    // 检查所有需要合并的列是否都相同
    let isSameGroup = data[rowIndex][currentProp] === data[i][currentProp]
    if (currentProp === 'taskMethodName'){
      isSameGroup = (data[rowIndex][currentProp] === data[i][currentProp] && data[rowIndex]['resultNumber'] === data[i]['resultNumber'] )
    }
    if (isSameGroup) {
      // 当前行不是组的第一行，隐藏单元格
      return [0, 0]
    } else {
      break
    }
  }

  // 当前行是组的第一行，返回合并的行数
  return [rowspan, colspan]
}


// CRUD钩子

crud.hooks[CRUD.HOOK.afterRefresh] = () => {
  const rawData = crud.data
  rawData.forEach(row => {
    row.rawObjValue = parseJson(row.rawValue)
  })
  return true
}

// 初始化
onMounted(() => {
  crud.refresh()
})
</script>

<style scoped>
.result-tag {
  margin-bottom: 2px;
  margin-left: 2px;
}
</style>
