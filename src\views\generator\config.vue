<template>
  <div class="app-container">
    <el-row :gutter="15">
      <el-col style="margin-bottom: 10px">
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="clearfix">
              <span class="role-span">字段配置：{{ tableName }}</span>
              <el-button
                :loading="genLoading"
                :icon="Promotion"
                size="default"
                style="float: right; padding: 6px 9px;"
                type="success"
                @click="toGen"
              >保存&生成</el-button>
              <el-button
                :loading="columnLoading"
                :icon="Check"
                size="default"
                style="float: right; padding: 6px 9px;margin-right: 9px"
                type="primary"
                @click="saveColumnConfig"
              >保存</el-button>
              <el-tooltip class="item" effect="dark" content="数据库中表字段变动时使用该功能" placement="top-start">
                <el-button
                  :loading="syncLoading"
                  :icon="Refresh"
                  size="default"
                  style="float: right; padding: 6px 9px;"
                  type="info"
                  @click="handleSyncData"
                >同步</el-button>
              </el-tooltip>
            </div>
          </template>
          <el-form size="default" label-width="90px">
            <el-table v-loading="loading" :data="crud.data" :max-height="tableHeight" size="default" style="width: 100%;margin-bottom: 15px">
              <el-table-column prop="columnName" label="字段名称" />
              <el-table-column prop="columnType" label="字段类型" />
              <el-table-column prop="remark" label="字段描述">
                <template #default="scope">
                  <el-input v-model="crud.data[scope.$index].remark" size="default" class="edit-input" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="必填" width="70px">
                <template #default="scope">
                  <el-checkbox v-model="crud.data[scope.$index].notNull" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="列表" width="70px">
                <template #default="scope">
                  <el-checkbox v-model="crud.data[scope.$index].listShow" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="表单" width="70px">
                <template #default="scope">
                  <el-checkbox v-model="crud.data[scope.$index].formShow" />
                </template>
              </el-table-column>
              <el-table-column label="表单类型">
                <template #default="scope">
                  <el-select v-model="crud.data[scope.$index].formType" filterable class="edit-input" clearable size="default" placeholder="请选择">
                    <el-option
                      label="文本框"
                      value="Input"
                    />
                    <el-option
                      label="文本域"
                      value="Textarea"
                    />
                    <el-option
                      label="单选框"
                      value="Radio"
                    />
                    <el-option
                      label="下拉框"
                      value="Select"
                    />
                    <el-option
                      label="日期框"
                      value="Date"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="查询方式">
                <template #default="scope">
                  <el-select v-model="crud.data[scope.$index].queryType" filterable class="edit-input" clearable size="default" placeholder="请选择">
                    <el-option
                      label="="
                      value="="
                    />
                    <el-option
                      label="!="
                      value="!="
                    />
                    <el-option
                      label=">="
                      value=">="
                    />
                    <el-option
                      label="<="
                      value="<="
                    />
                    <el-option
                      label="Like"
                      value="Like"
                    />
                    <el-option
                      label="NotNull"
                      value="NotNull"
                    />
                    <el-option
                      label="BetWeen"
                      value="BetWeen"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="日期注解">
                <template #default="scope">
                  <el-select v-model="crud.data[scope.$index].dateAnnotation" filterable class="edit-input" clearable size="default" placeholder="请选择">
                    <el-option
                      label="自动创建时间"
                      value="CreationTimestamp"
                    />
                    <el-option
                      label="自动更新时间"
                      value="UpdateTimestamp"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="关联字典">
                <template #default="scope">
                  <el-select v-model="crud.data[scope.$index].dictName" filterable class="edit-input" clearable size="default" placeholder="请选择">
                    <el-option v-for="item in dicts" :key="item.id" :label="item.remark === '' ? item.name : item.remark" :value="item.name" />
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-card>
      </el-col>
      <el-col>
        <el-card class="box-card" shadow="never">
          <template #header>
            <div class="clearfix">
              <span class="role-span">生成配置</span>
              <el-button
                :loading="configLoading"
                :icon="Check"
                size="default"
                style="float: right; padding: 6px 9px"
                type="primary"
                @click="doSubmit"
              >保存</el-button>
            </div>
          </template>
          <el-form ref="formRef" :model="form" :rules="rules" size="default" label-width="78px">
            <el-form-item label="作者名称" prop="author">
              <el-input v-model="form.author" style="width: 40%" />
              <span style="color: #C0C0C0;margin-left: 10px;">类上面的作者名称</span>
            </el-form-item>
            <el-form-item label="模块名称" prop="moduleName">
              <el-input v-model="form.moduleName" style="width: 40%" />
              <span style="color: #C0C0C0;margin-left: 10px;">模块的名称，请选择项目中已存在的模块</span>
            </el-form-item>
            <el-form-item label="至于包下" prop="pack">
              <el-input v-model="form.pack" style="width: 40%" />
              <span style="color: #C0C0C0;margin-left: 10px;">项目包的名称，生成的代码放到哪个包里面</span>
            </el-form-item>
            <el-form-item label="接口名称" prop="apiAlias">
              <el-input v-model="form.apiAlias" style="width: 40%" />
              <span style="color: #C0C0C0;margin-left: 10px;">接口的名称，用于控制器与接口文档中</span>
            </el-form-item>
            <el-form-item label="前端路径" prop="path">
              <el-input v-model="form.path" style="width: 40%" />
              <span style="color: #C0C0C0;margin-left: 10px;">输入views文件夹下的目录，不存在即创建</span>
            </el-form-item>
            <!--            <el-form-item label="接口目录">-->
            <!--              <el-input v-model="form.apiPath" style="width: 40%" />-->
            <!--              <span style="color: #C0C0C0;margin-left: 10px;">Api存放路径[src/api]，为空则自动生成路径</span>-->
            <!--            </el-form-item>-->
            <el-form-item label="去表前缀" prop="prefix">
              <el-input v-model="form.prefix" placeholder="默认不去除表前缀" style="width: 40%" />
              <span style="color: #C0C0C0;margin-left: 10px;">默认不去除表前缀，可自定义</span>
            </el-form-item>
            <el-form-item label="是否覆盖" prop="cover">
              <el-radio-group v-model="form.cover" size="default" style="width: 40%">
                <el-radio-button label="true">是</el-radio-button>
                <el-radio-button label="false">否</el-radio-button>
              </el-radio-group>
              <span style="color: #C0C0C0;margin-left: 10px;">谨防误操作，请慎重选择</span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElNotification, ElMessageBox } from 'element-plus'
import { Check, Refresh, Promotion } from '@element-plus/icons-vue'
import { update, get } from '@/api/generator/genConfig'
import { save, sync as syncData, generator } from '@/api/generator/generator'
import { getDicts } from '@/api/system/dict'
import { useCrud } from '@/hooks/useCrud'

// 路由参数
const route = useRoute()
const tableName = ref('')

// 表单引用
const formRef = ref(null)

// 状态数据
const tableHeight = ref(550)
const columnLoading = ref(false)
const configLoading = ref(false)
const syncLoading = ref(false)
const genLoading = ref(false)
const dicts = ref([])

// 表单数据
const form = reactive({
  id: null,
  tableName: '',
  author: '',
  pack: '',
  path: '',
  moduleName: '',
  cover: 'false',
  apiPath: '',
  prefix: '',
  apiAlias: null
})

// 表单验证规则
const rules = {
  author: [
    { required: true, message: '作者不能为空', trigger: 'blur' }
  ],
  pack: [
    { required: true, message: '包路径不能为空', trigger: 'blur' }
  ],
  moduleName: [
    { required: true, message: '包路径不能为空', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '前端路径不能为空', trigger: 'blur' }
  ],
  apiAlias: [
    { required: true, message: '接口名称不能为空', trigger: 'blur' }
  ],
  cover: [
    { required: true, message: '不能为空', trigger: 'blur' }
  ]
}

// 使用 crud hook
const { crud, CRUD, loading, params, init } = useCrud({
  url: 'api/generator/columns',
  beforeInit: () => {
    const url = 'api/generator/columns'
    const params = { tableName: tableName.value }
    return { url, params }
  }
})

crud.hooks[CRUD.HOOK.beforeRefresh] = () => {
  Object.assign(params, { tableName: tableName.value })
}

// 保存列配置
const saveColumnConfig = () => {
  columnLoading.value = true
  save(crud.data).then(() => {
    ElNotification({
      title: '保存成功',
      type: 'success',
      duration: 2500
    })
    columnLoading.value = false
  }).catch(err => {
    columnLoading.value = false
    console.log(err.response?.data?.message || err.message)
  })
}

// 提交表单
const doSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      configLoading.value = true
      update(form).then(res => {
        ElNotification({
          title: '保存成功',
          type: 'success',
          duration: 2500
        })
        Object.assign(form, res)
        form.cover = form.cover.toString()
        configLoading.value = false
      }).catch(err => {
        configLoading.value = false
        console.log(err.response?.data?.message || err.message)
      })
    }
  })
}

// 同步数据
const handleSyncData = () => {
  syncLoading.value = true
  syncData([tableName.value]).then(() => {
    crud.refresh()
    ElNotification({
      title: '同步成功',
      type: 'success',
      duration: 2500
    })
    syncLoading.value = false
  }).catch(() => {
    syncLoading.value = false
  })
}

// 生成代码
const toGen = () => {
  genLoading.value = true
  save(crud.data).then(() => {
    ElNotification({
      title: '保存成功',
      type: 'success',
      duration: 2500
    })
    // 生成代码
    generator(tableName.value, 0).then(() => {
      genLoading.value = false
      ElNotification({
        title: '生成成功',
        type: 'success',
        duration: 2500
      })
    }).catch(err => {
      genLoading.value = false
      console.log(err.response?.data?.message || err.message)
    })
  }).catch(err => {
    genLoading.value = false
    console.log(err.response?.data?.message || err.message)
  })
}

// 初始化
onMounted(() => {
  tableHeight.value = document.documentElement.clientHeight - 385
  tableName.value = route.params.tableName
  
  nextTick(() => {
    crud.refresh()
    get(tableName.value).then(data => {
      Object.assign(form, data)
      form.cover = form.cover.toString()
    })
    getDicts().then(data => {
      dicts.value = data
    })
  })
})
</script>

<style lang="scss">
.edit-input {
  .el-input__inner {
    border: 1px solid #e5e6e7;
  }
}
</style>

<style scoped>
:deep(.input-with-select .el-input-group__prepend) {
  background-color: #fff;
}
</style>
