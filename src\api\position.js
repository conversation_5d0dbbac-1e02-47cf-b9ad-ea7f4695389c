import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/position',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/position/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/position',
    method: 'put',
    data
  })
}

export function changePositionStatus(data) {
  return request({
    url: 'api/position/changePositionStatus',
    method: 'put',
    data
  })
}

export function movePos(data) {
  return request({
    url: 'api/position/move',
    method: 'put',
    data
  })
}

export function queryPositionNamesForSelection(params) {
  return request({
    url: 'api/position/queryPositionNamesForSelection',
    method: 'get',
    params
  })
}
export default { add, edit, del, changePositionStatus, movePos, queryPositionNamesForSelection }
